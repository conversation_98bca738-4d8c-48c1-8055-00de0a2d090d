[{"/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/index.tsx": "1", "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/App.tsx": "2", "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Best-Sellers.tsx": "3", "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Inventory.tsx": "4", "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Dashboard.tsx": "5", "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Product-Details.tsx": "6", "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Slow-Moving.tsx": "7"}, {"size": 273, "mtime": 1752275901729, "results": "8", "hashOfConfig": "9"}, {"size": 730, "mtime": 1752275393656, "results": "10", "hashOfConfig": "9"}, {"size": 40222, "mtime": 1753556030643, "results": "11", "hashOfConfig": "9"}, {"size": 29064, "mtime": 1752287327805, "results": "12", "hashOfConfig": "9"}, {"size": 13833, "mtime": 1752277492647, "results": "13", "hashOfConfig": "9"}, {"size": 84840, "mtime": 1752288659539, "results": "14", "hashOfConfig": "9"}, {"size": 34769, "mtime": 1752277434250, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2kctj5", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/index.tsx", [], [], "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/App.tsx", [], [], "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Best-Sellers.tsx", ["37"], [], "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Inventory.tsx", ["38"], [], "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Dashboard.tsx", ["39"], [], "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Product-Details.tsx", ["40", "41", "42"], [], "/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/src/Slow-Moving.tsx", ["43", "44", "45", "46", "47"], [], {"ruleId": null, "fatal": true, "severity": 2, "message": "48", "line": 420, "column": 0, "nodeType": null}, {"ruleId": "49", "severity": 1, "message": "50", "line": 233, "column": 1, "nodeType": "51", "endLine": 233, "endColumn": 114}, {"ruleId": "49", "severity": 1, "message": "50", "line": 332, "column": 1, "nodeType": "51", "endLine": 332, "endColumn": 98}, {"ruleId": "52", "severity": 1, "message": "53", "line": 183, "column": 12, "nodeType": "54", "messageId": "55", "endLine": 183, "endColumn": 22}, {"ruleId": "56", "severity": 1, "message": "57", "line": 231, "column": 4, "nodeType": "58", "endLine": 231, "endColumn": 15, "suggestions": "59"}, {"ruleId": "56", "severity": 1, "message": "60", "line": 286, "column": 4, "nodeType": "58", "endLine": 286, "endColumn": 15, "suggestions": "61"}, {"ruleId": "62", "severity": 1, "message": "63", "line": 3, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 3, "endColumn": 14}, {"ruleId": "49", "severity": 1, "message": "50", "line": 344, "column": 1, "nodeType": "51", "endLine": 344, "endColumn": 116}, {"ruleId": "49", "severity": 1, "message": "50", "line": 350, "column": 1, "nodeType": "51", "endLine": 350, "endColumn": 114}, {"ruleId": "49", "severity": 1, "message": "50", "line": 419, "column": 1, "nodeType": "51", "endLine": 419, "endColumn": 78}, {"ruleId": "49", "severity": 1, "message": "50", "line": 421, "column": 1, "nodeType": "51", "endLine": 421, "endColumn": 78}, "Parsing error: ')' expected.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'salesHistory'. Either include it or remove the dependency array.", "ArrayExpression", ["66"], "React Hook useEffect has a missing dependency: 'locations'. Either include it or remove the dependency array.", ["67"], "@typescript-eslint/no-unused-vars", "'Link' is defined but never used.", "Identifier", "unusedVar", {"desc": "68", "fix": "69"}, {"desc": "70", "fix": "71"}, "Update the dependencies array to be: [activeTab, salesHistory]", {"range": "72", "text": "73"}, "Update the dependencies array to be: [activeTab, locations]", {"range": "74", "text": "75"}, [9511, 9522], "[activeTab, salesHistory]", [10464, 10475], "[activeTab, locations]"]