{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar Cartesian = /** @class */function () {\n  function Cartesian(name) {\n    this.type = 'cartesian';\n    this._dimList = [];\n    this._axes = {};\n    this.name = name || '';\n  }\n  Cartesian.prototype.getAxis = function (dim) {\n    return this._axes[dim];\n  };\n  Cartesian.prototype.getAxes = function () {\n    return zrUtil.map(this._dimList, function (dim) {\n      return this._axes[dim];\n    }, this);\n  };\n  Cartesian.prototype.getAxesByScale = function (scaleType) {\n    scaleType = scaleType.toLowerCase();\n    return zrUtil.filter(this.getAxes(), function (axis) {\n      return axis.scale.type === scaleType;\n    });\n  };\n  Cartesian.prototype.addAxis = function (axis) {\n    var dim = axis.dim;\n    this._axes[dim] = axis;\n    this._dimList.push(dim);\n  };\n  return Cartesian;\n}();\n;\nexport default Cartesian;", "map": {"version": 3, "names": ["zrUtil", "<PERSON><PERSON><PERSON>", "name", "type", "_dimList", "_axes", "prototype", "getAxis", "dim", "getAxes", "map", "getAxesByScale", "scaleType", "toLowerCase", "filter", "axis", "scale", "addAxis", "push"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/coord/cartesian/Cartesian.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar Cartesian = /** @class */function () {\n  function Cartesian(name) {\n    this.type = 'cartesian';\n    this._dimList = [];\n    this._axes = {};\n    this.name = name || '';\n  }\n  Cartesian.prototype.getAxis = function (dim) {\n    return this._axes[dim];\n  };\n  Cartesian.prototype.getAxes = function () {\n    return zrUtil.map(this._dimList, function (dim) {\n      return this._axes[dim];\n    }, this);\n  };\n  Cartesian.prototype.getAxesByScale = function (scaleType) {\n    scaleType = scaleType.toLowerCase();\n    return zrUtil.filter(this.getAxes(), function (axis) {\n      return axis.scale.type === scaleType;\n    });\n  };\n  Cartesian.prototype.addAxis = function (axis) {\n    var dim = axis.dim;\n    this._axes[dim] = axis;\n    this._dimList.push(dim);\n  };\n  return Cartesian;\n}();\n;\nexport default Cartesian;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,IAAIC,SAAS,GAAG,aAAa,YAAY;EACvC,SAASA,SAASA,CAACC,IAAI,EAAE;IACvB,IAAI,CAACC,IAAI,GAAG,WAAW;IACvB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACH,IAAI,GAAGA,IAAI,IAAI,EAAE;EACxB;EACAD,SAAS,CAACK,SAAS,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAE;IAC3C,OAAO,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC;EACxB,CAAC;EACDP,SAAS,CAACK,SAAS,CAACG,OAAO,GAAG,YAAY;IACxC,OAAOT,MAAM,CAACU,GAAG,CAAC,IAAI,CAACN,QAAQ,EAAE,UAAUI,GAAG,EAAE;MAC9C,OAAO,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDP,SAAS,CAACK,SAAS,CAACK,cAAc,GAAG,UAAUC,SAAS,EAAE;IACxDA,SAAS,GAAGA,SAAS,CAACC,WAAW,CAAC,CAAC;IACnC,OAAOb,MAAM,CAACc,MAAM,CAAC,IAAI,CAACL,OAAO,CAAC,CAAC,EAAE,UAAUM,IAAI,EAAE;MACnD,OAAOA,IAAI,CAACC,KAAK,CAACb,IAAI,KAAKS,SAAS;IACtC,CAAC,CAAC;EACJ,CAAC;EACDX,SAAS,CAACK,SAAS,CAACW,OAAO,GAAG,UAAUF,IAAI,EAAE;IAC5C,IAAIP,GAAG,GAAGO,IAAI,CAACP,GAAG;IAClB,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC,GAAGO,IAAI;IACtB,IAAI,CAACX,QAAQ,CAACc,IAAI,CAACV,GAAG,CAAC;EACzB,CAAC;EACD,OAAOP,SAAS;AAClB,CAAC,CAAC,CAAC;AACH;AACA,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}