{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { each, createHashMap, merge, assert } from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { getAxisMainType, DATA_ZOOM_AXIS_DIMENSIONS } from './helper.js';\nimport { MULTIPLE_REFERRING, SINGLE_REFERRING } from '../../util/model.js';\nvar DataZoomAxisInfo = /** @class */function () {\n  function DataZoomAxisInfo() {\n    this.indexList = [];\n    this.indexMap = [];\n  }\n  DataZoomAxisInfo.prototype.add = function (axisCmptIdx) {\n    // Remove duplication.\n    if (!this.indexMap[axisCmptIdx]) {\n      this.indexList.push(axisCmptIdx);\n      this.indexMap[axisCmptIdx] = true;\n    }\n  };\n  return DataZoomAxisInfo;\n}();\nvar DataZoomModel = /** @class */function (_super) {\n  __extends(DataZoomModel, _super);\n  function DataZoomModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = DataZoomModel.type;\n    _this._autoThrottle = true;\n    _this._noTarget = true;\n    /**\r\n     * It is `[rangeModeForMin, rangeModeForMax]`.\r\n     * The optional values for `rangeMode`:\r\n     * + `'value'` mode: the axis extent will always be determined by\r\n     *     `dataZoom.startValue` and `dataZoom.endValue`, despite\r\n     *     how data like and how `axis.min` and `axis.max` are.\r\n     * + `'percent'` mode: `100` represents 100% of the `[dMin, dMax]`,\r\n     *     where `dMin` is `axis.min` if `axis.min` specified, otherwise `data.extent[0]`,\r\n     *     and `dMax` is `axis.max` if `axis.max` specified, otherwise `data.extent[1]`.\r\n     *     Axis extent will be determined by the result of the percent of `[dMin, dMax]`.\r\n     *\r\n     * For example, when users are using dynamic data (update data periodically via `setOption`),\r\n     * if in `'value`' mode, the window will be kept in a fixed value range despite how\r\n     * data are appended, while if in `'percent'` mode, whe window range will be changed alone with\r\n     * the appended data (suppose `axis.min` and `axis.max` are not specified).\r\n     */\n    _this._rangePropMode = ['percent', 'percent'];\n    return _this;\n  }\n  DataZoomModel.prototype.init = function (option, parentModel, ecModel) {\n    var inputRawOption = retrieveRawOption(option);\n    /**\r\n     * Suppose a \"main process\" start at the point that model prepared (that is,\r\n     * model initialized or merged or method called in `action`).\r\n     * We should keep the `main process` idempotent, that is, given a set of values\r\n     * on `option`, we get the same result.\r\n     *\r\n     * But sometimes, values on `option` will be updated for providing users\r\n     * a \"final calculated value\" (`dataZoomProcessor` will do that). Those value\r\n     * should not be the base/input of the `main process`.\r\n     *\r\n     * So in that case we should save and keep the input of the `main process`\r\n     * separately, called `settledOption`.\r\n     *\r\n     * For example, consider the case:\r\n     * (Step_1) brush zoom the grid by `toolbox.dataZoom`,\r\n     *     where the original input `option.startValue`, `option.endValue` are earsed by\r\n     *     calculated value.\r\n     * (Step)2) click the legend to hide and show a series,\r\n     *     where the new range is calculated by the earsed `startValue` and `endValue`,\r\n     *     which brings incorrect result.\r\n     */\n    this.settledOption = inputRawOption;\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._doInit(inputRawOption);\n  };\n  DataZoomModel.prototype.mergeOption = function (newOption) {\n    var inputRawOption = retrieveRawOption(newOption);\n    // FIX #2591\n    merge(this.option, newOption, true);\n    merge(this.settledOption, inputRawOption, true);\n    this._doInit(inputRawOption);\n  };\n  DataZoomModel.prototype._doInit = function (inputRawOption) {\n    var thisOption = this.option;\n    this._setDefaultThrottle(inputRawOption);\n    this._updateRangeUse(inputRawOption);\n    var settledOption = this.settledOption;\n    each([['start', 'startValue'], ['end', 'endValue']], function (names, index) {\n      // start/end has higher priority over startValue/endValue if they\n      // both set, but we should make chart.setOption({endValue: 1000})\n      // effective, rather than chart.setOption({endValue: 1000, end: null}).\n      if (this._rangePropMode[index] === 'value') {\n        thisOption[names[0]] = settledOption[names[0]] = null;\n      }\n      // Otherwise do nothing and use the merge result.\n    }, this);\n    this._resetTarget();\n  };\n  DataZoomModel.prototype._resetTarget = function () {\n    var optionOrient = this.get('orient', true);\n    var targetAxisIndexMap = this._targetAxisInfoMap = createHashMap();\n    var hasAxisSpecified = this._fillSpecifiedTargetAxis(targetAxisIndexMap);\n    if (hasAxisSpecified) {\n      this._orient = optionOrient || this._makeAutoOrientByTargetAxis();\n    } else {\n      this._orient = optionOrient || 'horizontal';\n      this._fillAutoTargetAxisByOrient(targetAxisIndexMap, this._orient);\n    }\n    this._noTarget = true;\n    targetAxisIndexMap.each(function (axisInfo) {\n      if (axisInfo.indexList.length) {\n        this._noTarget = false;\n      }\n    }, this);\n  };\n  DataZoomModel.prototype._fillSpecifiedTargetAxis = function (targetAxisIndexMap) {\n    var hasAxisSpecified = false;\n    each(DATA_ZOOM_AXIS_DIMENSIONS, function (axisDim) {\n      var refering = this.getReferringComponents(getAxisMainType(axisDim), MULTIPLE_REFERRING);\n      // When user set axisIndex as a empty array, we think that user specify axisIndex\n      // but do not want use auto mode. Because empty array may be encountered when\n      // some error occurred.\n      if (!refering.specified) {\n        return;\n      }\n      hasAxisSpecified = true;\n      var axisInfo = new DataZoomAxisInfo();\n      each(refering.models, function (axisModel) {\n        axisInfo.add(axisModel.componentIndex);\n      });\n      targetAxisIndexMap.set(axisDim, axisInfo);\n    }, this);\n    return hasAxisSpecified;\n  };\n  DataZoomModel.prototype._fillAutoTargetAxisByOrient = function (targetAxisIndexMap, orient) {\n    var ecModel = this.ecModel;\n    var needAuto = true;\n    // Find axis that parallel to dataZoom as default.\n    if (needAuto) {\n      var axisDim = orient === 'vertical' ? 'y' : 'x';\n      var axisModels = ecModel.findComponents({\n        mainType: axisDim + 'Axis'\n      });\n      setParallelAxis(axisModels, axisDim);\n    }\n    // Find axis that parallel to dataZoom as default.\n    if (needAuto) {\n      var axisModels = ecModel.findComponents({\n        mainType: 'singleAxis',\n        filter: function (axisModel) {\n          return axisModel.get('orient', true) === orient;\n        }\n      });\n      setParallelAxis(axisModels, 'single');\n    }\n    function setParallelAxis(axisModels, axisDim) {\n      // At least use the first parallel axis as the target axis.\n      var axisModel = axisModels[0];\n      if (!axisModel) {\n        return;\n      }\n      var axisInfo = new DataZoomAxisInfo();\n      axisInfo.add(axisModel.componentIndex);\n      targetAxisIndexMap.set(axisDim, axisInfo);\n      needAuto = false;\n      // Find parallel axes in the same grid.\n      if (axisDim === 'x' || axisDim === 'y') {\n        var gridModel_1 = axisModel.getReferringComponents('grid', SINGLE_REFERRING).models[0];\n        gridModel_1 && each(axisModels, function (axModel) {\n          if (axisModel.componentIndex !== axModel.componentIndex && gridModel_1 === axModel.getReferringComponents('grid', SINGLE_REFERRING).models[0]) {\n            axisInfo.add(axModel.componentIndex);\n          }\n        });\n      }\n    }\n    if (needAuto) {\n      // If no parallel axis, find the first category axis as default. (Also consider polar).\n      each(DATA_ZOOM_AXIS_DIMENSIONS, function (axisDim) {\n        if (!needAuto) {\n          return;\n        }\n        var axisModels = ecModel.findComponents({\n          mainType: getAxisMainType(axisDim),\n          filter: function (axisModel) {\n            return axisModel.get('type', true) === 'category';\n          }\n        });\n        if (axisModels[0]) {\n          var axisInfo = new DataZoomAxisInfo();\n          axisInfo.add(axisModels[0].componentIndex);\n          targetAxisIndexMap.set(axisDim, axisInfo);\n          needAuto = false;\n        }\n      }, this);\n    }\n  };\n  DataZoomModel.prototype._makeAutoOrientByTargetAxis = function () {\n    var dim;\n    // Find the first axis\n    this.eachTargetAxis(function (axisDim) {\n      !dim && (dim = axisDim);\n    }, this);\n    return dim === 'y' ? 'vertical' : 'horizontal';\n  };\n  DataZoomModel.prototype._setDefaultThrottle = function (inputRawOption) {\n    // When first time user set throttle, auto throttle ends.\n    if (inputRawOption.hasOwnProperty('throttle')) {\n      this._autoThrottle = false;\n    }\n    if (this._autoThrottle) {\n      var globalOption = this.ecModel.option;\n      this.option.throttle = globalOption.animation && globalOption.animationDurationUpdate > 0 ? 100 : 20;\n    }\n  };\n  DataZoomModel.prototype._updateRangeUse = function (inputRawOption) {\n    var rangePropMode = this._rangePropMode;\n    var rangeModeInOption = this.get('rangeMode');\n    each([['start', 'startValue'], ['end', 'endValue']], function (names, index) {\n      var percentSpecified = inputRawOption[names[0]] != null;\n      var valueSpecified = inputRawOption[names[1]] != null;\n      if (percentSpecified && !valueSpecified) {\n        rangePropMode[index] = 'percent';\n      } else if (!percentSpecified && valueSpecified) {\n        rangePropMode[index] = 'value';\n      } else if (rangeModeInOption) {\n        rangePropMode[index] = rangeModeInOption[index];\n      } else if (percentSpecified) {\n        // percentSpecified && valueSpecified\n        rangePropMode[index] = 'percent';\n      }\n      // else remain its original setting.\n    });\n  };\n  DataZoomModel.prototype.noTarget = function () {\n    return this._noTarget;\n  };\n  DataZoomModel.prototype.getFirstTargetAxisModel = function () {\n    var firstAxisModel;\n    this.eachTargetAxis(function (axisDim, axisIndex) {\n      if (firstAxisModel == null) {\n        firstAxisModel = this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n      }\n    }, this);\n    return firstAxisModel;\n  };\n  /**\r\n   * @param {Function} callback param: axisModel, dimNames, axisIndex, dataZoomModel, ecModel\r\n   */\n  DataZoomModel.prototype.eachTargetAxis = function (callback, context) {\n    this._targetAxisInfoMap.each(function (axisInfo, axisDim) {\n      each(axisInfo.indexList, function (axisIndex) {\n        callback.call(context, axisDim, axisIndex);\n      });\n    });\n  };\n  /**\r\n   * @return If not found, return null/undefined.\r\n   */\n  DataZoomModel.prototype.getAxisProxy = function (axisDim, axisIndex) {\n    var axisModel = this.getAxisModel(axisDim, axisIndex);\n    if (axisModel) {\n      return axisModel.__dzAxisProxy;\n    }\n  };\n  /**\r\n   * @return If not found, return null/undefined.\r\n   */\n  DataZoomModel.prototype.getAxisModel = function (axisDim, axisIndex) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(axisDim && axisIndex != null);\n    }\n    var axisInfo = this._targetAxisInfoMap.get(axisDim);\n    if (axisInfo && axisInfo.indexMap[axisIndex]) {\n      return this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n    }\n  };\n  /**\r\n   * If not specified, set to undefined.\r\n   */\n  DataZoomModel.prototype.setRawRange = function (opt) {\n    var thisOption = this.option;\n    var settledOption = this.settledOption;\n    each([['start', 'startValue'], ['end', 'endValue']], function (names) {\n      // Consider the pair <start, startValue>:\n      // If one has value and the other one is `null/undefined`, we both set them\n      // to `settledOption`. This strategy enables the feature to clear the original\n      // value in `settledOption` to `null/undefined`.\n      // But if both of them are `null/undefined`, we do not set them to `settledOption`\n      // and keep `settledOption` with the original value. This strategy enables users to\n      // only set <end or endValue> but not set <start or startValue> when calling\n      // `dispatchAction`.\n      // The pair <end, endValue> is treated in the same way.\n      if (opt[names[0]] != null || opt[names[1]] != null) {\n        thisOption[names[0]] = settledOption[names[0]] = opt[names[0]];\n        thisOption[names[1]] = settledOption[names[1]] = opt[names[1]];\n      }\n    }, this);\n    this._updateRangeUse(opt);\n  };\n  DataZoomModel.prototype.setCalculatedRange = function (opt) {\n    var option = this.option;\n    each(['start', 'startValue', 'end', 'endValue'], function (name) {\n      option[name] = opt[name];\n    });\n  };\n  DataZoomModel.prototype.getPercentRange = function () {\n    var axisProxy = this.findRepresentativeAxisProxy();\n    if (axisProxy) {\n      return axisProxy.getDataPercentWindow();\n    }\n  };\n  /**\r\n   * For example, chart.getModel().getComponent('dataZoom').getValueRange('y', 0);\r\n   *\r\n   * @return [startValue, endValue] value can only be '-' or finite number.\r\n   */\n  DataZoomModel.prototype.getValueRange = function (axisDim, axisIndex) {\n    if (axisDim == null && axisIndex == null) {\n      var axisProxy = this.findRepresentativeAxisProxy();\n      if (axisProxy) {\n        return axisProxy.getDataValueWindow();\n      }\n    } else {\n      return this.getAxisProxy(axisDim, axisIndex).getDataValueWindow();\n    }\n  };\n  /**\r\n   * @param axisModel If axisModel given, find axisProxy\r\n   *      corresponding to the axisModel\r\n   */\n  DataZoomModel.prototype.findRepresentativeAxisProxy = function (axisModel) {\n    if (axisModel) {\n      return axisModel.__dzAxisProxy;\n    }\n    // Find the first hosted axisProxy\n    var firstProxy;\n    var axisDimList = this._targetAxisInfoMap.keys();\n    for (var i = 0; i < axisDimList.length; i++) {\n      var axisDim = axisDimList[i];\n      var axisInfo = this._targetAxisInfoMap.get(axisDim);\n      for (var j = 0; j < axisInfo.indexList.length; j++) {\n        var proxy = this.getAxisProxy(axisDim, axisInfo.indexList[j]);\n        if (proxy.hostedBy(this)) {\n          return proxy;\n        }\n        if (!firstProxy) {\n          firstProxy = proxy;\n        }\n      }\n    }\n    // If no hosted proxy found, still need to return a proxy.\n    // This case always happens in toolbox dataZoom, where axes are all hosted by\n    // other dataZooms.\n    return firstProxy;\n  };\n  DataZoomModel.prototype.getRangePropMode = function () {\n    return this._rangePropMode.slice();\n  };\n  DataZoomModel.prototype.getOrient = function () {\n    if (process.env.NODE_ENV !== 'production') {\n      // Should not be called before initialized.\n      assert(this._orient);\n    }\n    return this._orient;\n  };\n  DataZoomModel.type = 'dataZoom';\n  DataZoomModel.dependencies = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'series', 'toolbox'];\n  DataZoomModel.defaultOption = {\n    // zlevel: 0,\n    z: 4,\n    filterMode: 'filter',\n    start: 0,\n    end: 100\n  };\n  return DataZoomModel;\n}(ComponentModel);\n/**\r\n * Retrieve those raw params from option, which will be cached separately,\r\n * because they will be overwritten by normalized/calculated values in the main\r\n * process.\r\n */\nfunction retrieveRawOption(option) {\n  var ret = {};\n  each(['start', 'end', 'startValue', 'endValue', 'throttle'], function (name) {\n    option.hasOwnProperty(name) && (ret[name] = option[name]);\n  });\n  return ret;\n}\nexport default DataZoomModel;", "map": {"version": 3, "names": ["__extends", "each", "createHashMap", "merge", "assert", "ComponentModel", "getAxisMainType", "DATA_ZOOM_AXIS_DIMENSIONS", "MULTIPLE_REFERRING", "SINGLE_REFERRING", "DataZoomAxisInfo", "indexList", "indexMap", "prototype", "add", "axisCmptIdx", "push", "DataZoomModel", "_super", "_this", "apply", "arguments", "type", "_autoThrottle", "_noTarget", "_rangePropMode", "init", "option", "parentModel", "ecModel", "inputRawOption", "retrieveRawOption", "settledOption", "mergeDefaultAndTheme", "_doInit", "mergeOption", "newOption", "thisOption", "_setDefaultThrottle", "_updateRangeUse", "names", "index", "_resetTarget", "optionOrient", "get", "targetAxisIndexMap", "_targetAxisInfoMap", "hasAxisSpecified", "_fillSpecifiedTargetAxis", "_orient", "_makeAutoOrientByTargetAxis", "_fillAutoTargetAxisByOrient", "axisInfo", "length", "axisDim", "refering", "getReferringComponents", "specified", "models", "axisModel", "componentIndex", "set", "orient", "needAuto", "axisModels", "findComponents", "mainType", "setParallelAxis", "filter", "gridModel_1", "axModel", "dim", "eachTargetAxis", "hasOwnProperty", "globalOption", "throttle", "animation", "animationDurationUpdate", "rangePropMode", "rangeModeInOption", "percentSpecified", "valueSpecified", "no<PERSON><PERSON><PERSON>", "getFirstTargetAxisModel", "firstAxisModel", "axisIndex", "getComponent", "callback", "context", "call", "getAxisProxy", "getAxisModel", "__dzAxisProxy", "process", "env", "NODE_ENV", "setRawRange", "opt", "setCalculatedRange", "name", "getPercentRange", "axisProxy", "findRepresentativeAxisProxy", "getDataPercentWindow", "getValueRange", "getDataValueWindow", "firstProxy", "axisDimList", "keys", "i", "j", "proxy", "hostedBy", "getRangePropMode", "slice", "getOrient", "dependencies", "defaultOption", "z", "filterMode", "start", "end", "ret"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/dataZoom/DataZoomModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { each, createHashMap, merge, assert } from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { getAxisMainType, DATA_ZOOM_AXIS_DIMENSIONS } from './helper.js';\nimport { MULTIPLE_REFERRING, SINGLE_REFERRING } from '../../util/model.js';\nvar DataZoomAxisInfo = /** @class */function () {\n  function DataZoomAxisInfo() {\n    this.indexList = [];\n    this.indexMap = [];\n  }\n  DataZoomAxisInfo.prototype.add = function (axisCmptIdx) {\n    // Remove duplication.\n    if (!this.indexMap[axisCmptIdx]) {\n      this.indexList.push(axisCmptIdx);\n      this.indexMap[axisCmptIdx] = true;\n    }\n  };\n  return DataZoomAxisInfo;\n}();\nvar DataZoomModel = /** @class */function (_super) {\n  __extends(DataZoomModel, _super);\n  function DataZoomModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = DataZoomModel.type;\n    _this._autoThrottle = true;\n    _this._noTarget = true;\n    /**\r\n     * It is `[rangeModeForMin, rangeModeForMax]`.\r\n     * The optional values for `rangeMode`:\r\n     * + `'value'` mode: the axis extent will always be determined by\r\n     *     `dataZoom.startValue` and `dataZoom.endValue`, despite\r\n     *     how data like and how `axis.min` and `axis.max` are.\r\n     * + `'percent'` mode: `100` represents 100% of the `[dMin, dMax]`,\r\n     *     where `dMin` is `axis.min` if `axis.min` specified, otherwise `data.extent[0]`,\r\n     *     and `dMax` is `axis.max` if `axis.max` specified, otherwise `data.extent[1]`.\r\n     *     Axis extent will be determined by the result of the percent of `[dMin, dMax]`.\r\n     *\r\n     * For example, when users are using dynamic data (update data periodically via `setOption`),\r\n     * if in `'value`' mode, the window will be kept in a fixed value range despite how\r\n     * data are appended, while if in `'percent'` mode, whe window range will be changed alone with\r\n     * the appended data (suppose `axis.min` and `axis.max` are not specified).\r\n     */\n    _this._rangePropMode = ['percent', 'percent'];\n    return _this;\n  }\n  DataZoomModel.prototype.init = function (option, parentModel, ecModel) {\n    var inputRawOption = retrieveRawOption(option);\n    /**\r\n     * Suppose a \"main process\" start at the point that model prepared (that is,\r\n     * model initialized or merged or method called in `action`).\r\n     * We should keep the `main process` idempotent, that is, given a set of values\r\n     * on `option`, we get the same result.\r\n     *\r\n     * But sometimes, values on `option` will be updated for providing users\r\n     * a \"final calculated value\" (`dataZoomProcessor` will do that). Those value\r\n     * should not be the base/input of the `main process`.\r\n     *\r\n     * So in that case we should save and keep the input of the `main process`\r\n     * separately, called `settledOption`.\r\n     *\r\n     * For example, consider the case:\r\n     * (Step_1) brush zoom the grid by `toolbox.dataZoom`,\r\n     *     where the original input `option.startValue`, `option.endValue` are earsed by\r\n     *     calculated value.\r\n     * (Step)2) click the legend to hide and show a series,\r\n     *     where the new range is calculated by the earsed `startValue` and `endValue`,\r\n     *     which brings incorrect result.\r\n     */\n    this.settledOption = inputRawOption;\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._doInit(inputRawOption);\n  };\n  DataZoomModel.prototype.mergeOption = function (newOption) {\n    var inputRawOption = retrieveRawOption(newOption);\n    // FIX #2591\n    merge(this.option, newOption, true);\n    merge(this.settledOption, inputRawOption, true);\n    this._doInit(inputRawOption);\n  };\n  DataZoomModel.prototype._doInit = function (inputRawOption) {\n    var thisOption = this.option;\n    this._setDefaultThrottle(inputRawOption);\n    this._updateRangeUse(inputRawOption);\n    var settledOption = this.settledOption;\n    each([['start', 'startValue'], ['end', 'endValue']], function (names, index) {\n      // start/end has higher priority over startValue/endValue if they\n      // both set, but we should make chart.setOption({endValue: 1000})\n      // effective, rather than chart.setOption({endValue: 1000, end: null}).\n      if (this._rangePropMode[index] === 'value') {\n        thisOption[names[0]] = settledOption[names[0]] = null;\n      }\n      // Otherwise do nothing and use the merge result.\n    }, this);\n    this._resetTarget();\n  };\n  DataZoomModel.prototype._resetTarget = function () {\n    var optionOrient = this.get('orient', true);\n    var targetAxisIndexMap = this._targetAxisInfoMap = createHashMap();\n    var hasAxisSpecified = this._fillSpecifiedTargetAxis(targetAxisIndexMap);\n    if (hasAxisSpecified) {\n      this._orient = optionOrient || this._makeAutoOrientByTargetAxis();\n    } else {\n      this._orient = optionOrient || 'horizontal';\n      this._fillAutoTargetAxisByOrient(targetAxisIndexMap, this._orient);\n    }\n    this._noTarget = true;\n    targetAxisIndexMap.each(function (axisInfo) {\n      if (axisInfo.indexList.length) {\n        this._noTarget = false;\n      }\n    }, this);\n  };\n  DataZoomModel.prototype._fillSpecifiedTargetAxis = function (targetAxisIndexMap) {\n    var hasAxisSpecified = false;\n    each(DATA_ZOOM_AXIS_DIMENSIONS, function (axisDim) {\n      var refering = this.getReferringComponents(getAxisMainType(axisDim), MULTIPLE_REFERRING);\n      // When user set axisIndex as a empty array, we think that user specify axisIndex\n      // but do not want use auto mode. Because empty array may be encountered when\n      // some error occurred.\n      if (!refering.specified) {\n        return;\n      }\n      hasAxisSpecified = true;\n      var axisInfo = new DataZoomAxisInfo();\n      each(refering.models, function (axisModel) {\n        axisInfo.add(axisModel.componentIndex);\n      });\n      targetAxisIndexMap.set(axisDim, axisInfo);\n    }, this);\n    return hasAxisSpecified;\n  };\n  DataZoomModel.prototype._fillAutoTargetAxisByOrient = function (targetAxisIndexMap, orient) {\n    var ecModel = this.ecModel;\n    var needAuto = true;\n    // Find axis that parallel to dataZoom as default.\n    if (needAuto) {\n      var axisDim = orient === 'vertical' ? 'y' : 'x';\n      var axisModels = ecModel.findComponents({\n        mainType: axisDim + 'Axis'\n      });\n      setParallelAxis(axisModels, axisDim);\n    }\n    // Find axis that parallel to dataZoom as default.\n    if (needAuto) {\n      var axisModels = ecModel.findComponents({\n        mainType: 'singleAxis',\n        filter: function (axisModel) {\n          return axisModel.get('orient', true) === orient;\n        }\n      });\n      setParallelAxis(axisModels, 'single');\n    }\n    function setParallelAxis(axisModels, axisDim) {\n      // At least use the first parallel axis as the target axis.\n      var axisModel = axisModels[0];\n      if (!axisModel) {\n        return;\n      }\n      var axisInfo = new DataZoomAxisInfo();\n      axisInfo.add(axisModel.componentIndex);\n      targetAxisIndexMap.set(axisDim, axisInfo);\n      needAuto = false;\n      // Find parallel axes in the same grid.\n      if (axisDim === 'x' || axisDim === 'y') {\n        var gridModel_1 = axisModel.getReferringComponents('grid', SINGLE_REFERRING).models[0];\n        gridModel_1 && each(axisModels, function (axModel) {\n          if (axisModel.componentIndex !== axModel.componentIndex && gridModel_1 === axModel.getReferringComponents('grid', SINGLE_REFERRING).models[0]) {\n            axisInfo.add(axModel.componentIndex);\n          }\n        });\n      }\n    }\n    if (needAuto) {\n      // If no parallel axis, find the first category axis as default. (Also consider polar).\n      each(DATA_ZOOM_AXIS_DIMENSIONS, function (axisDim) {\n        if (!needAuto) {\n          return;\n        }\n        var axisModels = ecModel.findComponents({\n          mainType: getAxisMainType(axisDim),\n          filter: function (axisModel) {\n            return axisModel.get('type', true) === 'category';\n          }\n        });\n        if (axisModels[0]) {\n          var axisInfo = new DataZoomAxisInfo();\n          axisInfo.add(axisModels[0].componentIndex);\n          targetAxisIndexMap.set(axisDim, axisInfo);\n          needAuto = false;\n        }\n      }, this);\n    }\n  };\n  DataZoomModel.prototype._makeAutoOrientByTargetAxis = function () {\n    var dim;\n    // Find the first axis\n    this.eachTargetAxis(function (axisDim) {\n      !dim && (dim = axisDim);\n    }, this);\n    return dim === 'y' ? 'vertical' : 'horizontal';\n  };\n  DataZoomModel.prototype._setDefaultThrottle = function (inputRawOption) {\n    // When first time user set throttle, auto throttle ends.\n    if (inputRawOption.hasOwnProperty('throttle')) {\n      this._autoThrottle = false;\n    }\n    if (this._autoThrottle) {\n      var globalOption = this.ecModel.option;\n      this.option.throttle = globalOption.animation && globalOption.animationDurationUpdate > 0 ? 100 : 20;\n    }\n  };\n  DataZoomModel.prototype._updateRangeUse = function (inputRawOption) {\n    var rangePropMode = this._rangePropMode;\n    var rangeModeInOption = this.get('rangeMode');\n    each([['start', 'startValue'], ['end', 'endValue']], function (names, index) {\n      var percentSpecified = inputRawOption[names[0]] != null;\n      var valueSpecified = inputRawOption[names[1]] != null;\n      if (percentSpecified && !valueSpecified) {\n        rangePropMode[index] = 'percent';\n      } else if (!percentSpecified && valueSpecified) {\n        rangePropMode[index] = 'value';\n      } else if (rangeModeInOption) {\n        rangePropMode[index] = rangeModeInOption[index];\n      } else if (percentSpecified) {\n        // percentSpecified && valueSpecified\n        rangePropMode[index] = 'percent';\n      }\n      // else remain its original setting.\n    });\n  };\n  DataZoomModel.prototype.noTarget = function () {\n    return this._noTarget;\n  };\n  DataZoomModel.prototype.getFirstTargetAxisModel = function () {\n    var firstAxisModel;\n    this.eachTargetAxis(function (axisDim, axisIndex) {\n      if (firstAxisModel == null) {\n        firstAxisModel = this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n      }\n    }, this);\n    return firstAxisModel;\n  };\n  /**\r\n   * @param {Function} callback param: axisModel, dimNames, axisIndex, dataZoomModel, ecModel\r\n   */\n  DataZoomModel.prototype.eachTargetAxis = function (callback, context) {\n    this._targetAxisInfoMap.each(function (axisInfo, axisDim) {\n      each(axisInfo.indexList, function (axisIndex) {\n        callback.call(context, axisDim, axisIndex);\n      });\n    });\n  };\n  /**\r\n   * @return If not found, return null/undefined.\r\n   */\n  DataZoomModel.prototype.getAxisProxy = function (axisDim, axisIndex) {\n    var axisModel = this.getAxisModel(axisDim, axisIndex);\n    if (axisModel) {\n      return axisModel.__dzAxisProxy;\n    }\n  };\n  /**\r\n   * @return If not found, return null/undefined.\r\n   */\n  DataZoomModel.prototype.getAxisModel = function (axisDim, axisIndex) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(axisDim && axisIndex != null);\n    }\n    var axisInfo = this._targetAxisInfoMap.get(axisDim);\n    if (axisInfo && axisInfo.indexMap[axisIndex]) {\n      return this.ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n    }\n  };\n  /**\r\n   * If not specified, set to undefined.\r\n   */\n  DataZoomModel.prototype.setRawRange = function (opt) {\n    var thisOption = this.option;\n    var settledOption = this.settledOption;\n    each([['start', 'startValue'], ['end', 'endValue']], function (names) {\n      // Consider the pair <start, startValue>:\n      // If one has value and the other one is `null/undefined`, we both set them\n      // to `settledOption`. This strategy enables the feature to clear the original\n      // value in `settledOption` to `null/undefined`.\n      // But if both of them are `null/undefined`, we do not set them to `settledOption`\n      // and keep `settledOption` with the original value. This strategy enables users to\n      // only set <end or endValue> but not set <start or startValue> when calling\n      // `dispatchAction`.\n      // The pair <end, endValue> is treated in the same way.\n      if (opt[names[0]] != null || opt[names[1]] != null) {\n        thisOption[names[0]] = settledOption[names[0]] = opt[names[0]];\n        thisOption[names[1]] = settledOption[names[1]] = opt[names[1]];\n      }\n    }, this);\n    this._updateRangeUse(opt);\n  };\n  DataZoomModel.prototype.setCalculatedRange = function (opt) {\n    var option = this.option;\n    each(['start', 'startValue', 'end', 'endValue'], function (name) {\n      option[name] = opt[name];\n    });\n  };\n  DataZoomModel.prototype.getPercentRange = function () {\n    var axisProxy = this.findRepresentativeAxisProxy();\n    if (axisProxy) {\n      return axisProxy.getDataPercentWindow();\n    }\n  };\n  /**\r\n   * For example, chart.getModel().getComponent('dataZoom').getValueRange('y', 0);\r\n   *\r\n   * @return [startValue, endValue] value can only be '-' or finite number.\r\n   */\n  DataZoomModel.prototype.getValueRange = function (axisDim, axisIndex) {\n    if (axisDim == null && axisIndex == null) {\n      var axisProxy = this.findRepresentativeAxisProxy();\n      if (axisProxy) {\n        return axisProxy.getDataValueWindow();\n      }\n    } else {\n      return this.getAxisProxy(axisDim, axisIndex).getDataValueWindow();\n    }\n  };\n  /**\r\n   * @param axisModel If axisModel given, find axisProxy\r\n   *      corresponding to the axisModel\r\n   */\n  DataZoomModel.prototype.findRepresentativeAxisProxy = function (axisModel) {\n    if (axisModel) {\n      return axisModel.__dzAxisProxy;\n    }\n    // Find the first hosted axisProxy\n    var firstProxy;\n    var axisDimList = this._targetAxisInfoMap.keys();\n    for (var i = 0; i < axisDimList.length; i++) {\n      var axisDim = axisDimList[i];\n      var axisInfo = this._targetAxisInfoMap.get(axisDim);\n      for (var j = 0; j < axisInfo.indexList.length; j++) {\n        var proxy = this.getAxisProxy(axisDim, axisInfo.indexList[j]);\n        if (proxy.hostedBy(this)) {\n          return proxy;\n        }\n        if (!firstProxy) {\n          firstProxy = proxy;\n        }\n      }\n    }\n    // If no hosted proxy found, still need to return a proxy.\n    // This case always happens in toolbox dataZoom, where axes are all hosted by\n    // other dataZooms.\n    return firstProxy;\n  };\n  DataZoomModel.prototype.getRangePropMode = function () {\n    return this._rangePropMode.slice();\n  };\n  DataZoomModel.prototype.getOrient = function () {\n    if (process.env.NODE_ENV !== 'production') {\n      // Should not be called before initialized.\n      assert(this._orient);\n    }\n    return this._orient;\n  };\n  DataZoomModel.type = 'dataZoom';\n  DataZoomModel.dependencies = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'series', 'toolbox'];\n  DataZoomModel.defaultOption = {\n    // zlevel: 0,\n    z: 4,\n    filterMode: 'filter',\n    start: 0,\n    end: 100\n  };\n  return DataZoomModel;\n}(ComponentModel);\n/**\r\n * Retrieve those raw params from option, which will be cached separately,\r\n * because they will be overwritten by normalized/calculated values in the main\r\n * process.\r\n */\nfunction retrieveRawOption(option) {\n  var ret = {};\n  each(['start', 'end', 'startValue', 'endValue', 'throttle'], function (name) {\n    option.hasOwnProperty(name) && (ret[name] = option[name]);\n  });\n  return ret;\n}\nexport default DataZoomModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,EAAEC,aAAa,EAAEC,KAAK,EAAEC,MAAM,QAAQ,0BAA0B;AAC7E,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,eAAe,EAAEC,yBAAyB,QAAQ,aAAa;AACxE,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,qBAAqB;AAC1E,IAAIC,gBAAgB,GAAG,aAAa,YAAY;EAC9C,SAASA,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;EACpB;EACAF,gBAAgB,CAACG,SAAS,CAACC,GAAG,GAAG,UAAUC,WAAW,EAAE;IACtD;IACA,IAAI,CAAC,IAAI,CAACH,QAAQ,CAACG,WAAW,CAAC,EAAE;MAC/B,IAAI,CAACJ,SAAS,CAACK,IAAI,CAACD,WAAW,CAAC;MAChC,IAAI,CAACH,QAAQ,CAACG,WAAW,CAAC,GAAG,IAAI;IACnC;EACF,CAAC;EACD,OAAOL,gBAAgB;AACzB,CAAC,CAAC,CAAC;AACH,IAAIO,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDlB,SAAS,CAACiB,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/BH,KAAK,CAACI,aAAa,GAAG,IAAI;IAC1BJ,KAAK,CAACK,SAAS,GAAG,IAAI;IACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIL,KAAK,CAACM,cAAc,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;IAC7C,OAAON,KAAK;EACd;EACAF,aAAa,CAACJ,SAAS,CAACa,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACrE,IAAIC,cAAc,GAAGC,iBAAiB,CAACJ,MAAM,CAAC;IAC9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACK,aAAa,GAAGF,cAAc;IACnC,IAAI,CAACG,oBAAoB,CAACN,MAAM,EAAEE,OAAO,CAAC;IAC1C,IAAI,CAACK,OAAO,CAACJ,cAAc,CAAC;EAC9B,CAAC;EACDb,aAAa,CAACJ,SAAS,CAACsB,WAAW,GAAG,UAAUC,SAAS,EAAE;IACzD,IAAIN,cAAc,GAAGC,iBAAiB,CAACK,SAAS,CAAC;IACjD;IACAjC,KAAK,CAAC,IAAI,CAACwB,MAAM,EAAES,SAAS,EAAE,IAAI,CAAC;IACnCjC,KAAK,CAAC,IAAI,CAAC6B,aAAa,EAAEF,cAAc,EAAE,IAAI,CAAC;IAC/C,IAAI,CAACI,OAAO,CAACJ,cAAc,CAAC;EAC9B,CAAC;EACDb,aAAa,CAACJ,SAAS,CAACqB,OAAO,GAAG,UAAUJ,cAAc,EAAE;IAC1D,IAAIO,UAAU,GAAG,IAAI,CAACV,MAAM;IAC5B,IAAI,CAACW,mBAAmB,CAACR,cAAc,CAAC;IACxC,IAAI,CAACS,eAAe,CAACT,cAAc,CAAC;IACpC,IAAIE,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC/B,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE,UAAUuC,KAAK,EAAEC,KAAK,EAAE;MAC3E;MACA;MACA;MACA,IAAI,IAAI,CAAChB,cAAc,CAACgB,KAAK,CAAC,KAAK,OAAO,EAAE;QAC1CJ,UAAU,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGR,aAAa,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACvD;MACA;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAACE,YAAY,CAAC,CAAC;EACrB,CAAC;EACDzB,aAAa,CAACJ,SAAS,CAAC6B,YAAY,GAAG,YAAY;IACjD,IAAIC,YAAY,GAAG,IAAI,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC3C,IAAIC,kBAAkB,GAAG,IAAI,CAACC,kBAAkB,GAAG5C,aAAa,CAAC,CAAC;IAClE,IAAI6C,gBAAgB,GAAG,IAAI,CAACC,wBAAwB,CAACH,kBAAkB,CAAC;IACxE,IAAIE,gBAAgB,EAAE;MACpB,IAAI,CAACE,OAAO,GAAGN,YAAY,IAAI,IAAI,CAACO,2BAA2B,CAAC,CAAC;IACnE,CAAC,MAAM;MACL,IAAI,CAACD,OAAO,GAAGN,YAAY,IAAI,YAAY;MAC3C,IAAI,CAACQ,2BAA2B,CAACN,kBAAkB,EAAE,IAAI,CAACI,OAAO,CAAC;IACpE;IACA,IAAI,CAACzB,SAAS,GAAG,IAAI;IACrBqB,kBAAkB,CAAC5C,IAAI,CAAC,UAAUmD,QAAQ,EAAE;MAC1C,IAAIA,QAAQ,CAACzC,SAAS,CAAC0C,MAAM,EAAE;QAC7B,IAAI,CAAC7B,SAAS,GAAG,KAAK;MACxB;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDP,aAAa,CAACJ,SAAS,CAACmC,wBAAwB,GAAG,UAAUH,kBAAkB,EAAE;IAC/E,IAAIE,gBAAgB,GAAG,KAAK;IAC5B9C,IAAI,CAACM,yBAAyB,EAAE,UAAU+C,OAAO,EAAE;MACjD,IAAIC,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAAClD,eAAe,CAACgD,OAAO,CAAC,EAAE9C,kBAAkB,CAAC;MACxF;MACA;MACA;MACA,IAAI,CAAC+C,QAAQ,CAACE,SAAS,EAAE;QACvB;MACF;MACAV,gBAAgB,GAAG,IAAI;MACvB,IAAIK,QAAQ,GAAG,IAAI1C,gBAAgB,CAAC,CAAC;MACrCT,IAAI,CAACsD,QAAQ,CAACG,MAAM,EAAE,UAAUC,SAAS,EAAE;QACzCP,QAAQ,CAACtC,GAAG,CAAC6C,SAAS,CAACC,cAAc,CAAC;MACxC,CAAC,CAAC;MACFf,kBAAkB,CAACgB,GAAG,CAACP,OAAO,EAAEF,QAAQ,CAAC;IAC3C,CAAC,EAAE,IAAI,CAAC;IACR,OAAOL,gBAAgB;EACzB,CAAC;EACD9B,aAAa,CAACJ,SAAS,CAACsC,2BAA2B,GAAG,UAAUN,kBAAkB,EAAEiB,MAAM,EAAE;IAC1F,IAAIjC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIkC,QAAQ,GAAG,IAAI;IACnB;IACA,IAAIA,QAAQ,EAAE;MACZ,IAAIT,OAAO,GAAGQ,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,GAAG;MAC/C,IAAIE,UAAU,GAAGnC,OAAO,CAACoC,cAAc,CAAC;QACtCC,QAAQ,EAAEZ,OAAO,GAAG;MACtB,CAAC,CAAC;MACFa,eAAe,CAACH,UAAU,EAAEV,OAAO,CAAC;IACtC;IACA;IACA,IAAIS,QAAQ,EAAE;MACZ,IAAIC,UAAU,GAAGnC,OAAO,CAACoC,cAAc,CAAC;QACtCC,QAAQ,EAAE,YAAY;QACtBE,MAAM,EAAE,SAAAA,CAAUT,SAAS,EAAE;UAC3B,OAAOA,SAAS,CAACf,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAKkB,MAAM;QACjD;MACF,CAAC,CAAC;MACFK,eAAe,CAACH,UAAU,EAAE,QAAQ,CAAC;IACvC;IACA,SAASG,eAAeA,CAACH,UAAU,EAAEV,OAAO,EAAE;MAC5C;MACA,IAAIK,SAAS,GAAGK,UAAU,CAAC,CAAC,CAAC;MAC7B,IAAI,CAACL,SAAS,EAAE;QACd;MACF;MACA,IAAIP,QAAQ,GAAG,IAAI1C,gBAAgB,CAAC,CAAC;MACrC0C,QAAQ,CAACtC,GAAG,CAAC6C,SAAS,CAACC,cAAc,CAAC;MACtCf,kBAAkB,CAACgB,GAAG,CAACP,OAAO,EAAEF,QAAQ,CAAC;MACzCW,QAAQ,GAAG,KAAK;MAChB;MACA,IAAIT,OAAO,KAAK,GAAG,IAAIA,OAAO,KAAK,GAAG,EAAE;QACtC,IAAIe,WAAW,GAAGV,SAAS,CAACH,sBAAsB,CAAC,MAAM,EAAE/C,gBAAgB,CAAC,CAACiD,MAAM,CAAC,CAAC,CAAC;QACtFW,WAAW,IAAIpE,IAAI,CAAC+D,UAAU,EAAE,UAAUM,OAAO,EAAE;UACjD,IAAIX,SAAS,CAACC,cAAc,KAAKU,OAAO,CAACV,cAAc,IAAIS,WAAW,KAAKC,OAAO,CAACd,sBAAsB,CAAC,MAAM,EAAE/C,gBAAgB,CAAC,CAACiD,MAAM,CAAC,CAAC,CAAC,EAAE;YAC7IN,QAAQ,CAACtC,GAAG,CAACwD,OAAO,CAACV,cAAc,CAAC;UACtC;QACF,CAAC,CAAC;MACJ;IACF;IACA,IAAIG,QAAQ,EAAE;MACZ;MACA9D,IAAI,CAACM,yBAAyB,EAAE,UAAU+C,OAAO,EAAE;QACjD,IAAI,CAACS,QAAQ,EAAE;UACb;QACF;QACA,IAAIC,UAAU,GAAGnC,OAAO,CAACoC,cAAc,CAAC;UACtCC,QAAQ,EAAE5D,eAAe,CAACgD,OAAO,CAAC;UAClCc,MAAM,EAAE,SAAAA,CAAUT,SAAS,EAAE;YAC3B,OAAOA,SAAS,CAACf,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,UAAU;UACnD;QACF,CAAC,CAAC;QACF,IAAIoB,UAAU,CAAC,CAAC,CAAC,EAAE;UACjB,IAAIZ,QAAQ,GAAG,IAAI1C,gBAAgB,CAAC,CAAC;UACrC0C,QAAQ,CAACtC,GAAG,CAACkD,UAAU,CAAC,CAAC,CAAC,CAACJ,cAAc,CAAC;UAC1Cf,kBAAkB,CAACgB,GAAG,CAACP,OAAO,EAAEF,QAAQ,CAAC;UACzCW,QAAQ,GAAG,KAAK;QAClB;MACF,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EACD9C,aAAa,CAACJ,SAAS,CAACqC,2BAA2B,GAAG,YAAY;IAChE,IAAIqB,GAAG;IACP;IACA,IAAI,CAACC,cAAc,CAAC,UAAUlB,OAAO,EAAE;MACrC,CAACiB,GAAG,KAAKA,GAAG,GAAGjB,OAAO,CAAC;IACzB,CAAC,EAAE,IAAI,CAAC;IACR,OAAOiB,GAAG,KAAK,GAAG,GAAG,UAAU,GAAG,YAAY;EAChD,CAAC;EACDtD,aAAa,CAACJ,SAAS,CAACyB,mBAAmB,GAAG,UAAUR,cAAc,EAAE;IACtE;IACA,IAAIA,cAAc,CAAC2C,cAAc,CAAC,UAAU,CAAC,EAAE;MAC7C,IAAI,CAAClD,aAAa,GAAG,KAAK;IAC5B;IACA,IAAI,IAAI,CAACA,aAAa,EAAE;MACtB,IAAImD,YAAY,GAAG,IAAI,CAAC7C,OAAO,CAACF,MAAM;MACtC,IAAI,CAACA,MAAM,CAACgD,QAAQ,GAAGD,YAAY,CAACE,SAAS,IAAIF,YAAY,CAACG,uBAAuB,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;IACtG;EACF,CAAC;EACD5D,aAAa,CAACJ,SAAS,CAAC0B,eAAe,GAAG,UAAUT,cAAc,EAAE;IAClE,IAAIgD,aAAa,GAAG,IAAI,CAACrD,cAAc;IACvC,IAAIsD,iBAAiB,GAAG,IAAI,CAACnC,GAAG,CAAC,WAAW,CAAC;IAC7C3C,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE,UAAUuC,KAAK,EAAEC,KAAK,EAAE;MAC3E,IAAIuC,gBAAgB,GAAGlD,cAAc,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;MACvD,IAAIyC,cAAc,GAAGnD,cAAc,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;MACrD,IAAIwC,gBAAgB,IAAI,CAACC,cAAc,EAAE;QACvCH,aAAa,CAACrC,KAAK,CAAC,GAAG,SAAS;MAClC,CAAC,MAAM,IAAI,CAACuC,gBAAgB,IAAIC,cAAc,EAAE;QAC9CH,aAAa,CAACrC,KAAK,CAAC,GAAG,OAAO;MAChC,CAAC,MAAM,IAAIsC,iBAAiB,EAAE;QAC5BD,aAAa,CAACrC,KAAK,CAAC,GAAGsC,iBAAiB,CAACtC,KAAK,CAAC;MACjD,CAAC,MAAM,IAAIuC,gBAAgB,EAAE;QAC3B;QACAF,aAAa,CAACrC,KAAK,CAAC,GAAG,SAAS;MAClC;MACA;IACF,CAAC,CAAC;EACJ,CAAC;EACDxB,aAAa,CAACJ,SAAS,CAACqE,QAAQ,GAAG,YAAY;IAC7C,OAAO,IAAI,CAAC1D,SAAS;EACvB,CAAC;EACDP,aAAa,CAACJ,SAAS,CAACsE,uBAAuB,GAAG,YAAY;IAC5D,IAAIC,cAAc;IAClB,IAAI,CAACZ,cAAc,CAAC,UAAUlB,OAAO,EAAE+B,SAAS,EAAE;MAChD,IAAID,cAAc,IAAI,IAAI,EAAE;QAC1BA,cAAc,GAAG,IAAI,CAACvD,OAAO,CAACyD,YAAY,CAAChF,eAAe,CAACgD,OAAO,CAAC,EAAE+B,SAAS,CAAC;MACjF;IACF,CAAC,EAAE,IAAI,CAAC;IACR,OAAOD,cAAc;EACvB,CAAC;EACD;AACF;AACA;EACEnE,aAAa,CAACJ,SAAS,CAAC2D,cAAc,GAAG,UAAUe,QAAQ,EAAEC,OAAO,EAAE;IACpE,IAAI,CAAC1C,kBAAkB,CAAC7C,IAAI,CAAC,UAAUmD,QAAQ,EAAEE,OAAO,EAAE;MACxDrD,IAAI,CAACmD,QAAQ,CAACzC,SAAS,EAAE,UAAU0E,SAAS,EAAE;QAC5CE,QAAQ,CAACE,IAAI,CAACD,OAAO,EAAElC,OAAO,EAAE+B,SAAS,CAAC;MAC5C,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEpE,aAAa,CAACJ,SAAS,CAAC6E,YAAY,GAAG,UAAUpC,OAAO,EAAE+B,SAAS,EAAE;IACnE,IAAI1B,SAAS,GAAG,IAAI,CAACgC,YAAY,CAACrC,OAAO,EAAE+B,SAAS,CAAC;IACrD,IAAI1B,SAAS,EAAE;MACb,OAAOA,SAAS,CAACiC,aAAa;IAChC;EACF,CAAC;EACD;AACF;AACA;EACE3E,aAAa,CAACJ,SAAS,CAAC8E,YAAY,GAAG,UAAUrC,OAAO,EAAE+B,SAAS,EAAE;IACnE,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC3F,MAAM,CAACkD,OAAO,IAAI+B,SAAS,IAAI,IAAI,CAAC;IACtC;IACA,IAAIjC,QAAQ,GAAG,IAAI,CAACN,kBAAkB,CAACF,GAAG,CAACU,OAAO,CAAC;IACnD,IAAIF,QAAQ,IAAIA,QAAQ,CAACxC,QAAQ,CAACyE,SAAS,CAAC,EAAE;MAC5C,OAAO,IAAI,CAACxD,OAAO,CAACyD,YAAY,CAAChF,eAAe,CAACgD,OAAO,CAAC,EAAE+B,SAAS,CAAC;IACvE;EACF,CAAC;EACD;AACF;AACA;EACEpE,aAAa,CAACJ,SAAS,CAACmF,WAAW,GAAG,UAAUC,GAAG,EAAE;IACnD,IAAI5D,UAAU,GAAG,IAAI,CAACV,MAAM;IAC5B,IAAIK,aAAa,GAAG,IAAI,CAACA,aAAa;IACtC/B,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE,UAAUuC,KAAK,EAAE;MACpE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIyD,GAAG,CAACzD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIyD,GAAG,CAACzD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QAClDH,UAAU,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGR,aAAa,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGyD,GAAG,CAACzD,KAAK,CAAC,CAAC,CAAC,CAAC;QAC9DH,UAAU,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGR,aAAa,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGyD,GAAG,CAACzD,KAAK,CAAC,CAAC,CAAC,CAAC;MAChE;IACF,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAACD,eAAe,CAAC0D,GAAG,CAAC;EAC3B,CAAC;EACDhF,aAAa,CAACJ,SAAS,CAACqF,kBAAkB,GAAG,UAAUD,GAAG,EAAE;IAC1D,IAAItE,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB1B,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,UAAUkG,IAAI,EAAE;MAC/DxE,MAAM,CAACwE,IAAI,CAAC,GAAGF,GAAG,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EACDlF,aAAa,CAACJ,SAAS,CAACuF,eAAe,GAAG,YAAY;IACpD,IAAIC,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;IAClD,IAAID,SAAS,EAAE;MACb,OAAOA,SAAS,CAACE,oBAAoB,CAAC,CAAC;IACzC;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;EACEtF,aAAa,CAACJ,SAAS,CAAC2F,aAAa,GAAG,UAAUlD,OAAO,EAAE+B,SAAS,EAAE;IACpE,IAAI/B,OAAO,IAAI,IAAI,IAAI+B,SAAS,IAAI,IAAI,EAAE;MACxC,IAAIgB,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;MAClD,IAAID,SAAS,EAAE;QACb,OAAOA,SAAS,CAACI,kBAAkB,CAAC,CAAC;MACvC;IACF,CAAC,MAAM;MACL,OAAO,IAAI,CAACf,YAAY,CAACpC,OAAO,EAAE+B,SAAS,CAAC,CAACoB,kBAAkB,CAAC,CAAC;IACnE;EACF,CAAC;EACD;AACF;AACA;AACA;EACExF,aAAa,CAACJ,SAAS,CAACyF,2BAA2B,GAAG,UAAU3C,SAAS,EAAE;IACzE,IAAIA,SAAS,EAAE;MACb,OAAOA,SAAS,CAACiC,aAAa;IAChC;IACA;IACA,IAAIc,UAAU;IACd,IAAIC,WAAW,GAAG,IAAI,CAAC7D,kBAAkB,CAAC8D,IAAI,CAAC,CAAC;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACtD,MAAM,EAAEwD,CAAC,EAAE,EAAE;MAC3C,IAAIvD,OAAO,GAAGqD,WAAW,CAACE,CAAC,CAAC;MAC5B,IAAIzD,QAAQ,GAAG,IAAI,CAACN,kBAAkB,CAACF,GAAG,CAACU,OAAO,CAAC;MACnD,KAAK,IAAIwD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1D,QAAQ,CAACzC,SAAS,CAAC0C,MAAM,EAAEyD,CAAC,EAAE,EAAE;QAClD,IAAIC,KAAK,GAAG,IAAI,CAACrB,YAAY,CAACpC,OAAO,EAAEF,QAAQ,CAACzC,SAAS,CAACmG,CAAC,CAAC,CAAC;QAC7D,IAAIC,KAAK,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;UACxB,OAAOD,KAAK;QACd;QACA,IAAI,CAACL,UAAU,EAAE;UACfA,UAAU,GAAGK,KAAK;QACpB;MACF;IACF;IACA;IACA;IACA;IACA,OAAOL,UAAU;EACnB,CAAC;EACDzF,aAAa,CAACJ,SAAS,CAACoG,gBAAgB,GAAG,YAAY;IACrD,OAAO,IAAI,CAACxF,cAAc,CAACyF,KAAK,CAAC,CAAC;EACpC,CAAC;EACDjG,aAAa,CAACJ,SAAS,CAACsG,SAAS,GAAG,YAAY;IAC9C,IAAItB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA3F,MAAM,CAAC,IAAI,CAAC6C,OAAO,CAAC;IACtB;IACA,OAAO,IAAI,CAACA,OAAO;EACrB,CAAC;EACDhC,aAAa,CAACK,IAAI,GAAG,UAAU;EAC/BL,aAAa,CAACmG,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC;EAC7GnG,aAAa,CAACoG,aAAa,GAAG;IAC5B;IACAC,CAAC,EAAE,CAAC;IACJC,UAAU,EAAE,QAAQ;IACpBC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACP,CAAC;EACD,OAAOxG,aAAa;AACtB,CAAC,CAACZ,cAAc,CAAC;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS0B,iBAAiBA,CAACJ,MAAM,EAAE;EACjC,IAAI+F,GAAG,GAAG,CAAC,CAAC;EACZzH,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,CAAC,EAAE,UAAUkG,IAAI,EAAE;IAC3ExE,MAAM,CAAC8C,cAAc,CAAC0B,IAAI,CAAC,KAAKuB,GAAG,CAACvB,IAAI,CAAC,GAAGxE,MAAM,CAACwE,IAAI,CAAC,CAAC;EAC3D,CAAC,CAAC;EACF,OAAOuB,GAAG;AACZ;AACA,eAAezG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}