{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeToArray } from '../../util/model.js';\nvar DEFAULT_TOOLBOX_BTNS = ['rect', 'polygon', 'keep', 'clear'];\nexport default function brushPreprocessor(option, isNew) {\n  var brushComponents = normalizeToArray(option ? option.brush : []);\n  if (!brushComponents.length) {\n    return;\n  }\n  var brushComponentSpecifiedBtns = [];\n  zrUtil.each(brushComponents, function (brushOpt) {\n    var tbs = brushOpt.hasOwnProperty('toolbox') ? brushOpt.toolbox : [];\n    if (tbs instanceof Array) {\n      brushComponentSpecifiedBtns = brushComponentSpecifiedBtns.concat(tbs);\n    }\n  });\n  var toolbox = option && option.toolbox;\n  if (zrUtil.isArray(toolbox)) {\n    toolbox = toolbox[0];\n  }\n  if (!toolbox) {\n    toolbox = {\n      feature: {}\n    };\n    option.toolbox = [toolbox];\n  }\n  var toolboxFeature = toolbox.feature || (toolbox.feature = {});\n  var toolboxBrush = toolboxFeature.brush || (toolboxFeature.brush = {});\n  var brushTypes = toolboxBrush.type || (toolboxBrush.type = []);\n  brushTypes.push.apply(brushTypes, brushComponentSpecifiedBtns);\n  removeDuplicate(brushTypes);\n  if (isNew && !brushTypes.length) {\n    brushTypes.push.apply(brushTypes, DEFAULT_TOOLBOX_BTNS);\n  }\n}\nfunction removeDuplicate(arr) {\n  var map = {};\n  zrUtil.each(arr, function (val) {\n    map[val] = 1;\n  });\n  arr.length = 0;\n  zrUtil.each(map, function (flag, val) {\n    arr.push(val);\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "normalizeToArray", "DEFAULT_TOOLBOX_BTNS", "brushPreprocessor", "option", "isNew", "brushComponents", "brush", "length", "brushComponentSpecifiedBtns", "each", "brushOpt", "tbs", "hasOwnProperty", "toolbox", "Array", "concat", "isArray", "feature", "toolboxFeature", "toolboxBrush", "brushTypes", "type", "push", "apply", "removeDuplicate", "arr", "map", "val", "flag"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/brush/preprocessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { normalizeToArray } from '../../util/model.js';\nvar DEFAULT_TOOLBOX_BTNS = ['rect', 'polygon', 'keep', 'clear'];\nexport default function brushPreprocessor(option, isNew) {\n  var brushComponents = normalizeToArray(option ? option.brush : []);\n  if (!brushComponents.length) {\n    return;\n  }\n  var brushComponentSpecifiedBtns = [];\n  zrUtil.each(brushComponents, function (brushOpt) {\n    var tbs = brushOpt.hasOwnProperty('toolbox') ? brushOpt.toolbox : [];\n    if (tbs instanceof Array) {\n      brushComponentSpecifiedBtns = brushComponentSpecifiedBtns.concat(tbs);\n    }\n  });\n  var toolbox = option && option.toolbox;\n  if (zrUtil.isArray(toolbox)) {\n    toolbox = toolbox[0];\n  }\n  if (!toolbox) {\n    toolbox = {\n      feature: {}\n    };\n    option.toolbox = [toolbox];\n  }\n  var toolboxFeature = toolbox.feature || (toolbox.feature = {});\n  var toolboxBrush = toolboxFeature.brush || (toolboxFeature.brush = {});\n  var brushTypes = toolboxBrush.type || (toolboxBrush.type = []);\n  brushTypes.push.apply(brushTypes, brushComponentSpecifiedBtns);\n  removeDuplicate(brushTypes);\n  if (isNew && !brushTypes.length) {\n    brushTypes.push.apply(brushTypes, DEFAULT_TOOLBOX_BTNS);\n  }\n}\nfunction removeDuplicate(arr) {\n  var map = {};\n  zrUtil.each(arr, function (val) {\n    map[val] = 1;\n  });\n  arr.length = 0;\n  zrUtil.each(map, function (flag, val) {\n    arr.push(val);\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,IAAIC,oBAAoB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AAC/D,eAAe,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACvD,IAAIC,eAAe,GAAGL,gBAAgB,CAACG,MAAM,GAAGA,MAAM,CAACG,KAAK,GAAG,EAAE,CAAC;EAClE,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC3B;EACF;EACA,IAAIC,2BAA2B,GAAG,EAAE;EACpCT,MAAM,CAACU,IAAI,CAACJ,eAAe,EAAE,UAAUK,QAAQ,EAAE;IAC/C,IAAIC,GAAG,GAAGD,QAAQ,CAACE,cAAc,CAAC,SAAS,CAAC,GAAGF,QAAQ,CAACG,OAAO,GAAG,EAAE;IACpE,IAAIF,GAAG,YAAYG,KAAK,EAAE;MACxBN,2BAA2B,GAAGA,2BAA2B,CAACO,MAAM,CAACJ,GAAG,CAAC;IACvE;EACF,CAAC,CAAC;EACF,IAAIE,OAAO,GAAGV,MAAM,IAAIA,MAAM,CAACU,OAAO;EACtC,IAAId,MAAM,CAACiB,OAAO,CAACH,OAAO,CAAC,EAAE;IAC3BA,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;EACtB;EACA,IAAI,CAACA,OAAO,EAAE;IACZA,OAAO,GAAG;MACRI,OAAO,EAAE,CAAC;IACZ,CAAC;IACDd,MAAM,CAACU,OAAO,GAAG,CAACA,OAAO,CAAC;EAC5B;EACA,IAAIK,cAAc,GAAGL,OAAO,CAACI,OAAO,KAAKJ,OAAO,CAACI,OAAO,GAAG,CAAC,CAAC,CAAC;EAC9D,IAAIE,YAAY,GAAGD,cAAc,CAACZ,KAAK,KAAKY,cAAc,CAACZ,KAAK,GAAG,CAAC,CAAC,CAAC;EACtE,IAAIc,UAAU,GAAGD,YAAY,CAACE,IAAI,KAAKF,YAAY,CAACE,IAAI,GAAG,EAAE,CAAC;EAC9DD,UAAU,CAACE,IAAI,CAACC,KAAK,CAACH,UAAU,EAAEZ,2BAA2B,CAAC;EAC9DgB,eAAe,CAACJ,UAAU,CAAC;EAC3B,IAAIhB,KAAK,IAAI,CAACgB,UAAU,CAACb,MAAM,EAAE;IAC/Ba,UAAU,CAACE,IAAI,CAACC,KAAK,CAACH,UAAU,EAAEnB,oBAAoB,CAAC;EACzD;AACF;AACA,SAASuB,eAAeA,CAACC,GAAG,EAAE;EAC5B,IAAIC,GAAG,GAAG,CAAC,CAAC;EACZ3B,MAAM,CAACU,IAAI,CAACgB,GAAG,EAAE,UAAUE,GAAG,EAAE;IAC9BD,GAAG,CAACC,GAAG,CAAC,GAAG,CAAC;EACd,CAAC,CAAC;EACFF,GAAG,CAAClB,MAAM,GAAG,CAAC;EACdR,MAAM,CAACU,IAAI,CAACiB,GAAG,EAAE,UAAUE,IAAI,EAAED,GAAG,EAAE;IACpCF,GAAG,CAACH,IAAI,CAACK,GAAG,CAAC;EACf,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}