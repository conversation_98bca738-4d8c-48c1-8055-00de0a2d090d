{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS } from '../../util/types.js';\nimport { makePrintable, throwError } from '../../util/log.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { getRawValueParser, SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nvar sampleLog = '';\nif (process.env.NODE_ENV !== 'production') {\n  sampleLog = ['Valid config is like:', '{ dimension: \"age\", order: \"asc\" }', 'or [{ dimension: \"age\", order: \"asc\"], { dimension: \"date\", order: \"desc\" }]'].join(' ');\n}\nexport var sortTransform = {\n  type: 'echarts:sort',\n  transform: function (params) {\n    var upstream = params.upstream;\n    var config = params.config;\n    var errMsg = '';\n    // Normalize\n    // const orderExprList: OrderExpression[] = isArray(config[0])\n    //     ? config as OrderExpression[]\n    //     : [config as OrderExpression];\n    var orderExprList = normalizeToArray(config);\n    if (!orderExprList.length) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Empty `config` in sort transform.';\n      }\n      throwError(errMsg);\n    }\n    var orderDefList = [];\n    each(orderExprList, function (orderExpr) {\n      var dimLoose = orderExpr.dimension;\n      var order = orderExpr.order;\n      var parserName = orderExpr.parser;\n      var incomparable = orderExpr.incomparable;\n      if (dimLoose == null) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'Sort transform config must has \"dimension\" specified.' + sampleLog;\n        }\n        throwError(errMsg);\n      }\n      if (order !== 'asc' && order !== 'desc') {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'Sort transform config must has \"order\" specified.' + sampleLog;\n        }\n        throwError(errMsg);\n      }\n      if (incomparable && incomparable !== 'min' && incomparable !== 'max') {\n        var errMsg_1 = '';\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg_1 = 'incomparable must be \"min\" or \"max\" rather than \"' + incomparable + '\".';\n        }\n        throwError(errMsg_1);\n      }\n      if (order !== 'asc' && order !== 'desc') {\n        var errMsg_2 = '';\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg_2 = 'order must be \"asc\" or \"desc\" rather than \"' + order + '\".';\n        }\n        throwError(errMsg_2);\n      }\n      var dimInfo = upstream.getDimensionInfo(dimLoose);\n      if (!dimInfo) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = makePrintable('Can not find dimension info via: ' + dimLoose + '.\\n', 'Existing dimensions: ', upstream.cloneAllDimensionInfo(), '.\\n', 'Illegal config:', orderExpr, '.\\n');\n        }\n        throwError(errMsg);\n      }\n      var parser = parserName ? getRawValueParser(parserName) : null;\n      if (parserName && !parser) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = makePrintable('Invalid parser name ' + parserName + '.\\n', 'Illegal config:', orderExpr, '.\\n');\n        }\n        throwError(errMsg);\n      }\n      orderDefList.push({\n        dimIdx: dimInfo.index,\n        parser: parser,\n        comparator: new SortOrderComparator(order, incomparable)\n      });\n    });\n    // TODO: support it?\n    var sourceFormat = upstream.sourceFormat;\n    if (sourceFormat !== SOURCE_FORMAT_ARRAY_ROWS && sourceFormat !== SOURCE_FORMAT_OBJECT_ROWS) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'sourceFormat \"' + sourceFormat + '\" is not supported yet';\n      }\n      throwError(errMsg);\n    }\n    // Other upstream format are all array.\n    var resultData = [];\n    for (var i = 0, len = upstream.count(); i < len; i++) {\n      resultData.push(upstream.getRawDataItem(i));\n    }\n    resultData.sort(function (item0, item1) {\n      for (var i = 0; i < orderDefList.length; i++) {\n        var orderDef = orderDefList[i];\n        var val0 = upstream.retrieveValueFromItem(item0, orderDef.dimIdx);\n        var val1 = upstream.retrieveValueFromItem(item1, orderDef.dimIdx);\n        if (orderDef.parser) {\n          val0 = orderDef.parser(val0);\n          val1 = orderDef.parser(val1);\n        }\n        var result = orderDef.comparator.evaluate(val0, val1);\n        if (result !== 0) {\n          return result;\n        }\n      }\n      return 0;\n    });\n    return {\n      data: resultData\n    };\n  }\n};", "map": {"version": 3, "names": ["SOURCE_FORMAT_ARRAY_ROWS", "SOURCE_FORMAT_OBJECT_ROWS", "makePrintable", "throwError", "each", "normalizeToArray", "getRawValueParser", "SortOrderComparator", "sampleLog", "process", "env", "NODE_ENV", "join", "sortTransform", "type", "transform", "params", "upstream", "config", "errMsg", "orderExprList", "length", "orderDefList", "orderExpr", "dimLoose", "dimension", "order", "parserName", "parser", "incomparable", "errMsg_1", "errMsg_2", "dimInfo", "getDimensionInfo", "cloneAllDimensionInfo", "push", "dimIdx", "index", "comparator", "sourceFormat", "resultData", "i", "len", "count", "getRawDataItem", "sort", "item0", "item1", "orderDef", "val0", "retrieveValueFromItem", "val1", "result", "evaluate", "data"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/transform/sortTransform.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { SOURCE_FORMAT_ARRAY_ROWS, SOURCE_FORMAT_OBJECT_ROWS } from '../../util/types.js';\nimport { makePrintable, throwError } from '../../util/log.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { getRawValueParser, SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nvar sampleLog = '';\nif (process.env.NODE_ENV !== 'production') {\n  sampleLog = ['Valid config is like:', '{ dimension: \"age\", order: \"asc\" }', 'or [{ dimension: \"age\", order: \"asc\"], { dimension: \"date\", order: \"desc\" }]'].join(' ');\n}\nexport var sortTransform = {\n  type: 'echarts:sort',\n  transform: function (params) {\n    var upstream = params.upstream;\n    var config = params.config;\n    var errMsg = '';\n    // Normalize\n    // const orderExprList: OrderExpression[] = isArray(config[0])\n    //     ? config as OrderExpression[]\n    //     : [config as OrderExpression];\n    var orderExprList = normalizeToArray(config);\n    if (!orderExprList.length) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'Empty `config` in sort transform.';\n      }\n      throwError(errMsg);\n    }\n    var orderDefList = [];\n    each(orderExprList, function (orderExpr) {\n      var dimLoose = orderExpr.dimension;\n      var order = orderExpr.order;\n      var parserName = orderExpr.parser;\n      var incomparable = orderExpr.incomparable;\n      if (dimLoose == null) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'Sort transform config must has \"dimension\" specified.' + sampleLog;\n        }\n        throwError(errMsg);\n      }\n      if (order !== 'asc' && order !== 'desc') {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = 'Sort transform config must has \"order\" specified.' + sampleLog;\n        }\n        throwError(errMsg);\n      }\n      if (incomparable && incomparable !== 'min' && incomparable !== 'max') {\n        var errMsg_1 = '';\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg_1 = 'incomparable must be \"min\" or \"max\" rather than \"' + incomparable + '\".';\n        }\n        throwError(errMsg_1);\n      }\n      if (order !== 'asc' && order !== 'desc') {\n        var errMsg_2 = '';\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg_2 = 'order must be \"asc\" or \"desc\" rather than \"' + order + '\".';\n        }\n        throwError(errMsg_2);\n      }\n      var dimInfo = upstream.getDimensionInfo(dimLoose);\n      if (!dimInfo) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = makePrintable('Can not find dimension info via: ' + dimLoose + '.\\n', 'Existing dimensions: ', upstream.cloneAllDimensionInfo(), '.\\n', 'Illegal config:', orderExpr, '.\\n');\n        }\n        throwError(errMsg);\n      }\n      var parser = parserName ? getRawValueParser(parserName) : null;\n      if (parserName && !parser) {\n        if (process.env.NODE_ENV !== 'production') {\n          errMsg = makePrintable('Invalid parser name ' + parserName + '.\\n', 'Illegal config:', orderExpr, '.\\n');\n        }\n        throwError(errMsg);\n      }\n      orderDefList.push({\n        dimIdx: dimInfo.index,\n        parser: parser,\n        comparator: new SortOrderComparator(order, incomparable)\n      });\n    });\n    // TODO: support it?\n    var sourceFormat = upstream.sourceFormat;\n    if (sourceFormat !== SOURCE_FORMAT_ARRAY_ROWS && sourceFormat !== SOURCE_FORMAT_OBJECT_ROWS) {\n      if (process.env.NODE_ENV !== 'production') {\n        errMsg = 'sourceFormat \"' + sourceFormat + '\" is not supported yet';\n      }\n      throwError(errMsg);\n    }\n    // Other upstream format are all array.\n    var resultData = [];\n    for (var i = 0, len = upstream.count(); i < len; i++) {\n      resultData.push(upstream.getRawDataItem(i));\n    }\n    resultData.sort(function (item0, item1) {\n      for (var i = 0; i < orderDefList.length; i++) {\n        var orderDef = orderDefList[i];\n        var val0 = upstream.retrieveValueFromItem(item0, orderDef.dimIdx);\n        var val1 = upstream.retrieveValueFromItem(item1, orderDef.dimIdx);\n        if (orderDef.parser) {\n          val0 = orderDef.parser(val0);\n          val1 = orderDef.parser(val1);\n        }\n        var result = orderDef.comparator.evaluate(val0, val1);\n        if (result !== 0) {\n          return result;\n        }\n      }\n      return 0;\n    });\n    return {\n      data: resultData\n    };\n  }\n};"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,wBAAwB,EAAEC,yBAAyB,QAAQ,qBAAqB;AACzF,SAASC,aAAa,EAAEC,UAAU,QAAQ,mBAAmB;AAC7D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,iBAAiB,EAAEC,mBAAmB,QAAQ,sCAAsC;AAC7F,IAAIC,SAAS,GAAG,EAAE;AAClB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCH,SAAS,GAAG,CAAC,uBAAuB,EAAE,oCAAoC,EAAE,8EAA8E,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;AACvK;AACA,OAAO,IAAIC,aAAa,GAAG;EACzBC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;IAC3B,IAAIC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAC9B,IAAIC,MAAM,GAAGF,MAAM,CAACE,MAAM;IAC1B,IAAIC,MAAM,GAAG,EAAE;IACf;IACA;IACA;IACA;IACA,IAAIC,aAAa,GAAGf,gBAAgB,CAACa,MAAM,CAAC;IAC5C,IAAI,CAACE,aAAa,CAACC,MAAM,EAAE;MACzB,IAAIZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCQ,MAAM,GAAG,mCAAmC;MAC9C;MACAhB,UAAU,CAACgB,MAAM,CAAC;IACpB;IACA,IAAIG,YAAY,GAAG,EAAE;IACrBlB,IAAI,CAACgB,aAAa,EAAE,UAAUG,SAAS,EAAE;MACvC,IAAIC,QAAQ,GAAGD,SAAS,CAACE,SAAS;MAClC,IAAIC,KAAK,GAAGH,SAAS,CAACG,KAAK;MAC3B,IAAIC,UAAU,GAAGJ,SAAS,CAACK,MAAM;MACjC,IAAIC,YAAY,GAAGN,SAAS,CAACM,YAAY;MACzC,IAAIL,QAAQ,IAAI,IAAI,EAAE;QACpB,IAAIf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCQ,MAAM,GAAG,uDAAuD,GAAGX,SAAS;QAC9E;QACAL,UAAU,CAACgB,MAAM,CAAC;MACpB;MACA,IAAIO,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,MAAM,EAAE;QACvC,IAAIjB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCQ,MAAM,GAAG,mDAAmD,GAAGX,SAAS;QAC1E;QACAL,UAAU,CAACgB,MAAM,CAAC;MACpB;MACA,IAAIU,YAAY,IAAIA,YAAY,KAAK,KAAK,IAAIA,YAAY,KAAK,KAAK,EAAE;QACpE,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAIrB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCmB,QAAQ,GAAG,mDAAmD,GAAGD,YAAY,GAAG,IAAI;QACtF;QACA1B,UAAU,CAAC2B,QAAQ,CAAC;MACtB;MACA,IAAIJ,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,MAAM,EAAE;QACvC,IAAIK,QAAQ,GAAG,EAAE;QACjB,IAAItB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCoB,QAAQ,GAAG,6CAA6C,GAAGL,KAAK,GAAG,IAAI;QACzE;QACAvB,UAAU,CAAC4B,QAAQ,CAAC;MACtB;MACA,IAAIC,OAAO,GAAGf,QAAQ,CAACgB,gBAAgB,CAACT,QAAQ,CAAC;MACjD,IAAI,CAACQ,OAAO,EAAE;QACZ,IAAIvB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCQ,MAAM,GAAGjB,aAAa,CAAC,mCAAmC,GAAGsB,QAAQ,GAAG,KAAK,EAAE,uBAAuB,EAAEP,QAAQ,CAACiB,qBAAqB,CAAC,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAEX,SAAS,EAAE,KAAK,CAAC;QACvL;QACApB,UAAU,CAACgB,MAAM,CAAC;MACpB;MACA,IAAIS,MAAM,GAAGD,UAAU,GAAGrB,iBAAiB,CAACqB,UAAU,CAAC,GAAG,IAAI;MAC9D,IAAIA,UAAU,IAAI,CAACC,MAAM,EAAE;QACzB,IAAInB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCQ,MAAM,GAAGjB,aAAa,CAAC,sBAAsB,GAAGyB,UAAU,GAAG,KAAK,EAAE,iBAAiB,EAAEJ,SAAS,EAAE,KAAK,CAAC;QAC1G;QACApB,UAAU,CAACgB,MAAM,CAAC;MACpB;MACAG,YAAY,CAACa,IAAI,CAAC;QAChBC,MAAM,EAAEJ,OAAO,CAACK,KAAK;QACrBT,MAAM,EAAEA,MAAM;QACdU,UAAU,EAAE,IAAI/B,mBAAmB,CAACmB,KAAK,EAAEG,YAAY;MACzD,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;IACA,IAAIU,YAAY,GAAGtB,QAAQ,CAACsB,YAAY;IACxC,IAAIA,YAAY,KAAKvC,wBAAwB,IAAIuC,YAAY,KAAKtC,yBAAyB,EAAE;MAC3F,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCQ,MAAM,GAAG,gBAAgB,GAAGoB,YAAY,GAAG,wBAAwB;MACrE;MACApC,UAAU,CAACgB,MAAM,CAAC;IACpB;IACA;IACA,IAAIqB,UAAU,GAAG,EAAE;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGzB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACpDD,UAAU,CAACL,IAAI,CAAClB,QAAQ,CAAC2B,cAAc,CAACH,CAAC,CAAC,CAAC;IAC7C;IACAD,UAAU,CAACK,IAAI,CAAC,UAAUC,KAAK,EAAEC,KAAK,EAAE;MACtC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,YAAY,CAACD,MAAM,EAAEoB,CAAC,EAAE,EAAE;QAC5C,IAAIO,QAAQ,GAAG1B,YAAY,CAACmB,CAAC,CAAC;QAC9B,IAAIQ,IAAI,GAAGhC,QAAQ,CAACiC,qBAAqB,CAACJ,KAAK,EAAEE,QAAQ,CAACZ,MAAM,CAAC;QACjE,IAAIe,IAAI,GAAGlC,QAAQ,CAACiC,qBAAqB,CAACH,KAAK,EAAEC,QAAQ,CAACZ,MAAM,CAAC;QACjE,IAAIY,QAAQ,CAACpB,MAAM,EAAE;UACnBqB,IAAI,GAAGD,QAAQ,CAACpB,MAAM,CAACqB,IAAI,CAAC;UAC5BE,IAAI,GAAGH,QAAQ,CAACpB,MAAM,CAACuB,IAAI,CAAC;QAC9B;QACA,IAAIC,MAAM,GAAGJ,QAAQ,CAACV,UAAU,CAACe,QAAQ,CAACJ,IAAI,EAAEE,IAAI,CAAC;QACrD,IAAIC,MAAM,KAAK,CAAC,EAAE;UAChB,OAAOA,MAAM;QACf;MACF;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IACF,OAAO;MACLE,IAAI,EAAEd;IACR,CAAC;EACH;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}