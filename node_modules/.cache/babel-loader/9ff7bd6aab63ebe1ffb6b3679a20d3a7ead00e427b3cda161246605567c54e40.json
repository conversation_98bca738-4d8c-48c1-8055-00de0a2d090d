{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { groupData } from '../../util/model.js';\nexport default function sankeyLayout(ecModel, api) {\n  ecModel.eachSeriesByType('sankey', function (seriesModel) {\n    var nodeWidth = seriesModel.get('nodeWidth');\n    var nodeGap = seriesModel.get('nodeGap');\n    var layoutInfo = getViewRect(seriesModel, api);\n    seriesModel.layoutInfo = layoutInfo;\n    var width = layoutInfo.width;\n    var height = layoutInfo.height;\n    var graph = seriesModel.getGraph();\n    var nodes = graph.nodes;\n    var edges = graph.edges;\n    computeNodeValues(nodes);\n    var filteredNodes = zrUtil.filter(nodes, function (node) {\n      return node.getLayout().value === 0;\n    });\n    var iterations = filteredNodes.length !== 0 ? 0 : seriesModel.get('layoutIterations');\n    var orient = seriesModel.get('orient');\n    var nodeAlign = seriesModel.get('nodeAlign');\n    layoutSankey(nodes, edges, nodeWidth, nodeGap, width, height, iterations, orient, nodeAlign);\n  });\n}\n/**\r\n * Get the layout position of the whole view\r\n */\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nfunction layoutSankey(nodes, edges, nodeWidth, nodeGap, width, height, iterations, orient, nodeAlign) {\n  computeNodeBreadths(nodes, edges, nodeWidth, width, height, orient, nodeAlign);\n  computeNodeDepths(nodes, edges, height, width, nodeGap, iterations, orient);\n  computeEdgeDepths(nodes, orient);\n}\n/**\r\n * Compute the value of each node by summing the associated edge's value\r\n */\nfunction computeNodeValues(nodes) {\n  zrUtil.each(nodes, function (node) {\n    var value1 = sum(node.outEdges, getEdgeValue);\n    var value2 = sum(node.inEdges, getEdgeValue);\n    var nodeRawValue = node.getValue() || 0;\n    var value = Math.max(value1, value2, nodeRawValue);\n    node.setLayout({\n      value: value\n    }, true);\n  });\n}\n/**\r\n * Compute the x-position for each node.\r\n *\r\n * Here we use Kahn algorithm to detect cycle when we traverse\r\n * the node to computer the initial x position.\r\n */\nfunction computeNodeBreadths(nodes, edges, nodeWidth, width, height, orient, nodeAlign) {\n  // Used to mark whether the edge is deleted. if it is deleted,\n  // the value is 0, otherwise it is 1.\n  var remainEdges = [];\n  // Storage each node's indegree.\n  var indegreeArr = [];\n  // Used to storage the node with indegree is equal to 0.\n  var zeroIndegrees = [];\n  var nextTargetNode = [];\n  var x = 0;\n  // let kx = 0;\n  for (var i = 0; i < edges.length; i++) {\n    remainEdges[i] = 1;\n  }\n  for (var i = 0; i < nodes.length; i++) {\n    indegreeArr[i] = nodes[i].inEdges.length;\n    if (indegreeArr[i] === 0) {\n      zeroIndegrees.push(nodes[i]);\n    }\n  }\n  var maxNodeDepth = -1;\n  // Traversing nodes using topological sorting to calculate the\n  // horizontal(if orient === 'horizontal') or vertical(if orient === 'vertical')\n  // position of the nodes.\n  while (zeroIndegrees.length) {\n    for (var idx = 0; idx < zeroIndegrees.length; idx++) {\n      var node = zeroIndegrees[idx];\n      var item = node.hostGraph.data.getRawDataItem(node.dataIndex);\n      var isItemDepth = item.depth != null && item.depth >= 0;\n      if (isItemDepth && item.depth > maxNodeDepth) {\n        maxNodeDepth = item.depth;\n      }\n      node.setLayout({\n        depth: isItemDepth ? item.depth : x\n      }, true);\n      orient === 'vertical' ? node.setLayout({\n        dy: nodeWidth\n      }, true) : node.setLayout({\n        dx: nodeWidth\n      }, true);\n      for (var edgeIdx = 0; edgeIdx < node.outEdges.length; edgeIdx++) {\n        var edge = node.outEdges[edgeIdx];\n        var indexEdge = edges.indexOf(edge);\n        remainEdges[indexEdge] = 0;\n        var targetNode = edge.node2;\n        var nodeIndex = nodes.indexOf(targetNode);\n        if (--indegreeArr[nodeIndex] === 0 && nextTargetNode.indexOf(targetNode) < 0) {\n          nextTargetNode.push(targetNode);\n        }\n      }\n    }\n    ++x;\n    zeroIndegrees = nextTargetNode;\n    nextTargetNode = [];\n  }\n  for (var i = 0; i < remainEdges.length; i++) {\n    if (remainEdges[i] === 1) {\n      throw new Error('Sankey is a DAG, the original data has cycle!');\n    }\n  }\n  var maxDepth = maxNodeDepth > x - 1 ? maxNodeDepth : x - 1;\n  if (nodeAlign && nodeAlign !== 'left') {\n    adjustNodeWithNodeAlign(nodes, nodeAlign, orient, maxDepth);\n  }\n  var kx = orient === 'vertical' ? (height - nodeWidth) / maxDepth : (width - nodeWidth) / maxDepth;\n  scaleNodeBreadths(nodes, kx, orient);\n}\nfunction isNodeDepth(node) {\n  var item = node.hostGraph.data.getRawDataItem(node.dataIndex);\n  return item.depth != null && item.depth >= 0;\n}\nfunction adjustNodeWithNodeAlign(nodes, nodeAlign, orient, maxDepth) {\n  if (nodeAlign === 'right') {\n    var nextSourceNode = [];\n    var remainNodes = nodes;\n    var nodeHeight = 0;\n    while (remainNodes.length) {\n      for (var i = 0; i < remainNodes.length; i++) {\n        var node = remainNodes[i];\n        node.setLayout({\n          skNodeHeight: nodeHeight\n        }, true);\n        for (var j = 0; j < node.inEdges.length; j++) {\n          var edge = node.inEdges[j];\n          if (nextSourceNode.indexOf(edge.node1) < 0) {\n            nextSourceNode.push(edge.node1);\n          }\n        }\n      }\n      remainNodes = nextSourceNode;\n      nextSourceNode = [];\n      ++nodeHeight;\n    }\n    zrUtil.each(nodes, function (node) {\n      if (!isNodeDepth(node)) {\n        node.setLayout({\n          depth: Math.max(0, maxDepth - node.getLayout().skNodeHeight)\n        }, true);\n      }\n    });\n  } else if (nodeAlign === 'justify') {\n    moveSinksRight(nodes, maxDepth);\n  }\n}\n/**\r\n * All the node without outEgdes are assigned maximum x-position and\r\n *     be aligned in the last column.\r\n *\r\n * @param nodes.  node of sankey view.\r\n * @param maxDepth.  use to assign to node without outEdges as x-position.\r\n */\nfunction moveSinksRight(nodes, maxDepth) {\n  zrUtil.each(nodes, function (node) {\n    if (!isNodeDepth(node) && !node.outEdges.length) {\n      node.setLayout({\n        depth: maxDepth\n      }, true);\n    }\n  });\n}\n/**\r\n * Scale node x-position to the width\r\n *\r\n * @param nodes  node of sankey view\r\n * @param kx   multiple used to scale nodes\r\n */\nfunction scaleNodeBreadths(nodes, kx, orient) {\n  zrUtil.each(nodes, function (node) {\n    var nodeDepth = node.getLayout().depth * kx;\n    orient === 'vertical' ? node.setLayout({\n      y: nodeDepth\n    }, true) : node.setLayout({\n      x: nodeDepth\n    }, true);\n  });\n}\n/**\r\n * Using Gauss-Seidel iterations method to compute the node depth(y-position)\r\n *\r\n * @param nodes  node of sankey view\r\n * @param edges  edge of sankey view\r\n * @param height  the whole height of the area to draw the view\r\n * @param nodeGap  the vertical distance between two nodes\r\n *     in the same column.\r\n * @param iterations  the number of iterations for the algorithm\r\n */\nfunction computeNodeDepths(nodes, edges, height, width, nodeGap, iterations, orient) {\n  var nodesByBreadth = prepareNodesByBreadth(nodes, orient);\n  initializeNodeDepth(nodesByBreadth, edges, height, width, nodeGap, orient);\n  resolveCollisions(nodesByBreadth, nodeGap, height, width, orient);\n  for (var alpha = 1; iterations > 0; iterations--) {\n    // 0.99 is a experience parameter, ensure that each iterations of\n    // changes as small as possible.\n    alpha *= 0.99;\n    relaxRightToLeft(nodesByBreadth, alpha, orient);\n    resolveCollisions(nodesByBreadth, nodeGap, height, width, orient);\n    relaxLeftToRight(nodesByBreadth, alpha, orient);\n    resolveCollisions(nodesByBreadth, nodeGap, height, width, orient);\n  }\n}\nfunction prepareNodesByBreadth(nodes, orient) {\n  var nodesByBreadth = [];\n  var keyAttr = orient === 'vertical' ? 'y' : 'x';\n  var groupResult = groupData(nodes, function (node) {\n    return node.getLayout()[keyAttr];\n  });\n  groupResult.keys.sort(function (a, b) {\n    return a - b;\n  });\n  zrUtil.each(groupResult.keys, function (key) {\n    nodesByBreadth.push(groupResult.buckets.get(key));\n  });\n  return nodesByBreadth;\n}\n/**\r\n * Compute the original y-position for each node\r\n */\nfunction initializeNodeDepth(nodesByBreadth, edges, height, width, nodeGap, orient) {\n  var minKy = Infinity;\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    var n = nodes.length;\n    var sum = 0;\n    zrUtil.each(nodes, function (node) {\n      sum += node.getLayout().value;\n    });\n    var ky = orient === 'vertical' ? (width - (n - 1) * nodeGap) / sum : (height - (n - 1) * nodeGap) / sum;\n    if (ky < minKy) {\n      minKy = ky;\n    }\n  });\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    zrUtil.each(nodes, function (node, i) {\n      var nodeDy = node.getLayout().value * minKy;\n      if (orient === 'vertical') {\n        node.setLayout({\n          x: i\n        }, true);\n        node.setLayout({\n          dx: nodeDy\n        }, true);\n      } else {\n        node.setLayout({\n          y: i\n        }, true);\n        node.setLayout({\n          dy: nodeDy\n        }, true);\n      }\n    });\n  });\n  zrUtil.each(edges, function (edge) {\n    var edgeDy = +edge.getValue() * minKy;\n    edge.setLayout({\n      dy: edgeDy\n    }, true);\n  });\n}\n/**\r\n * Resolve the collision of initialized depth (y-position)\r\n */\nfunction resolveCollisions(nodesByBreadth, nodeGap, height, width, orient) {\n  var keyAttr = orient === 'vertical' ? 'x' : 'y';\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    nodes.sort(function (a, b) {\n      return a.getLayout()[keyAttr] - b.getLayout()[keyAttr];\n    });\n    var nodeX;\n    var node;\n    var dy;\n    var y0 = 0;\n    var n = nodes.length;\n    var nodeDyAttr = orient === 'vertical' ? 'dx' : 'dy';\n    for (var i = 0; i < n; i++) {\n      node = nodes[i];\n      dy = y0 - node.getLayout()[keyAttr];\n      if (dy > 0) {\n        nodeX = node.getLayout()[keyAttr] + dy;\n        orient === 'vertical' ? node.setLayout({\n          x: nodeX\n        }, true) : node.setLayout({\n          y: nodeX\n        }, true);\n      }\n      y0 = node.getLayout()[keyAttr] + node.getLayout()[nodeDyAttr] + nodeGap;\n    }\n    var viewWidth = orient === 'vertical' ? width : height;\n    // If the bottommost node goes outside the bounds, push it back up\n    dy = y0 - nodeGap - viewWidth;\n    if (dy > 0) {\n      nodeX = node.getLayout()[keyAttr] - dy;\n      orient === 'vertical' ? node.setLayout({\n        x: nodeX\n      }, true) : node.setLayout({\n        y: nodeX\n      }, true);\n      y0 = nodeX;\n      for (var i = n - 2; i >= 0; --i) {\n        node = nodes[i];\n        dy = node.getLayout()[keyAttr] + node.getLayout()[nodeDyAttr] + nodeGap - y0;\n        if (dy > 0) {\n          nodeX = node.getLayout()[keyAttr] - dy;\n          orient === 'vertical' ? node.setLayout({\n            x: nodeX\n          }, true) : node.setLayout({\n            y: nodeX\n          }, true);\n        }\n        y0 = node.getLayout()[keyAttr];\n      }\n    }\n  });\n}\n/**\r\n * Change the y-position of the nodes, except most the right side nodes\r\n * @param nodesByBreadth\r\n * @param alpha  parameter used to adjust the nodes y-position\r\n */\nfunction relaxRightToLeft(nodesByBreadth, alpha, orient) {\n  zrUtil.each(nodesByBreadth.slice().reverse(), function (nodes) {\n    zrUtil.each(nodes, function (node) {\n      if (node.outEdges.length) {\n        var y = sum(node.outEdges, weightedTarget, orient) / sum(node.outEdges, getEdgeValue);\n        if (isNaN(y)) {\n          var len = node.outEdges.length;\n          y = len ? sum(node.outEdges, centerTarget, orient) / len : 0;\n        }\n        if (orient === 'vertical') {\n          var nodeX = node.getLayout().x + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            x: nodeX\n          }, true);\n        } else {\n          var nodeY = node.getLayout().y + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            y: nodeY\n          }, true);\n        }\n      }\n    });\n  });\n}\nfunction weightedTarget(edge, orient) {\n  return center(edge.node2, orient) * edge.getValue();\n}\nfunction centerTarget(edge, orient) {\n  return center(edge.node2, orient);\n}\nfunction weightedSource(edge, orient) {\n  return center(edge.node1, orient) * edge.getValue();\n}\nfunction centerSource(edge, orient) {\n  return center(edge.node1, orient);\n}\nfunction center(node, orient) {\n  return orient === 'vertical' ? node.getLayout().x + node.getLayout().dx / 2 : node.getLayout().y + node.getLayout().dy / 2;\n}\nfunction getEdgeValue(edge) {\n  return edge.getValue();\n}\nfunction sum(array, cb, orient) {\n  var sum = 0;\n  var len = array.length;\n  var i = -1;\n  while (++i < len) {\n    var value = +cb(array[i], orient);\n    if (!isNaN(value)) {\n      sum += value;\n    }\n  }\n  return sum;\n}\n/**\r\n * Change the y-position of the nodes, except most the left side nodes\r\n */\nfunction relaxLeftToRight(nodesByBreadth, alpha, orient) {\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    zrUtil.each(nodes, function (node) {\n      if (node.inEdges.length) {\n        var y = sum(node.inEdges, weightedSource, orient) / sum(node.inEdges, getEdgeValue);\n        if (isNaN(y)) {\n          var len = node.inEdges.length;\n          y = len ? sum(node.inEdges, centerSource, orient) / len : 0;\n        }\n        if (orient === 'vertical') {\n          var nodeX = node.getLayout().x + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            x: nodeX\n          }, true);\n        } else {\n          var nodeY = node.getLayout().y + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            y: nodeY\n          }, true);\n        }\n      }\n    });\n  });\n}\n/**\r\n * Compute the depth(y-position) of each edge\r\n */\nfunction computeEdgeDepths(nodes, orient) {\n  var keyAttr = orient === 'vertical' ? 'x' : 'y';\n  zrUtil.each(nodes, function (node) {\n    node.outEdges.sort(function (a, b) {\n      return a.node2.getLayout()[keyAttr] - b.node2.getLayout()[keyAttr];\n    });\n    node.inEdges.sort(function (a, b) {\n      return a.node1.getLayout()[keyAttr] - b.node1.getLayout()[keyAttr];\n    });\n  });\n  zrUtil.each(nodes, function (node) {\n    var sy = 0;\n    var ty = 0;\n    zrUtil.each(node.outEdges, function (edge) {\n      edge.setLayout({\n        sy: sy\n      }, true);\n      sy += edge.getLayout().dy;\n    });\n    zrUtil.each(node.inEdges, function (edge) {\n      edge.setLayout({\n        ty: ty\n      }, true);\n      ty += edge.getLayout().dy;\n    });\n  });\n}", "map": {"version": 3, "names": ["layout", "zrUtil", "groupData", "sankeyLayout", "ecModel", "api", "eachSeriesByType", "seriesModel", "nodeWidth", "get", "nodeGap", "layoutInfo", "getViewRect", "width", "height", "graph", "getGraph", "nodes", "edges", "computeNodeValues", "filteredNodes", "filter", "node", "getLayout", "value", "iterations", "length", "orient", "nodeAlign", "layoutSankey", "getLayoutRect", "getBoxLayoutParams", "getWidth", "getHeight", "computeNodeBreadths", "computeNodeDepths", "computeEdgeDepths", "each", "value1", "sum", "outEdges", "getEdgeValue", "value2", "inEdges", "nodeRawValue", "getValue", "Math", "max", "setLayout", "remainEdges", "indegreeArr", "zeroIndegrees", "nextTargetNode", "x", "i", "push", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "idx", "item", "hostGraph", "data", "getRawDataItem", "dataIndex", "isItemDepth", "depth", "dy", "dx", "edgeIdx", "edge", "indexEdge", "indexOf", "targetNode", "node2", "nodeIndex", "Error", "max<PERSON><PERSON><PERSON>", "adjustNodeWithNodeAlign", "kx", "scaleNodeBreadths", "isNodeDepth", "nextSourceNode", "remainNodes", "nodeHeight", "skNodeHeight", "j", "node1", "moveSinksRight", "nodeDepth", "y", "nodesByBreadth", "prepareNodesByBreadth", "initializeNodeDepth", "resolveCollisions", "alpha", "relaxRightToLeft", "relaxLeftToRight", "keyAttr", "groupResult", "keys", "sort", "a", "b", "key", "buckets", "minKy", "Infinity", "n", "ky", "nodeDy", "edgeDy", "nodeX", "y0", "nodeDyAttr", "viewWidth", "slice", "reverse", "weightedTarget", "isNaN", "len", "centerTarget", "center", "nodeY", "weightedSource", "centerSource", "array", "cb", "sy", "ty"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/chart/sankey/sankeyLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as layout from '../../util/layout.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { groupData } from '../../util/model.js';\nexport default function sankeyLayout(ecModel, api) {\n  ecModel.eachSeriesByType('sankey', function (seriesModel) {\n    var nodeWidth = seriesModel.get('nodeWidth');\n    var nodeGap = seriesModel.get('nodeGap');\n    var layoutInfo = getViewRect(seriesModel, api);\n    seriesModel.layoutInfo = layoutInfo;\n    var width = layoutInfo.width;\n    var height = layoutInfo.height;\n    var graph = seriesModel.getGraph();\n    var nodes = graph.nodes;\n    var edges = graph.edges;\n    computeNodeValues(nodes);\n    var filteredNodes = zrUtil.filter(nodes, function (node) {\n      return node.getLayout().value === 0;\n    });\n    var iterations = filteredNodes.length !== 0 ? 0 : seriesModel.get('layoutIterations');\n    var orient = seriesModel.get('orient');\n    var nodeAlign = seriesModel.get('nodeAlign');\n    layoutSankey(nodes, edges, nodeWidth, nodeGap, width, height, iterations, orient, nodeAlign);\n  });\n}\n/**\r\n * Get the layout position of the whole view\r\n */\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nfunction layoutSankey(nodes, edges, nodeWidth, nodeGap, width, height, iterations, orient, nodeAlign) {\n  computeNodeBreadths(nodes, edges, nodeWidth, width, height, orient, nodeAlign);\n  computeNodeDepths(nodes, edges, height, width, nodeGap, iterations, orient);\n  computeEdgeDepths(nodes, orient);\n}\n/**\r\n * Compute the value of each node by summing the associated edge's value\r\n */\nfunction computeNodeValues(nodes) {\n  zrUtil.each(nodes, function (node) {\n    var value1 = sum(node.outEdges, getEdgeValue);\n    var value2 = sum(node.inEdges, getEdgeValue);\n    var nodeRawValue = node.getValue() || 0;\n    var value = Math.max(value1, value2, nodeRawValue);\n    node.setLayout({\n      value: value\n    }, true);\n  });\n}\n/**\r\n * Compute the x-position for each node.\r\n *\r\n * Here we use Kahn algorithm to detect cycle when we traverse\r\n * the node to computer the initial x position.\r\n */\nfunction computeNodeBreadths(nodes, edges, nodeWidth, width, height, orient, nodeAlign) {\n  // Used to mark whether the edge is deleted. if it is deleted,\n  // the value is 0, otherwise it is 1.\n  var remainEdges = [];\n  // Storage each node's indegree.\n  var indegreeArr = [];\n  // Used to storage the node with indegree is equal to 0.\n  var zeroIndegrees = [];\n  var nextTargetNode = [];\n  var x = 0;\n  // let kx = 0;\n  for (var i = 0; i < edges.length; i++) {\n    remainEdges[i] = 1;\n  }\n  for (var i = 0; i < nodes.length; i++) {\n    indegreeArr[i] = nodes[i].inEdges.length;\n    if (indegreeArr[i] === 0) {\n      zeroIndegrees.push(nodes[i]);\n    }\n  }\n  var maxNodeDepth = -1;\n  // Traversing nodes using topological sorting to calculate the\n  // horizontal(if orient === 'horizontal') or vertical(if orient === 'vertical')\n  // position of the nodes.\n  while (zeroIndegrees.length) {\n    for (var idx = 0; idx < zeroIndegrees.length; idx++) {\n      var node = zeroIndegrees[idx];\n      var item = node.hostGraph.data.getRawDataItem(node.dataIndex);\n      var isItemDepth = item.depth != null && item.depth >= 0;\n      if (isItemDepth && item.depth > maxNodeDepth) {\n        maxNodeDepth = item.depth;\n      }\n      node.setLayout({\n        depth: isItemDepth ? item.depth : x\n      }, true);\n      orient === 'vertical' ? node.setLayout({\n        dy: nodeWidth\n      }, true) : node.setLayout({\n        dx: nodeWidth\n      }, true);\n      for (var edgeIdx = 0; edgeIdx < node.outEdges.length; edgeIdx++) {\n        var edge = node.outEdges[edgeIdx];\n        var indexEdge = edges.indexOf(edge);\n        remainEdges[indexEdge] = 0;\n        var targetNode = edge.node2;\n        var nodeIndex = nodes.indexOf(targetNode);\n        if (--indegreeArr[nodeIndex] === 0 && nextTargetNode.indexOf(targetNode) < 0) {\n          nextTargetNode.push(targetNode);\n        }\n      }\n    }\n    ++x;\n    zeroIndegrees = nextTargetNode;\n    nextTargetNode = [];\n  }\n  for (var i = 0; i < remainEdges.length; i++) {\n    if (remainEdges[i] === 1) {\n      throw new Error('Sankey is a DAG, the original data has cycle!');\n    }\n  }\n  var maxDepth = maxNodeDepth > x - 1 ? maxNodeDepth : x - 1;\n  if (nodeAlign && nodeAlign !== 'left') {\n    adjustNodeWithNodeAlign(nodes, nodeAlign, orient, maxDepth);\n  }\n  var kx = orient === 'vertical' ? (height - nodeWidth) / maxDepth : (width - nodeWidth) / maxDepth;\n  scaleNodeBreadths(nodes, kx, orient);\n}\nfunction isNodeDepth(node) {\n  var item = node.hostGraph.data.getRawDataItem(node.dataIndex);\n  return item.depth != null && item.depth >= 0;\n}\nfunction adjustNodeWithNodeAlign(nodes, nodeAlign, orient, maxDepth) {\n  if (nodeAlign === 'right') {\n    var nextSourceNode = [];\n    var remainNodes = nodes;\n    var nodeHeight = 0;\n    while (remainNodes.length) {\n      for (var i = 0; i < remainNodes.length; i++) {\n        var node = remainNodes[i];\n        node.setLayout({\n          skNodeHeight: nodeHeight\n        }, true);\n        for (var j = 0; j < node.inEdges.length; j++) {\n          var edge = node.inEdges[j];\n          if (nextSourceNode.indexOf(edge.node1) < 0) {\n            nextSourceNode.push(edge.node1);\n          }\n        }\n      }\n      remainNodes = nextSourceNode;\n      nextSourceNode = [];\n      ++nodeHeight;\n    }\n    zrUtil.each(nodes, function (node) {\n      if (!isNodeDepth(node)) {\n        node.setLayout({\n          depth: Math.max(0, maxDepth - node.getLayout().skNodeHeight)\n        }, true);\n      }\n    });\n  } else if (nodeAlign === 'justify') {\n    moveSinksRight(nodes, maxDepth);\n  }\n}\n/**\r\n * All the node without outEgdes are assigned maximum x-position and\r\n *     be aligned in the last column.\r\n *\r\n * @param nodes.  node of sankey view.\r\n * @param maxDepth.  use to assign to node without outEdges as x-position.\r\n */\nfunction moveSinksRight(nodes, maxDepth) {\n  zrUtil.each(nodes, function (node) {\n    if (!isNodeDepth(node) && !node.outEdges.length) {\n      node.setLayout({\n        depth: maxDepth\n      }, true);\n    }\n  });\n}\n/**\r\n * Scale node x-position to the width\r\n *\r\n * @param nodes  node of sankey view\r\n * @param kx   multiple used to scale nodes\r\n */\nfunction scaleNodeBreadths(nodes, kx, orient) {\n  zrUtil.each(nodes, function (node) {\n    var nodeDepth = node.getLayout().depth * kx;\n    orient === 'vertical' ? node.setLayout({\n      y: nodeDepth\n    }, true) : node.setLayout({\n      x: nodeDepth\n    }, true);\n  });\n}\n/**\r\n * Using Gauss-Seidel iterations method to compute the node depth(y-position)\r\n *\r\n * @param nodes  node of sankey view\r\n * @param edges  edge of sankey view\r\n * @param height  the whole height of the area to draw the view\r\n * @param nodeGap  the vertical distance between two nodes\r\n *     in the same column.\r\n * @param iterations  the number of iterations for the algorithm\r\n */\nfunction computeNodeDepths(nodes, edges, height, width, nodeGap, iterations, orient) {\n  var nodesByBreadth = prepareNodesByBreadth(nodes, orient);\n  initializeNodeDepth(nodesByBreadth, edges, height, width, nodeGap, orient);\n  resolveCollisions(nodesByBreadth, nodeGap, height, width, orient);\n  for (var alpha = 1; iterations > 0; iterations--) {\n    // 0.99 is a experience parameter, ensure that each iterations of\n    // changes as small as possible.\n    alpha *= 0.99;\n    relaxRightToLeft(nodesByBreadth, alpha, orient);\n    resolveCollisions(nodesByBreadth, nodeGap, height, width, orient);\n    relaxLeftToRight(nodesByBreadth, alpha, orient);\n    resolveCollisions(nodesByBreadth, nodeGap, height, width, orient);\n  }\n}\nfunction prepareNodesByBreadth(nodes, orient) {\n  var nodesByBreadth = [];\n  var keyAttr = orient === 'vertical' ? 'y' : 'x';\n  var groupResult = groupData(nodes, function (node) {\n    return node.getLayout()[keyAttr];\n  });\n  groupResult.keys.sort(function (a, b) {\n    return a - b;\n  });\n  zrUtil.each(groupResult.keys, function (key) {\n    nodesByBreadth.push(groupResult.buckets.get(key));\n  });\n  return nodesByBreadth;\n}\n/**\r\n * Compute the original y-position for each node\r\n */\nfunction initializeNodeDepth(nodesByBreadth, edges, height, width, nodeGap, orient) {\n  var minKy = Infinity;\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    var n = nodes.length;\n    var sum = 0;\n    zrUtil.each(nodes, function (node) {\n      sum += node.getLayout().value;\n    });\n    var ky = orient === 'vertical' ? (width - (n - 1) * nodeGap) / sum : (height - (n - 1) * nodeGap) / sum;\n    if (ky < minKy) {\n      minKy = ky;\n    }\n  });\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    zrUtil.each(nodes, function (node, i) {\n      var nodeDy = node.getLayout().value * minKy;\n      if (orient === 'vertical') {\n        node.setLayout({\n          x: i\n        }, true);\n        node.setLayout({\n          dx: nodeDy\n        }, true);\n      } else {\n        node.setLayout({\n          y: i\n        }, true);\n        node.setLayout({\n          dy: nodeDy\n        }, true);\n      }\n    });\n  });\n  zrUtil.each(edges, function (edge) {\n    var edgeDy = +edge.getValue() * minKy;\n    edge.setLayout({\n      dy: edgeDy\n    }, true);\n  });\n}\n/**\r\n * Resolve the collision of initialized depth (y-position)\r\n */\nfunction resolveCollisions(nodesByBreadth, nodeGap, height, width, orient) {\n  var keyAttr = orient === 'vertical' ? 'x' : 'y';\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    nodes.sort(function (a, b) {\n      return a.getLayout()[keyAttr] - b.getLayout()[keyAttr];\n    });\n    var nodeX;\n    var node;\n    var dy;\n    var y0 = 0;\n    var n = nodes.length;\n    var nodeDyAttr = orient === 'vertical' ? 'dx' : 'dy';\n    for (var i = 0; i < n; i++) {\n      node = nodes[i];\n      dy = y0 - node.getLayout()[keyAttr];\n      if (dy > 0) {\n        nodeX = node.getLayout()[keyAttr] + dy;\n        orient === 'vertical' ? node.setLayout({\n          x: nodeX\n        }, true) : node.setLayout({\n          y: nodeX\n        }, true);\n      }\n      y0 = node.getLayout()[keyAttr] + node.getLayout()[nodeDyAttr] + nodeGap;\n    }\n    var viewWidth = orient === 'vertical' ? width : height;\n    // If the bottommost node goes outside the bounds, push it back up\n    dy = y0 - nodeGap - viewWidth;\n    if (dy > 0) {\n      nodeX = node.getLayout()[keyAttr] - dy;\n      orient === 'vertical' ? node.setLayout({\n        x: nodeX\n      }, true) : node.setLayout({\n        y: nodeX\n      }, true);\n      y0 = nodeX;\n      for (var i = n - 2; i >= 0; --i) {\n        node = nodes[i];\n        dy = node.getLayout()[keyAttr] + node.getLayout()[nodeDyAttr] + nodeGap - y0;\n        if (dy > 0) {\n          nodeX = node.getLayout()[keyAttr] - dy;\n          orient === 'vertical' ? node.setLayout({\n            x: nodeX\n          }, true) : node.setLayout({\n            y: nodeX\n          }, true);\n        }\n        y0 = node.getLayout()[keyAttr];\n      }\n    }\n  });\n}\n/**\r\n * Change the y-position of the nodes, except most the right side nodes\r\n * @param nodesByBreadth\r\n * @param alpha  parameter used to adjust the nodes y-position\r\n */\nfunction relaxRightToLeft(nodesByBreadth, alpha, orient) {\n  zrUtil.each(nodesByBreadth.slice().reverse(), function (nodes) {\n    zrUtil.each(nodes, function (node) {\n      if (node.outEdges.length) {\n        var y = sum(node.outEdges, weightedTarget, orient) / sum(node.outEdges, getEdgeValue);\n        if (isNaN(y)) {\n          var len = node.outEdges.length;\n          y = len ? sum(node.outEdges, centerTarget, orient) / len : 0;\n        }\n        if (orient === 'vertical') {\n          var nodeX = node.getLayout().x + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            x: nodeX\n          }, true);\n        } else {\n          var nodeY = node.getLayout().y + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            y: nodeY\n          }, true);\n        }\n      }\n    });\n  });\n}\nfunction weightedTarget(edge, orient) {\n  return center(edge.node2, orient) * edge.getValue();\n}\nfunction centerTarget(edge, orient) {\n  return center(edge.node2, orient);\n}\nfunction weightedSource(edge, orient) {\n  return center(edge.node1, orient) * edge.getValue();\n}\nfunction centerSource(edge, orient) {\n  return center(edge.node1, orient);\n}\nfunction center(node, orient) {\n  return orient === 'vertical' ? node.getLayout().x + node.getLayout().dx / 2 : node.getLayout().y + node.getLayout().dy / 2;\n}\nfunction getEdgeValue(edge) {\n  return edge.getValue();\n}\nfunction sum(array, cb, orient) {\n  var sum = 0;\n  var len = array.length;\n  var i = -1;\n  while (++i < len) {\n    var value = +cb(array[i], orient);\n    if (!isNaN(value)) {\n      sum += value;\n    }\n  }\n  return sum;\n}\n/**\r\n * Change the y-position of the nodes, except most the left side nodes\r\n */\nfunction relaxLeftToRight(nodesByBreadth, alpha, orient) {\n  zrUtil.each(nodesByBreadth, function (nodes) {\n    zrUtil.each(nodes, function (node) {\n      if (node.inEdges.length) {\n        var y = sum(node.inEdges, weightedSource, orient) / sum(node.inEdges, getEdgeValue);\n        if (isNaN(y)) {\n          var len = node.inEdges.length;\n          y = len ? sum(node.inEdges, centerSource, orient) / len : 0;\n        }\n        if (orient === 'vertical') {\n          var nodeX = node.getLayout().x + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            x: nodeX\n          }, true);\n        } else {\n          var nodeY = node.getLayout().y + (y - center(node, orient)) * alpha;\n          node.setLayout({\n            y: nodeY\n          }, true);\n        }\n      }\n    });\n  });\n}\n/**\r\n * Compute the depth(y-position) of each edge\r\n */\nfunction computeEdgeDepths(nodes, orient) {\n  var keyAttr = orient === 'vertical' ? 'x' : 'y';\n  zrUtil.each(nodes, function (node) {\n    node.outEdges.sort(function (a, b) {\n      return a.node2.getLayout()[keyAttr] - b.node2.getLayout()[keyAttr];\n    });\n    node.inEdges.sort(function (a, b) {\n      return a.node1.getLayout()[keyAttr] - b.node1.getLayout()[keyAttr];\n    });\n  });\n  zrUtil.each(nodes, function (node) {\n    var sy = 0;\n    var ty = 0;\n    zrUtil.each(node.outEdges, function (edge) {\n      edge.setLayout({\n        sy: sy\n      }, true);\n      sy += edge.getLayout().dy;\n    });\n    zrUtil.each(node.inEdges, function (edge) {\n      edge.setLayout({\n        ty: ty\n      }, true);\n      ty += edge.getLayout().dy;\n    });\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,sBAAsB;AAC9C,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAEC,GAAG,EAAE;EACjDD,OAAO,CAACE,gBAAgB,CAAC,QAAQ,EAAE,UAAUC,WAAW,EAAE;IACxD,IAAIC,SAAS,GAAGD,WAAW,CAACE,GAAG,CAAC,WAAW,CAAC;IAC5C,IAAIC,OAAO,GAAGH,WAAW,CAACE,GAAG,CAAC,SAAS,CAAC;IACxC,IAAIE,UAAU,GAAGC,WAAW,CAACL,WAAW,EAAEF,GAAG,CAAC;IAC9CE,WAAW,CAACI,UAAU,GAAGA,UAAU;IACnC,IAAIE,KAAK,GAAGF,UAAU,CAACE,KAAK;IAC5B,IAAIC,MAAM,GAAGH,UAAU,CAACG,MAAM;IAC9B,IAAIC,KAAK,GAAGR,WAAW,CAACS,QAAQ,CAAC,CAAC;IAClC,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACvB,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACvBC,iBAAiB,CAACF,KAAK,CAAC;IACxB,IAAIG,aAAa,GAAGnB,MAAM,CAACoB,MAAM,CAACJ,KAAK,EAAE,UAAUK,IAAI,EAAE;MACvD,OAAOA,IAAI,CAACC,SAAS,CAAC,CAAC,CAACC,KAAK,KAAK,CAAC;IACrC,CAAC,CAAC;IACF,IAAIC,UAAU,GAAGL,aAAa,CAACM,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGnB,WAAW,CAACE,GAAG,CAAC,kBAAkB,CAAC;IACrF,IAAIkB,MAAM,GAAGpB,WAAW,CAACE,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAImB,SAAS,GAAGrB,WAAW,CAACE,GAAG,CAAC,WAAW,CAAC;IAC5CoB,YAAY,CAACZ,KAAK,EAAEC,KAAK,EAAEV,SAAS,EAAEE,OAAO,EAAEG,KAAK,EAAEC,MAAM,EAAEW,UAAU,EAAEE,MAAM,EAAEC,SAAS,CAAC;EAC9F,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,SAAShB,WAAWA,CAACL,WAAW,EAAEF,GAAG,EAAE;EACrC,OAAOL,MAAM,CAAC8B,aAAa,CAACvB,WAAW,CAACwB,kBAAkB,CAAC,CAAC,EAAE;IAC5DlB,KAAK,EAAER,GAAG,CAAC2B,QAAQ,CAAC,CAAC;IACrBlB,MAAM,EAAET,GAAG,CAAC4B,SAAS,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,SAASJ,YAAYA,CAACZ,KAAK,EAAEC,KAAK,EAAEV,SAAS,EAAEE,OAAO,EAAEG,KAAK,EAAEC,MAAM,EAAEW,UAAU,EAAEE,MAAM,EAAEC,SAAS,EAAE;EACpGM,mBAAmB,CAACjB,KAAK,EAAEC,KAAK,EAAEV,SAAS,EAAEK,KAAK,EAAEC,MAAM,EAAEa,MAAM,EAAEC,SAAS,CAAC;EAC9EO,iBAAiB,CAAClB,KAAK,EAAEC,KAAK,EAAEJ,MAAM,EAAED,KAAK,EAAEH,OAAO,EAAEe,UAAU,EAAEE,MAAM,CAAC;EAC3ES,iBAAiB,CAACnB,KAAK,EAAEU,MAAM,CAAC;AAClC;AACA;AACA;AACA;AACA,SAASR,iBAAiBA,CAACF,KAAK,EAAE;EAChChB,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;IACjC,IAAIgB,MAAM,GAAGC,GAAG,CAACjB,IAAI,CAACkB,QAAQ,EAAEC,YAAY,CAAC;IAC7C,IAAIC,MAAM,GAAGH,GAAG,CAACjB,IAAI,CAACqB,OAAO,EAAEF,YAAY,CAAC;IAC5C,IAAIG,YAAY,GAAGtB,IAAI,CAACuB,QAAQ,CAAC,CAAC,IAAI,CAAC;IACvC,IAAIrB,KAAK,GAAGsB,IAAI,CAACC,GAAG,CAACT,MAAM,EAAEI,MAAM,EAAEE,YAAY,CAAC;IAClDtB,IAAI,CAAC0B,SAAS,CAAC;MACbxB,KAAK,EAAEA;IACT,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,mBAAmBA,CAACjB,KAAK,EAAEC,KAAK,EAAEV,SAAS,EAAEK,KAAK,EAAEC,MAAM,EAAEa,MAAM,EAAEC,SAAS,EAAE;EACtF;EACA;EACA,IAAIqB,WAAW,GAAG,EAAE;EACpB;EACA,IAAIC,WAAW,GAAG,EAAE;EACpB;EACA,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIC,CAAC,GAAG,CAAC;EACT;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,KAAK,CAACQ,MAAM,EAAE4B,CAAC,EAAE,EAAE;IACrCL,WAAW,CAACK,CAAC,CAAC,GAAG,CAAC;EACpB;EACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,KAAK,CAACS,MAAM,EAAE4B,CAAC,EAAE,EAAE;IACrCJ,WAAW,CAACI,CAAC,CAAC,GAAGrC,KAAK,CAACqC,CAAC,CAAC,CAACX,OAAO,CAACjB,MAAM;IACxC,IAAIwB,WAAW,CAACI,CAAC,CAAC,KAAK,CAAC,EAAE;MACxBH,aAAa,CAACI,IAAI,CAACtC,KAAK,CAACqC,CAAC,CAAC,CAAC;IAC9B;EACF;EACA,IAAIE,YAAY,GAAG,CAAC,CAAC;EACrB;EACA;EACA;EACA,OAAOL,aAAa,CAACzB,MAAM,EAAE;IAC3B,KAAK,IAAI+B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,aAAa,CAACzB,MAAM,EAAE+B,GAAG,EAAE,EAAE;MACnD,IAAInC,IAAI,GAAG6B,aAAa,CAACM,GAAG,CAAC;MAC7B,IAAIC,IAAI,GAAGpC,IAAI,CAACqC,SAAS,CAACC,IAAI,CAACC,cAAc,CAACvC,IAAI,CAACwC,SAAS,CAAC;MAC7D,IAAIC,WAAW,GAAGL,IAAI,CAACM,KAAK,IAAI,IAAI,IAAIN,IAAI,CAACM,KAAK,IAAI,CAAC;MACvD,IAAID,WAAW,IAAIL,IAAI,CAACM,KAAK,GAAGR,YAAY,EAAE;QAC5CA,YAAY,GAAGE,IAAI,CAACM,KAAK;MAC3B;MACA1C,IAAI,CAAC0B,SAAS,CAAC;QACbgB,KAAK,EAAED,WAAW,GAAGL,IAAI,CAACM,KAAK,GAAGX;MACpC,CAAC,EAAE,IAAI,CAAC;MACR1B,MAAM,KAAK,UAAU,GAAGL,IAAI,CAAC0B,SAAS,CAAC;QACrCiB,EAAE,EAAEzD;MACN,CAAC,EAAE,IAAI,CAAC,GAAGc,IAAI,CAAC0B,SAAS,CAAC;QACxBkB,EAAE,EAAE1D;MACN,CAAC,EAAE,IAAI,CAAC;MACR,KAAK,IAAI2D,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG7C,IAAI,CAACkB,QAAQ,CAACd,MAAM,EAAEyC,OAAO,EAAE,EAAE;QAC/D,IAAIC,IAAI,GAAG9C,IAAI,CAACkB,QAAQ,CAAC2B,OAAO,CAAC;QACjC,IAAIE,SAAS,GAAGnD,KAAK,CAACoD,OAAO,CAACF,IAAI,CAAC;QACnCnB,WAAW,CAACoB,SAAS,CAAC,GAAG,CAAC;QAC1B,IAAIE,UAAU,GAAGH,IAAI,CAACI,KAAK;QAC3B,IAAIC,SAAS,GAAGxD,KAAK,CAACqD,OAAO,CAACC,UAAU,CAAC;QACzC,IAAI,EAAErB,WAAW,CAACuB,SAAS,CAAC,KAAK,CAAC,IAAIrB,cAAc,CAACkB,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;UAC5EnB,cAAc,CAACG,IAAI,CAACgB,UAAU,CAAC;QACjC;MACF;IACF;IACA,EAAElB,CAAC;IACHF,aAAa,GAAGC,cAAc;IAC9BA,cAAc,GAAG,EAAE;EACrB;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACvB,MAAM,EAAE4B,CAAC,EAAE,EAAE;IAC3C,IAAIL,WAAW,CAACK,CAAC,CAAC,KAAK,CAAC,EAAE;MACxB,MAAM,IAAIoB,KAAK,CAAC,+CAA+C,CAAC;IAClE;EACF;EACA,IAAIC,QAAQ,GAAGnB,YAAY,GAAGH,CAAC,GAAG,CAAC,GAAGG,YAAY,GAAGH,CAAC,GAAG,CAAC;EAC1D,IAAIzB,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;IACrCgD,uBAAuB,CAAC3D,KAAK,EAAEW,SAAS,EAAED,MAAM,EAAEgD,QAAQ,CAAC;EAC7D;EACA,IAAIE,EAAE,GAAGlD,MAAM,KAAK,UAAU,GAAG,CAACb,MAAM,GAAGN,SAAS,IAAImE,QAAQ,GAAG,CAAC9D,KAAK,GAAGL,SAAS,IAAImE,QAAQ;EACjGG,iBAAiB,CAAC7D,KAAK,EAAE4D,EAAE,EAAElD,MAAM,CAAC;AACtC;AACA,SAASoD,WAAWA,CAACzD,IAAI,EAAE;EACzB,IAAIoC,IAAI,GAAGpC,IAAI,CAACqC,SAAS,CAACC,IAAI,CAACC,cAAc,CAACvC,IAAI,CAACwC,SAAS,CAAC;EAC7D,OAAOJ,IAAI,CAACM,KAAK,IAAI,IAAI,IAAIN,IAAI,CAACM,KAAK,IAAI,CAAC;AAC9C;AACA,SAASY,uBAAuBA,CAAC3D,KAAK,EAAEW,SAAS,EAAED,MAAM,EAAEgD,QAAQ,EAAE;EACnE,IAAI/C,SAAS,KAAK,OAAO,EAAE;IACzB,IAAIoD,cAAc,GAAG,EAAE;IACvB,IAAIC,WAAW,GAAGhE,KAAK;IACvB,IAAIiE,UAAU,GAAG,CAAC;IAClB,OAAOD,WAAW,CAACvD,MAAM,EAAE;MACzB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,WAAW,CAACvD,MAAM,EAAE4B,CAAC,EAAE,EAAE;QAC3C,IAAIhC,IAAI,GAAG2D,WAAW,CAAC3B,CAAC,CAAC;QACzBhC,IAAI,CAAC0B,SAAS,CAAC;UACbmC,YAAY,EAAED;QAChB,CAAC,EAAE,IAAI,CAAC;QACR,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9D,IAAI,CAACqB,OAAO,CAACjB,MAAM,EAAE0D,CAAC,EAAE,EAAE;UAC5C,IAAIhB,IAAI,GAAG9C,IAAI,CAACqB,OAAO,CAACyC,CAAC,CAAC;UAC1B,IAAIJ,cAAc,CAACV,OAAO,CAACF,IAAI,CAACiB,KAAK,CAAC,GAAG,CAAC,EAAE;YAC1CL,cAAc,CAACzB,IAAI,CAACa,IAAI,CAACiB,KAAK,CAAC;UACjC;QACF;MACF;MACAJ,WAAW,GAAGD,cAAc;MAC5BA,cAAc,GAAG,EAAE;MACnB,EAAEE,UAAU;IACd;IACAjF,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;MACjC,IAAI,CAACyD,WAAW,CAACzD,IAAI,CAAC,EAAE;QACtBA,IAAI,CAAC0B,SAAS,CAAC;UACbgB,KAAK,EAAElB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE4B,QAAQ,GAAGrD,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC4D,YAAY;QAC7D,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIvD,SAAS,KAAK,SAAS,EAAE;IAClC0D,cAAc,CAACrE,KAAK,EAAE0D,QAAQ,CAAC;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,cAAcA,CAACrE,KAAK,EAAE0D,QAAQ,EAAE;EACvC1E,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;IACjC,IAAI,CAACyD,WAAW,CAACzD,IAAI,CAAC,IAAI,CAACA,IAAI,CAACkB,QAAQ,CAACd,MAAM,EAAE;MAC/CJ,IAAI,CAAC0B,SAAS,CAAC;QACbgB,KAAK,EAAEW;MACT,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,iBAAiBA,CAAC7D,KAAK,EAAE4D,EAAE,EAAElD,MAAM,EAAE;EAC5C1B,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;IACjC,IAAIiE,SAAS,GAAGjE,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyC,KAAK,GAAGa,EAAE;IAC3ClD,MAAM,KAAK,UAAU,GAAGL,IAAI,CAAC0B,SAAS,CAAC;MACrCwC,CAAC,EAAED;IACL,CAAC,EAAE,IAAI,CAAC,GAAGjE,IAAI,CAAC0B,SAAS,CAAC;MACxBK,CAAC,EAAEkC;IACL,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASpD,iBAAiBA,CAAClB,KAAK,EAAEC,KAAK,EAAEJ,MAAM,EAAED,KAAK,EAAEH,OAAO,EAAEe,UAAU,EAAEE,MAAM,EAAE;EACnF,IAAI8D,cAAc,GAAGC,qBAAqB,CAACzE,KAAK,EAAEU,MAAM,CAAC;EACzDgE,mBAAmB,CAACF,cAAc,EAAEvE,KAAK,EAAEJ,MAAM,EAAED,KAAK,EAAEH,OAAO,EAAEiB,MAAM,CAAC;EAC1EiE,iBAAiB,CAACH,cAAc,EAAE/E,OAAO,EAAEI,MAAM,EAAED,KAAK,EAAEc,MAAM,CAAC;EACjE,KAAK,IAAIkE,KAAK,GAAG,CAAC,EAAEpE,UAAU,GAAG,CAAC,EAAEA,UAAU,EAAE,EAAE;IAChD;IACA;IACAoE,KAAK,IAAI,IAAI;IACbC,gBAAgB,CAACL,cAAc,EAAEI,KAAK,EAAElE,MAAM,CAAC;IAC/CiE,iBAAiB,CAACH,cAAc,EAAE/E,OAAO,EAAEI,MAAM,EAAED,KAAK,EAAEc,MAAM,CAAC;IACjEoE,gBAAgB,CAACN,cAAc,EAAEI,KAAK,EAAElE,MAAM,CAAC;IAC/CiE,iBAAiB,CAACH,cAAc,EAAE/E,OAAO,EAAEI,MAAM,EAAED,KAAK,EAAEc,MAAM,CAAC;EACnE;AACF;AACA,SAAS+D,qBAAqBA,CAACzE,KAAK,EAAEU,MAAM,EAAE;EAC5C,IAAI8D,cAAc,GAAG,EAAE;EACvB,IAAIO,OAAO,GAAGrE,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,GAAG;EAC/C,IAAIsE,WAAW,GAAG/F,SAAS,CAACe,KAAK,EAAE,UAAUK,IAAI,EAAE;IACjD,OAAOA,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC;EAClC,CAAC,CAAC;EACFC,WAAW,CAACC,IAAI,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACpC,OAAOD,CAAC,GAAGC,CAAC;EACd,CAAC,CAAC;EACFpG,MAAM,CAACoC,IAAI,CAAC4D,WAAW,CAACC,IAAI,EAAE,UAAUI,GAAG,EAAE;IAC3Cb,cAAc,CAAClC,IAAI,CAAC0C,WAAW,CAACM,OAAO,CAAC9F,GAAG,CAAC6F,GAAG,CAAC,CAAC;EACnD,CAAC,CAAC;EACF,OAAOb,cAAc;AACvB;AACA;AACA;AACA;AACA,SAASE,mBAAmBA,CAACF,cAAc,EAAEvE,KAAK,EAAEJ,MAAM,EAAED,KAAK,EAAEH,OAAO,EAAEiB,MAAM,EAAE;EAClF,IAAI6E,KAAK,GAAGC,QAAQ;EACpBxG,MAAM,CAACoC,IAAI,CAACoD,cAAc,EAAE,UAAUxE,KAAK,EAAE;IAC3C,IAAIyF,CAAC,GAAGzF,KAAK,CAACS,MAAM;IACpB,IAAIa,GAAG,GAAG,CAAC;IACXtC,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;MACjCiB,GAAG,IAAIjB,IAAI,CAACC,SAAS,CAAC,CAAC,CAACC,KAAK;IAC/B,CAAC,CAAC;IACF,IAAImF,EAAE,GAAGhF,MAAM,KAAK,UAAU,GAAG,CAACd,KAAK,GAAG,CAAC6F,CAAC,GAAG,CAAC,IAAIhG,OAAO,IAAI6B,GAAG,GAAG,CAACzB,MAAM,GAAG,CAAC4F,CAAC,GAAG,CAAC,IAAIhG,OAAO,IAAI6B,GAAG;IACvG,IAAIoE,EAAE,GAAGH,KAAK,EAAE;MACdA,KAAK,GAAGG,EAAE;IACZ;EACF,CAAC,CAAC;EACF1G,MAAM,CAACoC,IAAI,CAACoD,cAAc,EAAE,UAAUxE,KAAK,EAAE;IAC3ChB,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAEgC,CAAC,EAAE;MACpC,IAAIsD,MAAM,GAAGtF,IAAI,CAACC,SAAS,CAAC,CAAC,CAACC,KAAK,GAAGgF,KAAK;MAC3C,IAAI7E,MAAM,KAAK,UAAU,EAAE;QACzBL,IAAI,CAAC0B,SAAS,CAAC;UACbK,CAAC,EAAEC;QACL,CAAC,EAAE,IAAI,CAAC;QACRhC,IAAI,CAAC0B,SAAS,CAAC;UACbkB,EAAE,EAAE0C;QACN,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLtF,IAAI,CAAC0B,SAAS,CAAC;UACbwC,CAAC,EAAElC;QACL,CAAC,EAAE,IAAI,CAAC;QACRhC,IAAI,CAAC0B,SAAS,CAAC;UACbiB,EAAE,EAAE2C;QACN,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF3G,MAAM,CAACoC,IAAI,CAACnB,KAAK,EAAE,UAAUkD,IAAI,EAAE;IACjC,IAAIyC,MAAM,GAAG,CAACzC,IAAI,CAACvB,QAAQ,CAAC,CAAC,GAAG2D,KAAK;IACrCpC,IAAI,CAACpB,SAAS,CAAC;MACbiB,EAAE,EAAE4C;IACN,CAAC,EAAE,IAAI,CAAC;EACV,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,SAASjB,iBAAiBA,CAACH,cAAc,EAAE/E,OAAO,EAAEI,MAAM,EAAED,KAAK,EAAEc,MAAM,EAAE;EACzE,IAAIqE,OAAO,GAAGrE,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,GAAG;EAC/C1B,MAAM,CAACoC,IAAI,CAACoD,cAAc,EAAE,UAAUxE,KAAK,EAAE;IAC3CA,KAAK,CAACkF,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACzB,OAAOD,CAAC,CAAC7E,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAGK,CAAC,CAAC9E,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC;IACxD,CAAC,CAAC;IACF,IAAIc,KAAK;IACT,IAAIxF,IAAI;IACR,IAAI2C,EAAE;IACN,IAAI8C,EAAE,GAAG,CAAC;IACV,IAAIL,CAAC,GAAGzF,KAAK,CAACS,MAAM;IACpB,IAAIsF,UAAU,GAAGrF,MAAM,KAAK,UAAU,GAAG,IAAI,GAAG,IAAI;IACpD,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,CAAC,EAAEpD,CAAC,EAAE,EAAE;MAC1BhC,IAAI,GAAGL,KAAK,CAACqC,CAAC,CAAC;MACfW,EAAE,GAAG8C,EAAE,GAAGzF,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC;MACnC,IAAI/B,EAAE,GAAG,CAAC,EAAE;QACV6C,KAAK,GAAGxF,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAG/B,EAAE;QACtCtC,MAAM,KAAK,UAAU,GAAGL,IAAI,CAAC0B,SAAS,CAAC;UACrCK,CAAC,EAAEyD;QACL,CAAC,EAAE,IAAI,CAAC,GAAGxF,IAAI,CAAC0B,SAAS,CAAC;UACxBwC,CAAC,EAAEsB;QACL,CAAC,EAAE,IAAI,CAAC;MACV;MACAC,EAAE,GAAGzF,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAG1E,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyF,UAAU,CAAC,GAAGtG,OAAO;IACzE;IACA,IAAIuG,SAAS,GAAGtF,MAAM,KAAK,UAAU,GAAGd,KAAK,GAAGC,MAAM;IACtD;IACAmD,EAAE,GAAG8C,EAAE,GAAGrG,OAAO,GAAGuG,SAAS;IAC7B,IAAIhD,EAAE,GAAG,CAAC,EAAE;MACV6C,KAAK,GAAGxF,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAG/B,EAAE;MACtCtC,MAAM,KAAK,UAAU,GAAGL,IAAI,CAAC0B,SAAS,CAAC;QACrCK,CAAC,EAAEyD;MACL,CAAC,EAAE,IAAI,CAAC,GAAGxF,IAAI,CAAC0B,SAAS,CAAC;QACxBwC,CAAC,EAAEsB;MACL,CAAC,EAAE,IAAI,CAAC;MACRC,EAAE,GAAGD,KAAK;MACV,KAAK,IAAIxD,CAAC,GAAGoD,CAAC,GAAG,CAAC,EAAEpD,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC/BhC,IAAI,GAAGL,KAAK,CAACqC,CAAC,CAAC;QACfW,EAAE,GAAG3C,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAG1E,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyF,UAAU,CAAC,GAAGtG,OAAO,GAAGqG,EAAE;QAC5E,IAAI9C,EAAE,GAAG,CAAC,EAAE;UACV6C,KAAK,GAAGxF,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAG/B,EAAE;UACtCtC,MAAM,KAAK,UAAU,GAAGL,IAAI,CAAC0B,SAAS,CAAC;YACrCK,CAAC,EAAEyD;UACL,CAAC,EAAE,IAAI,CAAC,GAAGxF,IAAI,CAAC0B,SAAS,CAAC;YACxBwC,CAAC,EAAEsB;UACL,CAAC,EAAE,IAAI,CAAC;QACV;QACAC,EAAE,GAAGzF,IAAI,CAACC,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC;MAChC;IACF;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,gBAAgBA,CAACL,cAAc,EAAEI,KAAK,EAAElE,MAAM,EAAE;EACvD1B,MAAM,CAACoC,IAAI,CAACoD,cAAc,CAACyB,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE,UAAUlG,KAAK,EAAE;IAC7DhB,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;MACjC,IAAIA,IAAI,CAACkB,QAAQ,CAACd,MAAM,EAAE;QACxB,IAAI8D,CAAC,GAAGjD,GAAG,CAACjB,IAAI,CAACkB,QAAQ,EAAE4E,cAAc,EAAEzF,MAAM,CAAC,GAAGY,GAAG,CAACjB,IAAI,CAACkB,QAAQ,EAAEC,YAAY,CAAC;QACrF,IAAI4E,KAAK,CAAC7B,CAAC,CAAC,EAAE;UACZ,IAAI8B,GAAG,GAAGhG,IAAI,CAACkB,QAAQ,CAACd,MAAM;UAC9B8D,CAAC,GAAG8B,GAAG,GAAG/E,GAAG,CAACjB,IAAI,CAACkB,QAAQ,EAAE+E,YAAY,EAAE5F,MAAM,CAAC,GAAG2F,GAAG,GAAG,CAAC;QAC9D;QACA,IAAI3F,MAAM,KAAK,UAAU,EAAE;UACzB,IAAImF,KAAK,GAAGxF,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC8B,CAAC,GAAG,CAACmC,CAAC,GAAGgC,MAAM,CAAClG,IAAI,EAAEK,MAAM,CAAC,IAAIkE,KAAK;UACnEvE,IAAI,CAAC0B,SAAS,CAAC;YACbK,CAAC,EAAEyD;UACL,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAIW,KAAK,GAAGnG,IAAI,CAACC,SAAS,CAAC,CAAC,CAACiE,CAAC,GAAG,CAACA,CAAC,GAAGgC,MAAM,CAAClG,IAAI,EAAEK,MAAM,CAAC,IAAIkE,KAAK;UACnEvE,IAAI,CAAC0B,SAAS,CAAC;YACbwC,CAAC,EAAEiC;UACL,CAAC,EAAE,IAAI,CAAC;QACV;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASL,cAAcA,CAAChD,IAAI,EAAEzC,MAAM,EAAE;EACpC,OAAO6F,MAAM,CAACpD,IAAI,CAACI,KAAK,EAAE7C,MAAM,CAAC,GAAGyC,IAAI,CAACvB,QAAQ,CAAC,CAAC;AACrD;AACA,SAAS0E,YAAYA,CAACnD,IAAI,EAAEzC,MAAM,EAAE;EAClC,OAAO6F,MAAM,CAACpD,IAAI,CAACI,KAAK,EAAE7C,MAAM,CAAC;AACnC;AACA,SAAS+F,cAAcA,CAACtD,IAAI,EAAEzC,MAAM,EAAE;EACpC,OAAO6F,MAAM,CAACpD,IAAI,CAACiB,KAAK,EAAE1D,MAAM,CAAC,GAAGyC,IAAI,CAACvB,QAAQ,CAAC,CAAC;AACrD;AACA,SAAS8E,YAAYA,CAACvD,IAAI,EAAEzC,MAAM,EAAE;EAClC,OAAO6F,MAAM,CAACpD,IAAI,CAACiB,KAAK,EAAE1D,MAAM,CAAC;AACnC;AACA,SAAS6F,MAAMA,CAAClG,IAAI,EAAEK,MAAM,EAAE;EAC5B,OAAOA,MAAM,KAAK,UAAU,GAAGL,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC8B,CAAC,GAAG/B,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC2C,EAAE,GAAG,CAAC,GAAG5C,IAAI,CAACC,SAAS,CAAC,CAAC,CAACiE,CAAC,GAAGlE,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC0C,EAAE,GAAG,CAAC;AAC5H;AACA,SAASxB,YAAYA,CAAC2B,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACvB,QAAQ,CAAC,CAAC;AACxB;AACA,SAASN,GAAGA,CAACqF,KAAK,EAAEC,EAAE,EAAElG,MAAM,EAAE;EAC9B,IAAIY,GAAG,GAAG,CAAC;EACX,IAAI+E,GAAG,GAAGM,KAAK,CAAClG,MAAM;EACtB,IAAI4B,CAAC,GAAG,CAAC,CAAC;EACV,OAAO,EAAEA,CAAC,GAAGgE,GAAG,EAAE;IAChB,IAAI9F,KAAK,GAAG,CAACqG,EAAE,CAACD,KAAK,CAACtE,CAAC,CAAC,EAAE3B,MAAM,CAAC;IACjC,IAAI,CAAC0F,KAAK,CAAC7F,KAAK,CAAC,EAAE;MACjBe,GAAG,IAAIf,KAAK;IACd;EACF;EACA,OAAOe,GAAG;AACZ;AACA;AACA;AACA;AACA,SAASwD,gBAAgBA,CAACN,cAAc,EAAEI,KAAK,EAAElE,MAAM,EAAE;EACvD1B,MAAM,CAACoC,IAAI,CAACoD,cAAc,EAAE,UAAUxE,KAAK,EAAE;IAC3ChB,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;MACjC,IAAIA,IAAI,CAACqB,OAAO,CAACjB,MAAM,EAAE;QACvB,IAAI8D,CAAC,GAAGjD,GAAG,CAACjB,IAAI,CAACqB,OAAO,EAAE+E,cAAc,EAAE/F,MAAM,CAAC,GAAGY,GAAG,CAACjB,IAAI,CAACqB,OAAO,EAAEF,YAAY,CAAC;QACnF,IAAI4E,KAAK,CAAC7B,CAAC,CAAC,EAAE;UACZ,IAAI8B,GAAG,GAAGhG,IAAI,CAACqB,OAAO,CAACjB,MAAM;UAC7B8D,CAAC,GAAG8B,GAAG,GAAG/E,GAAG,CAACjB,IAAI,CAACqB,OAAO,EAAEgF,YAAY,EAAEhG,MAAM,CAAC,GAAG2F,GAAG,GAAG,CAAC;QAC7D;QACA,IAAI3F,MAAM,KAAK,UAAU,EAAE;UACzB,IAAImF,KAAK,GAAGxF,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC8B,CAAC,GAAG,CAACmC,CAAC,GAAGgC,MAAM,CAAClG,IAAI,EAAEK,MAAM,CAAC,IAAIkE,KAAK;UACnEvE,IAAI,CAAC0B,SAAS,CAAC;YACbK,CAAC,EAAEyD;UACL,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,MAAM;UACL,IAAIW,KAAK,GAAGnG,IAAI,CAACC,SAAS,CAAC,CAAC,CAACiE,CAAC,GAAG,CAACA,CAAC,GAAGgC,MAAM,CAAClG,IAAI,EAAEK,MAAM,CAAC,IAAIkE,KAAK;UACnEvE,IAAI,CAAC0B,SAAS,CAAC;YACbwC,CAAC,EAAEiC;UACL,CAAC,EAAE,IAAI,CAAC;QACV;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA,SAASrF,iBAAiBA,CAACnB,KAAK,EAAEU,MAAM,EAAE;EACxC,IAAIqE,OAAO,GAAGrE,MAAM,KAAK,UAAU,GAAG,GAAG,GAAG,GAAG;EAC/C1B,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;IACjCA,IAAI,CAACkB,QAAQ,CAAC2D,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACjC,OAAOD,CAAC,CAAC5B,KAAK,CAACjD,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAGK,CAAC,CAAC7B,KAAK,CAACjD,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC;IACpE,CAAC,CAAC;IACF1E,IAAI,CAACqB,OAAO,CAACwD,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAChC,OAAOD,CAAC,CAACf,KAAK,CAAC9D,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC,GAAGK,CAAC,CAAChB,KAAK,CAAC9D,SAAS,CAAC,CAAC,CAACyE,OAAO,CAAC;IACpE,CAAC,CAAC;EACJ,CAAC,CAAC;EACF/F,MAAM,CAACoC,IAAI,CAACpB,KAAK,EAAE,UAAUK,IAAI,EAAE;IACjC,IAAIwG,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV9H,MAAM,CAACoC,IAAI,CAACf,IAAI,CAACkB,QAAQ,EAAE,UAAU4B,IAAI,EAAE;MACzCA,IAAI,CAACpB,SAAS,CAAC;QACb8E,EAAE,EAAEA;MACN,CAAC,EAAE,IAAI,CAAC;MACRA,EAAE,IAAI1D,IAAI,CAAC7C,SAAS,CAAC,CAAC,CAAC0C,EAAE;IAC3B,CAAC,CAAC;IACFhE,MAAM,CAACoC,IAAI,CAACf,IAAI,CAACqB,OAAO,EAAE,UAAUyB,IAAI,EAAE;MACxCA,IAAI,CAACpB,SAAS,CAAC;QACb+E,EAAE,EAAEA;MACN,CAAC,EAAE,IAAI,CAAC;MACRA,EAAE,IAAI3D,IAAI,CAAC7C,SAAS,CAAC,CAAC,CAAC0C,EAAE;IAC3B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}