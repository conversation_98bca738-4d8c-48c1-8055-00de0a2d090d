{"ast": null, "code": "import { normalizeRadian } from './util.js';\nvar PI2 = Math.PI * 2;\nexport function containStroke(cx, cy, r, startAngle, endAngle, anticlockwise, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  x -= cx;\n  y -= cy;\n  var d = Math.sqrt(x * x + y * y);\n  if (d - _l > r || d + _l < r) {\n    return false;\n  }\n  if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n    return true;\n  }\n  if (anticlockwise) {\n    var tmp = startAngle;\n    startAngle = normalizeRadian(endAngle);\n    endAngle = normalizeRadian(tmp);\n  } else {\n    startAngle = normalizeRadian(startAngle);\n    endAngle = normalizeRadian(endAngle);\n  }\n  if (startAngle > endAngle) {\n    endAngle += PI2;\n  }\n  var angle = Math.atan2(y, x);\n  if (angle < 0) {\n    angle += PI2;\n  }\n  return angle >= startAngle && angle <= endAngle || angle + PI2 >= startAngle && angle + PI2 <= endAngle;\n}", "map": {"version": 3, "names": ["normalizeRadian", "PI2", "Math", "PI", "containStroke", "cx", "cy", "r", "startAngle", "endAngle", "anticlockwise", "lineWidth", "x", "y", "_l", "d", "sqrt", "abs", "tmp", "angle", "atan2"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/zrender/lib/contain/arc.js"], "sourcesContent": ["import { normalizeRadian } from './util.js';\nvar PI2 = Math.PI * 2;\nexport function containStroke(cx, cy, r, startAngle, endAngle, anticlockwise, lineWidth, x, y) {\n    if (lineWidth === 0) {\n        return false;\n    }\n    var _l = lineWidth;\n    x -= cx;\n    y -= cy;\n    var d = Math.sqrt(x * x + y * y);\n    if ((d - _l > r) || (d + _l < r)) {\n        return false;\n    }\n    if (Math.abs(startAngle - endAngle) % PI2 < 1e-4) {\n        return true;\n    }\n    if (anticlockwise) {\n        var tmp = startAngle;\n        startAngle = normalizeRadian(endAngle);\n        endAngle = normalizeRadian(tmp);\n    }\n    else {\n        startAngle = normalizeRadian(startAngle);\n        endAngle = normalizeRadian(endAngle);\n    }\n    if (startAngle > endAngle) {\n        endAngle += PI2;\n    }\n    var angle = Math.atan2(y, x);\n    if (angle < 0) {\n        angle += PI2;\n    }\n    return (angle >= startAngle && angle <= endAngle)\n        || (angle + PI2 >= startAngle && angle + PI2 <= endAngle);\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,WAAW;AAC3C,IAAIC,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;AACrB,OAAO,SAASC,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,SAAS,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3F,IAAIF,SAAS,KAAK,CAAC,EAAE;IACjB,OAAO,KAAK;EAChB;EACA,IAAIG,EAAE,GAAGH,SAAS;EAClBC,CAAC,IAAIP,EAAE;EACPQ,CAAC,IAAIP,EAAE;EACP,IAAIS,CAAC,GAAGb,IAAI,CAACc,IAAI,CAACJ,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;EAChC,IAAKE,CAAC,GAAGD,EAAE,GAAGP,CAAC,IAAMQ,CAAC,GAAGD,EAAE,GAAGP,CAAE,EAAE;IAC9B,OAAO,KAAK;EAChB;EACA,IAAIL,IAAI,CAACe,GAAG,CAACT,UAAU,GAAGC,QAAQ,CAAC,GAAGR,GAAG,GAAG,IAAI,EAAE;IAC9C,OAAO,IAAI;EACf;EACA,IAAIS,aAAa,EAAE;IACf,IAAIQ,GAAG,GAAGV,UAAU;IACpBA,UAAU,GAAGR,eAAe,CAACS,QAAQ,CAAC;IACtCA,QAAQ,GAAGT,eAAe,CAACkB,GAAG,CAAC;EACnC,CAAC,MACI;IACDV,UAAU,GAAGR,eAAe,CAACQ,UAAU,CAAC;IACxCC,QAAQ,GAAGT,eAAe,CAACS,QAAQ,CAAC;EACxC;EACA,IAAID,UAAU,GAAGC,QAAQ,EAAE;IACvBA,QAAQ,IAAIR,GAAG;EACnB;EACA,IAAIkB,KAAK,GAAGjB,IAAI,CAACkB,KAAK,CAACP,CAAC,EAAED,CAAC,CAAC;EAC5B,IAAIO,KAAK,GAAG,CAAC,EAAE;IACXA,KAAK,IAAIlB,GAAG;EAChB;EACA,OAAQkB,KAAK,IAAIX,UAAU,IAAIW,KAAK,IAAIV,QAAQ,IACxCU,KAAK,GAAGlB,GAAG,IAAIO,UAAU,IAAIW,KAAK,GAAGlB,GAAG,IAAIQ,QAAS;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}