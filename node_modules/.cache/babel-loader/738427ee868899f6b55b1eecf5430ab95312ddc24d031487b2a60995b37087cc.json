{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global window, Uint8Array, document */\nimport env from 'zrender/lib/core/env.js';\nimport { ToolboxFeature } from '../featureManager.js';\nvar SaveAsImage = /** @class */function (_super) {\n  __extends(SaveAsImage, _super);\n  function SaveAsImage() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  SaveAsImage.prototype.onclick = function (ecModel, api) {\n    var model = this.model;\n    var title = model.get('name') || ecModel.get('title.0.text') || 'echarts';\n    var isSvg = api.getZr().painter.getType() === 'svg';\n    var type = isSvg ? 'svg' : model.get('type', true) || 'png';\n    var url = api.getConnectedDataURL({\n      type: type,\n      backgroundColor: model.get('backgroundColor', true) || ecModel.get('backgroundColor') || '#fff',\n      connectedBackgroundColor: model.get('connectedBackgroundColor'),\n      excludeComponents: model.get('excludeComponents'),\n      pixelRatio: model.get('pixelRatio')\n    });\n    var browser = env.browser;\n    // Chrome, Firefox, New Edge\n    if (typeof MouseEvent === 'function' && (browser.newEdge || !browser.ie && !browser.edge)) {\n      var $a = document.createElement('a');\n      $a.download = title + '.' + type;\n      $a.target = '_blank';\n      $a.href = url;\n      var evt = new MouseEvent('click', {\n        // some micro front-end framework， window maybe is a Proxy\n        view: document.defaultView,\n        bubbles: true,\n        cancelable: false\n      });\n      $a.dispatchEvent(evt);\n    }\n    // IE or old Edge\n    else {\n      // @ts-ignore\n      if (window.navigator.msSaveOrOpenBlob || isSvg) {\n        var parts = url.split(',');\n        // data:[<mime type>][;charset=<charset>][;base64],<encoded data>\n        var base64Encoded = parts[0].indexOf('base64') > -1;\n        var bstr = isSvg\n        // should decode the svg data uri first\n        ? decodeURIComponent(parts[1]) : parts[1];\n        // only `atob` when the data uri is encoded with base64\n        // otherwise, like `svg` data uri exported by zrender,\n        // there will be an error, for it's not encoded with base64.\n        // (just a url-encoded string through `encodeURIComponent`)\n        base64Encoded && (bstr = window.atob(bstr));\n        var filename = title + '.' + type;\n        // @ts-ignore\n        if (window.navigator.msSaveOrOpenBlob) {\n          var n = bstr.length;\n          var u8arr = new Uint8Array(n);\n          while (n--) {\n            u8arr[n] = bstr.charCodeAt(n);\n          }\n          var blob = new Blob([u8arr]); // @ts-ignore\n          window.navigator.msSaveOrOpenBlob(blob, filename);\n        } else {\n          var frame = document.createElement('iframe');\n          document.body.appendChild(frame);\n          var cw = frame.contentWindow;\n          var doc = cw.document;\n          doc.open('image/svg+xml', 'replace');\n          doc.write(bstr);\n          doc.close();\n          cw.focus();\n          doc.execCommand('SaveAs', true, filename);\n          document.body.removeChild(frame);\n        }\n      } else {\n        var lang = model.get('lang');\n        var html = '' + '<body style=\"margin:0;\">' + '<img src=\"' + url + '\" style=\"max-width:100%;\" title=\"' + (lang && lang[0] || '') + '\" />' + '</body>';\n        var tab = window.open();\n        tab.document.write(html);\n        tab.document.title = title;\n      }\n    }\n  };\n  SaveAsImage.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      icon: 'M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0',\n      title: ecModel.getLocaleModel().get(['toolbox', 'saveAsImage', 'title']),\n      type: 'png',\n      // Default use option.backgroundColor\n      // backgroundColor: '#fff',\n      connectedBackgroundColor: '#fff',\n      name: '',\n      excludeComponents: ['toolbox'],\n      // use current pixel ratio of device by default\n      // pixelRatio: 1,\n      lang: ecModel.getLocaleModel().get(['toolbox', 'saveAsImage', 'lang'])\n    };\n    return defaultOption;\n  };\n  return SaveAsImage;\n}(ToolboxFeature);\nexport default SaveAsImage;", "map": {"version": 3, "names": ["__extends", "env", "ToolboxFeature", "SaveAsImage", "_super", "apply", "arguments", "prototype", "onclick", "ecModel", "api", "model", "title", "get", "isSvg", "getZr", "painter", "getType", "type", "url", "getConnectedDataURL", "backgroundColor", "connectedBackgroundColor", "excludeComponents", "pixelRatio", "browser", "MouseEvent", "newEdge", "ie", "edge", "$a", "document", "createElement", "download", "target", "href", "evt", "view", "defaultView", "bubbles", "cancelable", "dispatchEvent", "window", "navigator", "msSaveOrOpenBlob", "parts", "split", "base64Encoded", "indexOf", "bstr", "decodeURIComponent", "atob", "filename", "n", "length", "u8arr", "Uint8Array", "charCodeAt", "blob", "Blob", "frame", "body", "append<PERSON><PERSON><PERSON>", "cw", "contentWindow", "doc", "open", "write", "close", "focus", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "lang", "html", "tab", "getDefaultOption", "defaultOption", "show", "icon", "getLocaleModel", "name"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/toolbox/feature/SaveAsImage.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/* global window, Uint8Array, document */\nimport env from 'zrender/lib/core/env.js';\nimport { ToolboxFeature } from '../featureManager.js';\nvar SaveAsImage = /** @class */function (_super) {\n  __extends(SaveAsImage, _super);\n  function SaveAsImage() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  SaveAsImage.prototype.onclick = function (ecModel, api) {\n    var model = this.model;\n    var title = model.get('name') || ecModel.get('title.0.text') || 'echarts';\n    var isSvg = api.getZr().painter.getType() === 'svg';\n    var type = isSvg ? 'svg' : model.get('type', true) || 'png';\n    var url = api.getConnectedDataURL({\n      type: type,\n      backgroundColor: model.get('backgroundColor', true) || ecModel.get('backgroundColor') || '#fff',\n      connectedBackgroundColor: model.get('connectedBackgroundColor'),\n      excludeComponents: model.get('excludeComponents'),\n      pixelRatio: model.get('pixelRatio')\n    });\n    var browser = env.browser;\n    // Chrome, Firefox, New Edge\n    if (typeof MouseEvent === 'function' && (browser.newEdge || !browser.ie && !browser.edge)) {\n      var $a = document.createElement('a');\n      $a.download = title + '.' + type;\n      $a.target = '_blank';\n      $a.href = url;\n      var evt = new MouseEvent('click', {\n        // some micro front-end framework， window maybe is a Proxy\n        view: document.defaultView,\n        bubbles: true,\n        cancelable: false\n      });\n      $a.dispatchEvent(evt);\n    }\n    // IE or old Edge\n    else {\n      // @ts-ignore\n      if (window.navigator.msSaveOrOpenBlob || isSvg) {\n        var parts = url.split(',');\n        // data:[<mime type>][;charset=<charset>][;base64],<encoded data>\n        var base64Encoded = parts[0].indexOf('base64') > -1;\n        var bstr = isSvg\n        // should decode the svg data uri first\n        ? decodeURIComponent(parts[1]) : parts[1];\n        // only `atob` when the data uri is encoded with base64\n        // otherwise, like `svg` data uri exported by zrender,\n        // there will be an error, for it's not encoded with base64.\n        // (just a url-encoded string through `encodeURIComponent`)\n        base64Encoded && (bstr = window.atob(bstr));\n        var filename = title + '.' + type;\n        // @ts-ignore\n        if (window.navigator.msSaveOrOpenBlob) {\n          var n = bstr.length;\n          var u8arr = new Uint8Array(n);\n          while (n--) {\n            u8arr[n] = bstr.charCodeAt(n);\n          }\n          var blob = new Blob([u8arr]); // @ts-ignore\n          window.navigator.msSaveOrOpenBlob(blob, filename);\n        } else {\n          var frame = document.createElement('iframe');\n          document.body.appendChild(frame);\n          var cw = frame.contentWindow;\n          var doc = cw.document;\n          doc.open('image/svg+xml', 'replace');\n          doc.write(bstr);\n          doc.close();\n          cw.focus();\n          doc.execCommand('SaveAs', true, filename);\n          document.body.removeChild(frame);\n        }\n      } else {\n        var lang = model.get('lang');\n        var html = '' + '<body style=\"margin:0;\">' + '<img src=\"' + url + '\" style=\"max-width:100%;\" title=\"' + (lang && lang[0] || '') + '\" />' + '</body>';\n        var tab = window.open();\n        tab.document.write(html);\n        tab.document.title = title;\n      }\n    }\n  };\n  SaveAsImage.getDefaultOption = function (ecModel) {\n    var defaultOption = {\n      show: true,\n      icon: 'M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0',\n      title: ecModel.getLocaleModel().get(['toolbox', 'saveAsImage', 'title']),\n      type: 'png',\n      // Default use option.backgroundColor\n      // backgroundColor: '#fff',\n      connectedBackgroundColor: '#fff',\n      name: '',\n      excludeComponents: ['toolbox'],\n      // use current pixel ratio of device by default\n      // pixelRatio: 1,\n      lang: ecModel.getLocaleModel().get(['toolbox', 'saveAsImage', 'lang'])\n    };\n    return defaultOption;\n  };\n  return SaveAsImage;\n}(ToolboxFeature);\nexport default SaveAsImage;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA,OAAOC,GAAG,MAAM,yBAAyB;AACzC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CJ,SAAS,CAACG,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,WAAW,CAACI,SAAS,CAACC,OAAO,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACtD,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,KAAK,GAAGD,KAAK,CAACE,GAAG,CAAC,MAAM,CAAC,IAAIJ,OAAO,CAACI,GAAG,CAAC,cAAc,CAAC,IAAI,SAAS;IACzE,IAAIC,KAAK,GAAGJ,GAAG,CAACK,KAAK,CAAC,CAAC,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,KAAK,KAAK;IACnD,IAAIC,IAAI,GAAGJ,KAAK,GAAG,KAAK,GAAGH,KAAK,CAACE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,KAAK;IAC3D,IAAIM,GAAG,GAAGT,GAAG,CAACU,mBAAmB,CAAC;MAChCF,IAAI,EAAEA,IAAI;MACVG,eAAe,EAAEV,KAAK,CAACE,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAIJ,OAAO,CAACI,GAAG,CAAC,iBAAiB,CAAC,IAAI,MAAM;MAC/FS,wBAAwB,EAAEX,KAAK,CAACE,GAAG,CAAC,0BAA0B,CAAC;MAC/DU,iBAAiB,EAAEZ,KAAK,CAACE,GAAG,CAAC,mBAAmB,CAAC;MACjDW,UAAU,EAAEb,KAAK,CAACE,GAAG,CAAC,YAAY;IACpC,CAAC,CAAC;IACF,IAAIY,OAAO,GAAGxB,GAAG,CAACwB,OAAO;IACzB;IACA,IAAI,OAAOC,UAAU,KAAK,UAAU,KAAKD,OAAO,CAACE,OAAO,IAAI,CAACF,OAAO,CAACG,EAAE,IAAI,CAACH,OAAO,CAACI,IAAI,CAAC,EAAE;MACzF,IAAIC,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACpCF,EAAE,CAACG,QAAQ,GAAGrB,KAAK,GAAG,GAAG,GAAGM,IAAI;MAChCY,EAAE,CAACI,MAAM,GAAG,QAAQ;MACpBJ,EAAE,CAACK,IAAI,GAAGhB,GAAG;MACb,IAAIiB,GAAG,GAAG,IAAIV,UAAU,CAAC,OAAO,EAAE;QAChC;QACAW,IAAI,EAAEN,QAAQ,CAACO,WAAW;QAC1BC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE;MACd,CAAC,CAAC;MACFV,EAAE,CAACW,aAAa,CAACL,GAAG,CAAC;IACvB;IACA;IAAA,KACK;MACH;MACA,IAAIM,MAAM,CAACC,SAAS,CAACC,gBAAgB,IAAI9B,KAAK,EAAE;QAC9C,IAAI+B,KAAK,GAAG1B,GAAG,CAAC2B,KAAK,CAAC,GAAG,CAAC;QAC1B;QACA,IAAIC,aAAa,GAAGF,KAAK,CAAC,CAAC,CAAC,CAACG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QACnD,IAAIC,IAAI,GAAGnC;QACX;QAAA,EACEoC,kBAAkB,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;QACzC;QACA;QACA;QACA;QACAE,aAAa,KAAKE,IAAI,GAAGP,MAAM,CAACS,IAAI,CAACF,IAAI,CAAC,CAAC;QAC3C,IAAIG,QAAQ,GAAGxC,KAAK,GAAG,GAAG,GAAGM,IAAI;QACjC;QACA,IAAIwB,MAAM,CAACC,SAAS,CAACC,gBAAgB,EAAE;UACrC,IAAIS,CAAC,GAAGJ,IAAI,CAACK,MAAM;UACnB,IAAIC,KAAK,GAAG,IAAIC,UAAU,CAACH,CAAC,CAAC;UAC7B,OAAOA,CAAC,EAAE,EAAE;YACVE,KAAK,CAACF,CAAC,CAAC,GAAGJ,IAAI,CAACQ,UAAU,CAACJ,CAAC,CAAC;UAC/B;UACA,IAAIK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;UAC9Bb,MAAM,CAACC,SAAS,CAACC,gBAAgB,CAACc,IAAI,EAAEN,QAAQ,CAAC;QACnD,CAAC,MAAM;UACL,IAAIQ,KAAK,GAAG7B,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAC5CD,QAAQ,CAAC8B,IAAI,CAACC,WAAW,CAACF,KAAK,CAAC;UAChC,IAAIG,EAAE,GAAGH,KAAK,CAACI,aAAa;UAC5B,IAAIC,GAAG,GAAGF,EAAE,CAAChC,QAAQ;UACrBkC,GAAG,CAACC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC;UACpCD,GAAG,CAACE,KAAK,CAAClB,IAAI,CAAC;UACfgB,GAAG,CAACG,KAAK,CAAC,CAAC;UACXL,EAAE,CAACM,KAAK,CAAC,CAAC;UACVJ,GAAG,CAACK,WAAW,CAAC,QAAQ,EAAE,IAAI,EAAElB,QAAQ,CAAC;UACzCrB,QAAQ,CAAC8B,IAAI,CAACU,WAAW,CAACX,KAAK,CAAC;QAClC;MACF,CAAC,MAAM;QACL,IAAIY,IAAI,GAAG7D,KAAK,CAACE,GAAG,CAAC,MAAM,CAAC;QAC5B,IAAI4D,IAAI,GAAG,EAAE,GAAG,0BAA0B,GAAG,YAAY,GAAGtD,GAAG,GAAG,mCAAmC,IAAIqD,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,GAAG,SAAS;QACpJ,IAAIE,GAAG,GAAGhC,MAAM,CAACwB,IAAI,CAAC,CAAC;QACvBQ,GAAG,CAAC3C,QAAQ,CAACoC,KAAK,CAACM,IAAI,CAAC;QACxBC,GAAG,CAAC3C,QAAQ,CAACnB,KAAK,GAAGA,KAAK;MAC5B;IACF;EACF,CAAC;EACDT,WAAW,CAACwE,gBAAgB,GAAG,UAAUlE,OAAO,EAAE;IAChD,IAAImE,aAAa,GAAG;MAClBC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,kFAAkF;MACxFlE,KAAK,EAAEH,OAAO,CAACsE,cAAc,CAAC,CAAC,CAAClE,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;MACxEK,IAAI,EAAE,KAAK;MACX;MACA;MACAI,wBAAwB,EAAE,MAAM;MAChC0D,IAAI,EAAE,EAAE;MACRzD,iBAAiB,EAAE,CAAC,SAAS,CAAC;MAC9B;MACA;MACAiD,IAAI,EAAE/D,OAAO,CAACsE,cAAc,CAAC,CAAC,CAAClE,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,MAAM,CAAC;IACvE,CAAC;IACD,OAAO+D,aAAa;EACtB,CAAC;EACD,OAAOzE,WAAW;AACpB,CAAC,CAACD,cAAc,CAAC;AACjB,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}