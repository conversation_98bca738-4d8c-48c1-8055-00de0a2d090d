{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { makeInner } from '../../util/model.js';\nimport SeriesModel from '../../model/Series.js';\n// Also compat with ec4, where\n// `visual('color') visual('borderColor')` is supported.\nexport var STYLE_VISUAL_TYPE = {\n  color: 'fill',\n  borderColor: 'stroke'\n};\nexport var NON_STYLE_VISUAL_PROPS = {\n  symbol: 1,\n  symbolSize: 1,\n  symbolKeepAspect: 1,\n  legendIcon: 1,\n  visualMeta: 1,\n  liftZ: 1,\n  decal: 1\n};\n;\nexport var customInnerStore = makeInner();\nvar CustomSeriesModel = /** @class */function (_super) {\n  __extends(CustomSeriesModel, _super);\n  function CustomSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CustomSeriesModel.type;\n    return _this;\n  }\n  CustomSeriesModel.prototype.optionUpdated = function () {\n    this.currentZLevel = this.get('zlevel', true);\n    this.currentZ = this.get('z', true);\n  };\n  CustomSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this);\n  };\n  CustomSeriesModel.prototype.getDataParams = function (dataIndex, dataType, el) {\n    var params = _super.prototype.getDataParams.call(this, dataIndex, dataType);\n    el && (params.info = customInnerStore(el).info);\n    return params;\n  };\n  CustomSeriesModel.type = 'series.custom';\n  CustomSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  CustomSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    // Custom series will not clip by default.\n    // Some case will use custom series to draw label\n    // For example https://echarts.apache.org/examples/en/editor.html?c=custom-gantt-flight\n    clip: false\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // Polar coordinate system\n    // polarIndex: 0,\n    // Geo coordinate system\n    // geoIndex: 0,\n  };\n  return CustomSeriesModel;\n}(SeriesModel);\nexport default CustomSeriesModel;", "map": {"version": 3, "names": ["__extends", "createSeriesData", "makeInner", "SeriesModel", "STYLE_VISUAL_TYPE", "color", "borderColor", "NON_STYLE_VISUAL_PROPS", "symbol", "symbolSize", "symbolKeepAspect", "legendIcon", "visualMeta", "liftZ", "decal", "customInnerStore", "CustomSeriesModel", "_super", "_this", "apply", "arguments", "type", "prototype", "optionUpdated", "currentZLevel", "get", "currentZ", "getInitialData", "option", "ecModel", "getDataParams", "dataIndex", "dataType", "el", "params", "call", "info", "dependencies", "defaultOption", "coordinateSystem", "z", "legendHoverLink", "clip"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/chart/custom/CustomSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { makeInner } from '../../util/model.js';\nimport SeriesModel from '../../model/Series.js';\n// Also compat with ec4, where\n// `visual('color') visual('borderColor')` is supported.\nexport var STYLE_VISUAL_TYPE = {\n  color: 'fill',\n  borderColor: 'stroke'\n};\nexport var NON_STYLE_VISUAL_PROPS = {\n  symbol: 1,\n  symbolSize: 1,\n  symbolKeepAspect: 1,\n  legendIcon: 1,\n  visualMeta: 1,\n  liftZ: 1,\n  decal: 1\n};\n;\nexport var customInnerStore = makeInner();\nvar CustomSeriesModel = /** @class */function (_super) {\n  __extends(CustomSeriesModel, _super);\n  function CustomSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CustomSeriesModel.type;\n    return _this;\n  }\n  CustomSeriesModel.prototype.optionUpdated = function () {\n    this.currentZLevel = this.get('zlevel', true);\n    this.currentZ = this.get('z', true);\n  };\n  CustomSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this);\n  };\n  CustomSeriesModel.prototype.getDataParams = function (dataIndex, dataType, el) {\n    var params = _super.prototype.getDataParams.call(this, dataIndex, dataType);\n    el && (params.info = customInnerStore(el).info);\n    return params;\n  };\n  CustomSeriesModel.type = 'series.custom';\n  CustomSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  CustomSeriesModel.defaultOption = {\n    coordinateSystem: 'cartesian2d',\n    // zlevel: 0,\n    z: 2,\n    legendHoverLink: true,\n    // Custom series will not clip by default.\n    // Some case will use custom series to draw label\n    // For example https://echarts.apache.org/examples/en/editor.html?c=custom-gantt-flight\n    clip: false\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // Polar coordinate system\n    // polarIndex: 0,\n    // Geo coordinate system\n    // geoIndex: 0,\n  };\n  return CustomSeriesModel;\n}(SeriesModel);\nexport default CustomSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAOC,WAAW,MAAM,uBAAuB;AAC/C;AACA;AACA,OAAO,IAAIC,iBAAiB,GAAG;EAC7BC,KAAK,EAAE,MAAM;EACbC,WAAW,EAAE;AACf,CAAC;AACD,OAAO,IAAIC,sBAAsB,GAAG;EAClCC,MAAM,EAAE,CAAC;EACTC,UAAU,EAAE,CAAC;EACbC,gBAAgB,EAAE,CAAC;EACnBC,UAAU,EAAE,CAAC;EACbC,UAAU,EAAE,CAAC;EACbC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE;AACT,CAAC;AACD;AACA,OAAO,IAAIC,gBAAgB,GAAGb,SAAS,CAAC,CAAC;AACzC,IAAIc,iBAAiB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACrDjB,SAAS,CAACgB,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,iBAAiB,CAACK,IAAI;IACnC,OAAOH,KAAK;EACd;EACAF,iBAAiB,CAACM,SAAS,CAACC,aAAa,GAAG,YAAY;IACtD,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;IAC7C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACD,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC;EACrC,CAAC;EACDT,iBAAiB,CAACM,SAAS,CAACK,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACtE,OAAO5B,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;EACrC,CAAC;EACDe,iBAAiB,CAACM,SAAS,CAACQ,aAAa,GAAG,UAAUC,SAAS,EAAEC,QAAQ,EAAEC,EAAE,EAAE;IAC7E,IAAIC,MAAM,GAAGjB,MAAM,CAACK,SAAS,CAACQ,aAAa,CAACK,IAAI,CAAC,IAAI,EAAEJ,SAAS,EAAEC,QAAQ,CAAC;IAC3EC,EAAE,KAAKC,MAAM,CAACE,IAAI,GAAGrB,gBAAgB,CAACkB,EAAE,CAAC,CAACG,IAAI,CAAC;IAC/C,OAAOF,MAAM;EACf,CAAC;EACDlB,iBAAiB,CAACK,IAAI,GAAG,eAAe;EACxCL,iBAAiB,CAACqB,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC;EACnFrB,iBAAiB,CAACsB,aAAa,GAAG;IAChCC,gBAAgB,EAAE,aAAa;IAC/B;IACAC,CAAC,EAAE,CAAC;IACJC,eAAe,EAAE,IAAI;IACrB;IACA;IACA;IACAC,IAAI,EAAE;IACN;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC;EACD,OAAO1B,iBAAiB;AAC1B,CAAC,CAACb,WAAW,CAAC;AACd,eAAea,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}