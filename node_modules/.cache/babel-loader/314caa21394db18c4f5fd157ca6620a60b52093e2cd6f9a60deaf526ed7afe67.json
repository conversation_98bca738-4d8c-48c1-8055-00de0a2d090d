{"ast": null, "code": "var Gradient = function () {\n  function Gradient(colorStops) {\n    this.colorStops = colorStops || [];\n  }\n  Gradient.prototype.addColorStop = function (offset, color) {\n    this.colorStops.push({\n      offset: offset,\n      color: color\n    });\n  };\n  return Gradient;\n}();\nexport default Gradient;", "map": {"version": 3, "names": ["Gradient", "colorStops", "prototype", "addColorStop", "offset", "color", "push"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/zrender/lib/graphic/Gradient.js"], "sourcesContent": ["var Gradient = (function () {\n    function Gradient(colorStops) {\n        this.colorStops = colorStops || [];\n    }\n    Gradient.prototype.addColorStop = function (offset, color) {\n        this.colorStops.push({\n            offset: offset,\n            color: color\n        });\n    };\n    return Gradient;\n}());\nexport default Gradient;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,YAAY;EACxB,SAASA,QAAQA,CAACC,UAAU,EAAE;IAC1B,IAAI,CAACA,UAAU,GAAGA,UAAU,IAAI,EAAE;EACtC;EACAD,QAAQ,CAACE,SAAS,CAACC,YAAY,GAAG,UAAUC,MAAM,EAAEC,KAAK,EAAE;IACvD,IAAI,CAACJ,UAAU,CAACK,IAAI,CAAC;MACjBF,MAAM,EAAEA,MAAM;MACdC,KAAK,EAAEA;IACX,CAAC,CAAC;EACN,CAAC;EACD,OAAOL,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}