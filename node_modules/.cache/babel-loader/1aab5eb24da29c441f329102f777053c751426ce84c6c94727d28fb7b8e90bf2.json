{"ast": null, "code": "import { adjustTextY, getIdURL, getMatrixStr, getPathPrecision, getShadow<PERSON>ey, getSRTTransformString, hasShadow, isAroundZero, isGradient, isImagePattern, isLinearGradient, isPattern, isRadialGradient, normalizeColor, round4, TEXT_ALIGN_TO_ANCHOR } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineHeight } from '../contain/text.js';\nimport TSpan from '../graphic/TSpan.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport mapStyleToAttrs from './mapStyleToAttrs.js';\nimport { createVNode, vNodeToString, META_DATA_PREFIX } from './core.js';\nimport { assert, clone, isFunction, isString, logError, map, retrieve2 } from '../core/util.js';\nimport { createOrUpdateImage } from '../graphic/helper/image.js';\nimport { createCSSAnimation } from './cssAnimation.js';\nimport { hasSeparateFont, parseFontSize } from '../graphic/Text.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_FAMILY } from '../core/platform.js';\nimport { createCSSEmphasis } from './cssEmphasis.js';\nimport { getElementSSRData } from '../zrender.js';\nvar round = Math.round;\nfunction isImageLike(val) {\n  return val && isString(val.src);\n}\nfunction isCanvasLike(val) {\n  return val && isFunction(val.toDataURL);\n}\nfunction setStyleAttrs(attrs, style, el, scope) {\n  mapStyleToAttrs(function (key, val) {\n    var isFillStroke = key === 'fill' || key === 'stroke';\n    if (isFillStroke && isGradient(val)) {\n      setGradient(style, attrs, key, scope);\n    } else if (isFillStroke && isPattern(val)) {\n      setPattern(el, attrs, key, scope);\n    } else {\n      attrs[key] = val;\n    }\n    if (isFillStroke && scope.ssr && val === 'none') {\n      attrs['pointer-events'] = 'visible';\n    }\n  }, style, el, false);\n  setShadow(el, attrs, scope);\n}\nfunction setMetaData(attrs, el) {\n  var metaData = getElementSSRData(el);\n  if (metaData) {\n    metaData.each(function (val, key) {\n      val != null && (attrs[(META_DATA_PREFIX + key).toLowerCase()] = val + '');\n    });\n    if (el.isSilent()) {\n      attrs[META_DATA_PREFIX + 'silent'] = 'true';\n    }\n  }\n}\nfunction noRotateScale(m) {\n  return isAroundZero(m[0] - 1) && isAroundZero(m[1]) && isAroundZero(m[2]) && isAroundZero(m[3] - 1);\n}\nfunction noTranslate(m) {\n  return isAroundZero(m[4]) && isAroundZero(m[5]);\n}\nfunction setTransform(attrs, m, compress) {\n  if (m && !(noTranslate(m) && noRotateScale(m))) {\n    var mul = compress ? 10 : 1e4;\n    attrs.transform = noRotateScale(m) ? \"translate(\" + round(m[4] * mul) / mul + \" \" + round(m[5] * mul) / mul + \")\" : getMatrixStr(m);\n  }\n}\nfunction convertPolyShape(shape, attrs, mul) {\n  var points = shape.points;\n  var strArr = [];\n  for (var i = 0; i < points.length; i++) {\n    strArr.push(round(points[i][0] * mul) / mul);\n    strArr.push(round(points[i][1] * mul) / mul);\n  }\n  attrs.points = strArr.join(' ');\n}\nfunction validatePolyShape(shape) {\n  return !shape.smooth;\n}\nfunction createAttrsConvert(desc) {\n  var normalizedDesc = map(desc, function (item) {\n    return typeof item === 'string' ? [item, item] : item;\n  });\n  return function (shape, attrs, mul) {\n    for (var i = 0; i < normalizedDesc.length; i++) {\n      var item = normalizedDesc[i];\n      var val = shape[item[0]];\n      if (val != null) {\n        attrs[item[1]] = round(val * mul) / mul;\n      }\n    }\n  };\n}\nvar builtinShapesDef = {\n  circle: [createAttrsConvert(['cx', 'cy', 'r'])],\n  polyline: [convertPolyShape, validatePolyShape],\n  polygon: [convertPolyShape, validatePolyShape]\n};\nfunction hasShapeAnimation(el) {\n  var animators = el.animators;\n  for (var i = 0; i < animators.length; i++) {\n    if (animators[i].targetName === 'shape') {\n      return true;\n    }\n  }\n  return false;\n}\nexport function brushSVGPath(el, scope) {\n  var style = el.style;\n  var shape = el.shape;\n  var builtinShpDef = builtinShapesDef[el.type];\n  var attrs = {};\n  var needsAnimate = scope.animation;\n  var svgElType = 'path';\n  var strokePercent = el.style.strokePercent;\n  var precision = scope.compress && getPathPrecision(el) || 4;\n  if (builtinShpDef && !scope.willUpdate && !(builtinShpDef[1] && !builtinShpDef[1](shape)) && !(needsAnimate && hasShapeAnimation(el)) && !(strokePercent < 1)) {\n    svgElType = el.type;\n    var mul = Math.pow(10, precision);\n    builtinShpDef[0](shape, attrs, mul);\n  } else {\n    var needBuildPath = !el.path || el.shapeChanged();\n    if (!el.path) {\n      el.createPathProxy();\n    }\n    var path = el.path;\n    if (needBuildPath) {\n      path.beginPath();\n      el.buildPath(path, el.shape);\n      el.pathUpdated();\n    }\n    var pathVersion = path.getVersion();\n    var elExt = el;\n    var svgPathBuilder = elExt.__svgPathBuilder;\n    if (elExt.__svgPathVersion !== pathVersion || !svgPathBuilder || strokePercent !== elExt.__svgPathStrokePercent) {\n      if (!svgPathBuilder) {\n        svgPathBuilder = elExt.__svgPathBuilder = new SVGPathRebuilder();\n      }\n      svgPathBuilder.reset(precision);\n      path.rebuildPath(svgPathBuilder, strokePercent);\n      svgPathBuilder.generateStr();\n      elExt.__svgPathVersion = pathVersion;\n      elExt.__svgPathStrokePercent = strokePercent;\n    }\n    attrs.d = svgPathBuilder.getStr();\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  setMetaData(attrs, el);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  scope.emphasis && createCSSEmphasis(el, attrs, scope);\n  return createVNode(svgElType, el.id + '', attrs);\n}\nexport function brushSVGImage(el, scope) {\n  var style = el.style;\n  var image = style.image;\n  if (image && !isString(image)) {\n    if (isImageLike(image)) {\n      image = image.src;\n    } else if (isCanvasLike(image)) {\n      image = image.toDataURL();\n    }\n  }\n  if (!image) {\n    return;\n  }\n  var x = style.x || 0;\n  var y = style.y || 0;\n  var dw = style.width;\n  var dh = style.height;\n  var attrs = {\n    href: image,\n    width: dw,\n    height: dh\n  };\n  if (x) {\n    attrs.x = x;\n  }\n  if (y) {\n    attrs.y = y;\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  setMetaData(attrs, el);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  return createVNode('image', el.id + '', attrs);\n}\n;\nexport function brushSVGTSpan(el, scope) {\n  var style = el.style;\n  var text = style.text;\n  text != null && (text += '');\n  if (!text || isNaN(style.x) || isNaN(style.y)) {\n    return;\n  }\n  var font = style.font || DEFAULT_FONT;\n  var x = style.x || 0;\n  var y = adjustTextY(style.y || 0, getLineHeight(font), style.textBaseline);\n  var textAlign = TEXT_ALIGN_TO_ANCHOR[style.textAlign] || style.textAlign;\n  var attrs = {\n    'dominant-baseline': 'central',\n    'text-anchor': textAlign\n  };\n  if (hasSeparateFont(style)) {\n    var separatedFontStr = '';\n    var fontStyle = style.fontStyle;\n    var fontSize = parseFontSize(style.fontSize);\n    if (!parseFloat(fontSize)) {\n      return;\n    }\n    var fontFamily = style.fontFamily || DEFAULT_FONT_FAMILY;\n    var fontWeight = style.fontWeight;\n    separatedFontStr += \"font-size:\" + fontSize + \";font-family:\" + fontFamily + \";\";\n    if (fontStyle && fontStyle !== 'normal') {\n      separatedFontStr += \"font-style:\" + fontStyle + \";\";\n    }\n    if (fontWeight && fontWeight !== 'normal') {\n      separatedFontStr += \"font-weight:\" + fontWeight + \";\";\n    }\n    attrs.style = separatedFontStr;\n  } else {\n    attrs.style = \"font: \" + font;\n  }\n  if (text.match(/\\s/)) {\n    attrs['xml:space'] = 'preserve';\n  }\n  if (x) {\n    attrs.x = x;\n  }\n  if (y) {\n    attrs.y = y;\n  }\n  setTransform(attrs, el.transform);\n  setStyleAttrs(attrs, style, el, scope);\n  setMetaData(attrs, el);\n  scope.animation && createCSSAnimation(el, attrs, scope);\n  return createVNode('text', el.id + '', attrs, undefined, text);\n}\nexport function brush(el, scope) {\n  if (el instanceof Path) {\n    return brushSVGPath(el, scope);\n  } else if (el instanceof ZRImage) {\n    return brushSVGImage(el, scope);\n  } else if (el instanceof TSpan) {\n    return brushSVGTSpan(el, scope);\n  }\n}\nfunction setShadow(el, attrs, scope) {\n  var style = el.style;\n  if (hasShadow(style)) {\n    var shadowKey = getShadowKey(el);\n    var shadowCache = scope.shadowCache;\n    var shadowId = shadowCache[shadowKey];\n    if (!shadowId) {\n      var globalScale = el.getGlobalScale();\n      var scaleX = globalScale[0];\n      var scaleY = globalScale[1];\n      if (!scaleX || !scaleY) {\n        return;\n      }\n      var offsetX = style.shadowOffsetX || 0;\n      var offsetY = style.shadowOffsetY || 0;\n      var blur_1 = style.shadowBlur;\n      var _a = normalizeColor(style.shadowColor),\n        opacity = _a.opacity,\n        color = _a.color;\n      var stdDx = blur_1 / 2 / scaleX;\n      var stdDy = blur_1 / 2 / scaleY;\n      var stdDeviation = stdDx + ' ' + stdDy;\n      shadowId = scope.zrId + '-s' + scope.shadowIdx++;\n      scope.defs[shadowId] = createVNode('filter', shadowId, {\n        'id': shadowId,\n        'x': '-100%',\n        'y': '-100%',\n        'width': '300%',\n        'height': '300%'\n      }, [createVNode('feDropShadow', '', {\n        'dx': offsetX / scaleX,\n        'dy': offsetY / scaleY,\n        'stdDeviation': stdDeviation,\n        'flood-color': color,\n        'flood-opacity': opacity\n      })]);\n      shadowCache[shadowKey] = shadowId;\n    }\n    attrs.filter = getIdURL(shadowId);\n  }\n}\nexport function setGradient(style, attrs, target, scope) {\n  var val = style[target];\n  var gradientTag;\n  var gradientAttrs = {\n    'gradientUnits': val.global ? 'userSpaceOnUse' : 'objectBoundingBox'\n  };\n  if (isLinearGradient(val)) {\n    gradientTag = 'linearGradient';\n    gradientAttrs.x1 = val.x;\n    gradientAttrs.y1 = val.y;\n    gradientAttrs.x2 = val.x2;\n    gradientAttrs.y2 = val.y2;\n  } else if (isRadialGradient(val)) {\n    gradientTag = 'radialGradient';\n    gradientAttrs.cx = retrieve2(val.x, 0.5);\n    gradientAttrs.cy = retrieve2(val.y, 0.5);\n    gradientAttrs.r = retrieve2(val.r, 0.5);\n  } else {\n    if (process.env.NODE_ENV !== 'production') {\n      logError('Illegal gradient type.');\n    }\n    return;\n  }\n  var colors = val.colorStops;\n  var colorStops = [];\n  for (var i = 0, len = colors.length; i < len; ++i) {\n    var offset = round4(colors[i].offset) * 100 + '%';\n    var stopColor = colors[i].color;\n    var _a = normalizeColor(stopColor),\n      color = _a.color,\n      opacity = _a.opacity;\n    var stopsAttrs = {\n      'offset': offset\n    };\n    stopsAttrs['stop-color'] = color;\n    if (opacity < 1) {\n      stopsAttrs['stop-opacity'] = opacity;\n    }\n    colorStops.push(createVNode('stop', i + '', stopsAttrs));\n  }\n  var gradientVNode = createVNode(gradientTag, '', gradientAttrs, colorStops);\n  var gradientKey = vNodeToString(gradientVNode);\n  var gradientCache = scope.gradientCache;\n  var gradientId = gradientCache[gradientKey];\n  if (!gradientId) {\n    gradientId = scope.zrId + '-g' + scope.gradientIdx++;\n    gradientCache[gradientKey] = gradientId;\n    gradientAttrs.id = gradientId;\n    scope.defs[gradientId] = createVNode(gradientTag, gradientId, gradientAttrs, colorStops);\n  }\n  attrs[target] = getIdURL(gradientId);\n}\nexport function setPattern(el, attrs, target, scope) {\n  var val = el.style[target];\n  var boundingRect = el.getBoundingRect();\n  var patternAttrs = {};\n  var repeat = val.repeat;\n  var noRepeat = repeat === 'no-repeat';\n  var repeatX = repeat === 'repeat-x';\n  var repeatY = repeat === 'repeat-y';\n  var child;\n  if (isImagePattern(val)) {\n    var imageWidth_1 = val.imageWidth;\n    var imageHeight_1 = val.imageHeight;\n    var imageSrc = void 0;\n    var patternImage = val.image;\n    if (isString(patternImage)) {\n      imageSrc = patternImage;\n    } else if (isImageLike(patternImage)) {\n      imageSrc = patternImage.src;\n    } else if (isCanvasLike(patternImage)) {\n      imageSrc = patternImage.toDataURL();\n    }\n    if (typeof Image === 'undefined') {\n      var errMsg = 'Image width/height must been given explictly in svg-ssr renderer.';\n      assert(imageWidth_1, errMsg);\n      assert(imageHeight_1, errMsg);\n    } else if (imageWidth_1 == null || imageHeight_1 == null) {\n      var setSizeToVNode_1 = function (vNode, img) {\n        if (vNode) {\n          var svgEl = vNode.elm;\n          var width = imageWidth_1 || img.width;\n          var height = imageHeight_1 || img.height;\n          if (vNode.tag === 'pattern') {\n            if (repeatX) {\n              height = 1;\n              width /= boundingRect.width;\n            } else if (repeatY) {\n              width = 1;\n              height /= boundingRect.height;\n            }\n          }\n          vNode.attrs.width = width;\n          vNode.attrs.height = height;\n          if (svgEl) {\n            svgEl.setAttribute('width', width);\n            svgEl.setAttribute('height', height);\n          }\n        }\n      };\n      var createdImage = createOrUpdateImage(imageSrc, null, el, function (img) {\n        noRepeat || setSizeToVNode_1(patternVNode, img);\n        setSizeToVNode_1(child, img);\n      });\n      if (createdImage && createdImage.width && createdImage.height) {\n        imageWidth_1 = imageWidth_1 || createdImage.width;\n        imageHeight_1 = imageHeight_1 || createdImage.height;\n      }\n    }\n    child = createVNode('image', 'img', {\n      href: imageSrc,\n      width: imageWidth_1,\n      height: imageHeight_1\n    });\n    patternAttrs.width = imageWidth_1;\n    patternAttrs.height = imageHeight_1;\n  } else if (val.svgElement) {\n    child = clone(val.svgElement);\n    patternAttrs.width = val.svgWidth;\n    patternAttrs.height = val.svgHeight;\n  }\n  if (!child) {\n    return;\n  }\n  var patternWidth;\n  var patternHeight;\n  if (noRepeat) {\n    patternWidth = patternHeight = 1;\n  } else if (repeatX) {\n    patternHeight = 1;\n    patternWidth = patternAttrs.width / boundingRect.width;\n  } else if (repeatY) {\n    patternWidth = 1;\n    patternHeight = patternAttrs.height / boundingRect.height;\n  } else {\n    patternAttrs.patternUnits = 'userSpaceOnUse';\n  }\n  if (patternWidth != null && !isNaN(patternWidth)) {\n    patternAttrs.width = patternWidth;\n  }\n  if (patternHeight != null && !isNaN(patternHeight)) {\n    patternAttrs.height = patternHeight;\n  }\n  var patternTransform = getSRTTransformString(val);\n  patternTransform && (patternAttrs.patternTransform = patternTransform);\n  var patternVNode = createVNode('pattern', '', patternAttrs, [child]);\n  var patternKey = vNodeToString(patternVNode);\n  var patternCache = scope.patternCache;\n  var patternId = patternCache[patternKey];\n  if (!patternId) {\n    patternId = scope.zrId + '-p' + scope.patternIdx++;\n    patternCache[patternKey] = patternId;\n    patternAttrs.id = patternId;\n    patternVNode = scope.defs[patternId] = createVNode('pattern', patternId, patternAttrs, [child]);\n  }\n  attrs[target] = getIdURL(patternId);\n}\nexport function setClipPath(clipPath, attrs, scope) {\n  var clipPathCache = scope.clipPathCache,\n    defs = scope.defs;\n  var clipPathId = clipPathCache[clipPath.id];\n  if (!clipPathId) {\n    clipPathId = scope.zrId + '-c' + scope.clipPathIdx++;\n    var clipPathAttrs = {\n      id: clipPathId\n    };\n    clipPathCache[clipPath.id] = clipPathId;\n    defs[clipPathId] = createVNode('clipPath', clipPathId, clipPathAttrs, [brushSVGPath(clipPath, scope)]);\n  }\n  attrs['clip-path'] = getIdURL(clipPathId);\n}", "map": {"version": 3, "names": ["adjustTextY", "getIdURL", "getMatrixStr", "getPathPrecision", "getShadowKey", "getSRTTransformString", "<PERSON><PERSON><PERSON><PERSON>", "isAroundZero", "isGradient", "isImagePattern", "isLinearGradient", "isPattern", "isRadialGradient", "normalizeColor", "round4", "TEXT_ALIGN_TO_ANCHOR", "Path", "ZRImage", "getLineHeight", "TSpan", "SVGPathRebuilder", "mapStyleToAttrs", "createVNode", "vNodeToString", "META_DATA_PREFIX", "assert", "clone", "isFunction", "isString", "logError", "map", "retrieve2", "createOrUpdateImage", "createCSSAnimation", "hasSeparateFont", "parseFontSize", "DEFAULT_FONT", "DEFAULT_FONT_FAMILY", "createCSSEmphasis", "getElementSSRData", "round", "Math", "isImageLike", "val", "src", "isCanvasLike", "toDataURL", "setStyleAttrs", "attrs", "style", "el", "scope", "key", "isFillStroke", "setGradient", "setPattern", "ssr", "setShadow", "setMetaData", "metaData", "each", "toLowerCase", "isSilent", "noRotateScale", "m", "noTranslate", "setTransform", "compress", "mul", "transform", "convertPolyShape", "shape", "points", "strArr", "i", "length", "push", "join", "validatePolyShape", "smooth", "createAttrsConvert", "desc", "normalizedDesc", "item", "builtinShapesDef", "circle", "polyline", "polygon", "hasShapeAnimation", "animators", "targetName", "brush<PERSON><PERSON>ath", "builtinShpDef", "type", "needsAnimate", "animation", "svgElType", "strokePercent", "precision", "willUpdate", "pow", "needBuildPath", "path", "shapeChanged", "createPathProxy", "beginPath", "buildPath", "pathUpdated", "pathVersion", "getVersion", "elExt", "svgPathBuilder", "__svgPathBuilder", "__svgPathVersion", "__svgPathStrokePercent", "reset", "rebuildPath", "generateStr", "d", "getStr", "emphasis", "id", "brushSVGImage", "image", "x", "y", "dw", "width", "dh", "height", "href", "brushSVGTSpan", "text", "isNaN", "font", "textBaseline", "textAlign", "separatedFontStr", "fontStyle", "fontSize", "parseFloat", "fontFamily", "fontWeight", "match", "undefined", "brush", "<PERSON><PERSON><PERSON>", "shadowCache", "shadowId", "globalScale", "getGlobalScale", "scaleX", "scaleY", "offsetX", "shadowOffsetX", "offsetY", "shadowOffsetY", "blur_1", "<PERSON><PERSON><PERSON><PERSON>", "_a", "shadowColor", "opacity", "color", "stdDx", "stdDy", "stdDeviation", "zrId", "shadowIdx", "defs", "filter", "target", "gradientTag", "gradientAttrs", "global", "x1", "y1", "x2", "y2", "cx", "cy", "r", "process", "env", "NODE_ENV", "colors", "colorStops", "len", "offset", "stopColor", "stopsAttrs", "gradientVNode", "gradient<PERSON>ey", "gradientCache", "gradientId", "gradientIdx", "boundingRect", "getBoundingRect", "patternAttrs", "repeat", "noRepeat", "repeatX", "repeatY", "child", "imageWidth_1", "imageWidth", "imageHeight_1", "imageHeight", "imageSrc", "patternImage", "Image", "errMsg", "setSizeToVNode_1", "vNode", "img", "svgEl", "elm", "tag", "setAttribute", "createdImage", "patternVNode", "svgElement", "svgWidth", "svgHeight", "patternWidth", "patternHeight", "patternUnits", "patternTransform", "pattern<PERSON>ey", "patternCache", "patternId", "patternIdx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clipPath", "clipPath<PERSON>ache", "clipPathId", "clipPathIdx", "clipPathAttrs"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/zrender/lib/svg/graphic.js"], "sourcesContent": ["import { adjustTextY, getIdURL, getMatrixStr, getPathPrecision, getShadow<PERSON>ey, getSRTTransformString, hasShadow, isAroundZero, isGradient, isImagePattern, isLinearGradient, isPattern, isRadialGradient, normalizeColor, round4, TEXT_ALIGN_TO_ANCHOR } from './helper.js';\nimport Path from '../graphic/Path.js';\nimport ZRImage from '../graphic/Image.js';\nimport { getLineHeight } from '../contain/text.js';\nimport TSpan from '../graphic/TSpan.js';\nimport SVGPathRebuilder from './SVGPathRebuilder.js';\nimport mapStyleToAttrs from './mapStyleToAttrs.js';\nimport { createVNode, vNodeToString, META_DATA_PREFIX } from './core.js';\nimport { assert, clone, isFunction, isString, logError, map, retrieve2 } from '../core/util.js';\nimport { createOrUpdateImage } from '../graphic/helper/image.js';\nimport { createCSSAnimation } from './cssAnimation.js';\nimport { hasSeparateFont, parseFontSize } from '../graphic/Text.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_FAMILY } from '../core/platform.js';\nimport { createCSSEmphasis } from './cssEmphasis.js';\nimport { getElementSSRData } from '../zrender.js';\nvar round = Math.round;\nfunction isImageLike(val) {\n    return val && isString(val.src);\n}\nfunction isCanvasLike(val) {\n    return val && isFunction(val.toDataURL);\n}\nfunction setStyleAttrs(attrs, style, el, scope) {\n    mapStyleToAttrs(function (key, val) {\n        var isFillStroke = key === 'fill' || key === 'stroke';\n        if (isFillStroke && isGradient(val)) {\n            setGradient(style, attrs, key, scope);\n        }\n        else if (isFillStroke && isPattern(val)) {\n            setPattern(el, attrs, key, scope);\n        }\n        else {\n            attrs[key] = val;\n        }\n        if (isFillStroke && scope.ssr && val === 'none') {\n            attrs['pointer-events'] = 'visible';\n        }\n    }, style, el, false);\n    setShadow(el, attrs, scope);\n}\nfunction setMetaData(attrs, el) {\n    var metaData = getElementSSRData(el);\n    if (metaData) {\n        metaData.each(function (val, key) {\n            val != null && (attrs[(META_DATA_PREFIX + key).toLowerCase()] = val + '');\n        });\n        if (el.isSilent()) {\n            attrs[META_DATA_PREFIX + 'silent'] = 'true';\n        }\n    }\n}\nfunction noRotateScale(m) {\n    return isAroundZero(m[0] - 1)\n        && isAroundZero(m[1])\n        && isAroundZero(m[2])\n        && isAroundZero(m[3] - 1);\n}\nfunction noTranslate(m) {\n    return isAroundZero(m[4]) && isAroundZero(m[5]);\n}\nfunction setTransform(attrs, m, compress) {\n    if (m && !(noTranslate(m) && noRotateScale(m))) {\n        var mul = compress ? 10 : 1e4;\n        attrs.transform = noRotateScale(m)\n            ? \"translate(\" + round(m[4] * mul) / mul + \" \" + round(m[5] * mul) / mul + \")\" : getMatrixStr(m);\n    }\n}\nfunction convertPolyShape(shape, attrs, mul) {\n    var points = shape.points;\n    var strArr = [];\n    for (var i = 0; i < points.length; i++) {\n        strArr.push(round(points[i][0] * mul) / mul);\n        strArr.push(round(points[i][1] * mul) / mul);\n    }\n    attrs.points = strArr.join(' ');\n}\nfunction validatePolyShape(shape) {\n    return !shape.smooth;\n}\nfunction createAttrsConvert(desc) {\n    var normalizedDesc = map(desc, function (item) {\n        return (typeof item === 'string' ? [item, item] : item);\n    });\n    return function (shape, attrs, mul) {\n        for (var i = 0; i < normalizedDesc.length; i++) {\n            var item = normalizedDesc[i];\n            var val = shape[item[0]];\n            if (val != null) {\n                attrs[item[1]] = round(val * mul) / mul;\n            }\n        }\n    };\n}\nvar builtinShapesDef = {\n    circle: [createAttrsConvert(['cx', 'cy', 'r'])],\n    polyline: [convertPolyShape, validatePolyShape],\n    polygon: [convertPolyShape, validatePolyShape]\n};\nfunction hasShapeAnimation(el) {\n    var animators = el.animators;\n    for (var i = 0; i < animators.length; i++) {\n        if (animators[i].targetName === 'shape') {\n            return true;\n        }\n    }\n    return false;\n}\nexport function brushSVGPath(el, scope) {\n    var style = el.style;\n    var shape = el.shape;\n    var builtinShpDef = builtinShapesDef[el.type];\n    var attrs = {};\n    var needsAnimate = scope.animation;\n    var svgElType = 'path';\n    var strokePercent = el.style.strokePercent;\n    var precision = (scope.compress && getPathPrecision(el)) || 4;\n    if (builtinShpDef\n        && !scope.willUpdate\n        && !(builtinShpDef[1] && !builtinShpDef[1](shape))\n        && !(needsAnimate && hasShapeAnimation(el))\n        && !(strokePercent < 1)) {\n        svgElType = el.type;\n        var mul = Math.pow(10, precision);\n        builtinShpDef[0](shape, attrs, mul);\n    }\n    else {\n        var needBuildPath = !el.path || el.shapeChanged();\n        if (!el.path) {\n            el.createPathProxy();\n        }\n        var path = el.path;\n        if (needBuildPath) {\n            path.beginPath();\n            el.buildPath(path, el.shape);\n            el.pathUpdated();\n        }\n        var pathVersion = path.getVersion();\n        var elExt = el;\n        var svgPathBuilder = elExt.__svgPathBuilder;\n        if (elExt.__svgPathVersion !== pathVersion\n            || !svgPathBuilder\n            || strokePercent !== elExt.__svgPathStrokePercent) {\n            if (!svgPathBuilder) {\n                svgPathBuilder = elExt.__svgPathBuilder = new SVGPathRebuilder();\n            }\n            svgPathBuilder.reset(precision);\n            path.rebuildPath(svgPathBuilder, strokePercent);\n            svgPathBuilder.generateStr();\n            elExt.__svgPathVersion = pathVersion;\n            elExt.__svgPathStrokePercent = strokePercent;\n        }\n        attrs.d = svgPathBuilder.getStr();\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    scope.emphasis && createCSSEmphasis(el, attrs, scope);\n    return createVNode(svgElType, el.id + '', attrs);\n}\nexport function brushSVGImage(el, scope) {\n    var style = el.style;\n    var image = style.image;\n    if (image && !isString(image)) {\n        if (isImageLike(image)) {\n            image = image.src;\n        }\n        else if (isCanvasLike(image)) {\n            image = image.toDataURL();\n        }\n    }\n    if (!image) {\n        return;\n    }\n    var x = style.x || 0;\n    var y = style.y || 0;\n    var dw = style.width;\n    var dh = style.height;\n    var attrs = {\n        href: image,\n        width: dw,\n        height: dh\n    };\n    if (x) {\n        attrs.x = x;\n    }\n    if (y) {\n        attrs.y = y;\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    return createVNode('image', el.id + '', attrs);\n}\n;\nexport function brushSVGTSpan(el, scope) {\n    var style = el.style;\n    var text = style.text;\n    text != null && (text += '');\n    if (!text || isNaN(style.x) || isNaN(style.y)) {\n        return;\n    }\n    var font = style.font || DEFAULT_FONT;\n    var x = style.x || 0;\n    var y = adjustTextY(style.y || 0, getLineHeight(font), style.textBaseline);\n    var textAlign = TEXT_ALIGN_TO_ANCHOR[style.textAlign]\n        || style.textAlign;\n    var attrs = {\n        'dominant-baseline': 'central',\n        'text-anchor': textAlign\n    };\n    if (hasSeparateFont(style)) {\n        var separatedFontStr = '';\n        var fontStyle = style.fontStyle;\n        var fontSize = parseFontSize(style.fontSize);\n        if (!parseFloat(fontSize)) {\n            return;\n        }\n        var fontFamily = style.fontFamily || DEFAULT_FONT_FAMILY;\n        var fontWeight = style.fontWeight;\n        separatedFontStr += \"font-size:\" + fontSize + \";font-family:\" + fontFamily + \";\";\n        if (fontStyle && fontStyle !== 'normal') {\n            separatedFontStr += \"font-style:\" + fontStyle + \";\";\n        }\n        if (fontWeight && fontWeight !== 'normal') {\n            separatedFontStr += \"font-weight:\" + fontWeight + \";\";\n        }\n        attrs.style = separatedFontStr;\n    }\n    else {\n        attrs.style = \"font: \" + font;\n    }\n    if (text.match(/\\s/)) {\n        attrs['xml:space'] = 'preserve';\n    }\n    if (x) {\n        attrs.x = x;\n    }\n    if (y) {\n        attrs.y = y;\n    }\n    setTransform(attrs, el.transform);\n    setStyleAttrs(attrs, style, el, scope);\n    setMetaData(attrs, el);\n    scope.animation && createCSSAnimation(el, attrs, scope);\n    return createVNode('text', el.id + '', attrs, undefined, text);\n}\nexport function brush(el, scope) {\n    if (el instanceof Path) {\n        return brushSVGPath(el, scope);\n    }\n    else if (el instanceof ZRImage) {\n        return brushSVGImage(el, scope);\n    }\n    else if (el instanceof TSpan) {\n        return brushSVGTSpan(el, scope);\n    }\n}\nfunction setShadow(el, attrs, scope) {\n    var style = el.style;\n    if (hasShadow(style)) {\n        var shadowKey = getShadowKey(el);\n        var shadowCache = scope.shadowCache;\n        var shadowId = shadowCache[shadowKey];\n        if (!shadowId) {\n            var globalScale = el.getGlobalScale();\n            var scaleX = globalScale[0];\n            var scaleY = globalScale[1];\n            if (!scaleX || !scaleY) {\n                return;\n            }\n            var offsetX = style.shadowOffsetX || 0;\n            var offsetY = style.shadowOffsetY || 0;\n            var blur_1 = style.shadowBlur;\n            var _a = normalizeColor(style.shadowColor), opacity = _a.opacity, color = _a.color;\n            var stdDx = blur_1 / 2 / scaleX;\n            var stdDy = blur_1 / 2 / scaleY;\n            var stdDeviation = stdDx + ' ' + stdDy;\n            shadowId = scope.zrId + '-s' + scope.shadowIdx++;\n            scope.defs[shadowId] = createVNode('filter', shadowId, {\n                'id': shadowId,\n                'x': '-100%',\n                'y': '-100%',\n                'width': '300%',\n                'height': '300%'\n            }, [\n                createVNode('feDropShadow', '', {\n                    'dx': offsetX / scaleX,\n                    'dy': offsetY / scaleY,\n                    'stdDeviation': stdDeviation,\n                    'flood-color': color,\n                    'flood-opacity': opacity\n                })\n            ]);\n            shadowCache[shadowKey] = shadowId;\n        }\n        attrs.filter = getIdURL(shadowId);\n    }\n}\nexport function setGradient(style, attrs, target, scope) {\n    var val = style[target];\n    var gradientTag;\n    var gradientAttrs = {\n        'gradientUnits': val.global\n            ? 'userSpaceOnUse'\n            : 'objectBoundingBox'\n    };\n    if (isLinearGradient(val)) {\n        gradientTag = 'linearGradient';\n        gradientAttrs.x1 = val.x;\n        gradientAttrs.y1 = val.y;\n        gradientAttrs.x2 = val.x2;\n        gradientAttrs.y2 = val.y2;\n    }\n    else if (isRadialGradient(val)) {\n        gradientTag = 'radialGradient';\n        gradientAttrs.cx = retrieve2(val.x, 0.5);\n        gradientAttrs.cy = retrieve2(val.y, 0.5);\n        gradientAttrs.r = retrieve2(val.r, 0.5);\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            logError('Illegal gradient type.');\n        }\n        return;\n    }\n    var colors = val.colorStops;\n    var colorStops = [];\n    for (var i = 0, len = colors.length; i < len; ++i) {\n        var offset = round4(colors[i].offset) * 100 + '%';\n        var stopColor = colors[i].color;\n        var _a = normalizeColor(stopColor), color = _a.color, opacity = _a.opacity;\n        var stopsAttrs = {\n            'offset': offset\n        };\n        stopsAttrs['stop-color'] = color;\n        if (opacity < 1) {\n            stopsAttrs['stop-opacity'] = opacity;\n        }\n        colorStops.push(createVNode('stop', i + '', stopsAttrs));\n    }\n    var gradientVNode = createVNode(gradientTag, '', gradientAttrs, colorStops);\n    var gradientKey = vNodeToString(gradientVNode);\n    var gradientCache = scope.gradientCache;\n    var gradientId = gradientCache[gradientKey];\n    if (!gradientId) {\n        gradientId = scope.zrId + '-g' + scope.gradientIdx++;\n        gradientCache[gradientKey] = gradientId;\n        gradientAttrs.id = gradientId;\n        scope.defs[gradientId] = createVNode(gradientTag, gradientId, gradientAttrs, colorStops);\n    }\n    attrs[target] = getIdURL(gradientId);\n}\nexport function setPattern(el, attrs, target, scope) {\n    var val = el.style[target];\n    var boundingRect = el.getBoundingRect();\n    var patternAttrs = {};\n    var repeat = val.repeat;\n    var noRepeat = repeat === 'no-repeat';\n    var repeatX = repeat === 'repeat-x';\n    var repeatY = repeat === 'repeat-y';\n    var child;\n    if (isImagePattern(val)) {\n        var imageWidth_1 = val.imageWidth;\n        var imageHeight_1 = val.imageHeight;\n        var imageSrc = void 0;\n        var patternImage = val.image;\n        if (isString(patternImage)) {\n            imageSrc = patternImage;\n        }\n        else if (isImageLike(patternImage)) {\n            imageSrc = patternImage.src;\n        }\n        else if (isCanvasLike(patternImage)) {\n            imageSrc = patternImage.toDataURL();\n        }\n        if (typeof Image === 'undefined') {\n            var errMsg = 'Image width/height must been given explictly in svg-ssr renderer.';\n            assert(imageWidth_1, errMsg);\n            assert(imageHeight_1, errMsg);\n        }\n        else if (imageWidth_1 == null || imageHeight_1 == null) {\n            var setSizeToVNode_1 = function (vNode, img) {\n                if (vNode) {\n                    var svgEl = vNode.elm;\n                    var width = imageWidth_1 || img.width;\n                    var height = imageHeight_1 || img.height;\n                    if (vNode.tag === 'pattern') {\n                        if (repeatX) {\n                            height = 1;\n                            width /= boundingRect.width;\n                        }\n                        else if (repeatY) {\n                            width = 1;\n                            height /= boundingRect.height;\n                        }\n                    }\n                    vNode.attrs.width = width;\n                    vNode.attrs.height = height;\n                    if (svgEl) {\n                        svgEl.setAttribute('width', width);\n                        svgEl.setAttribute('height', height);\n                    }\n                }\n            };\n            var createdImage = createOrUpdateImage(imageSrc, null, el, function (img) {\n                noRepeat || setSizeToVNode_1(patternVNode, img);\n                setSizeToVNode_1(child, img);\n            });\n            if (createdImage && createdImage.width && createdImage.height) {\n                imageWidth_1 = imageWidth_1 || createdImage.width;\n                imageHeight_1 = imageHeight_1 || createdImage.height;\n            }\n        }\n        child = createVNode('image', 'img', {\n            href: imageSrc,\n            width: imageWidth_1,\n            height: imageHeight_1\n        });\n        patternAttrs.width = imageWidth_1;\n        patternAttrs.height = imageHeight_1;\n    }\n    else if (val.svgElement) {\n        child = clone(val.svgElement);\n        patternAttrs.width = val.svgWidth;\n        patternAttrs.height = val.svgHeight;\n    }\n    if (!child) {\n        return;\n    }\n    var patternWidth;\n    var patternHeight;\n    if (noRepeat) {\n        patternWidth = patternHeight = 1;\n    }\n    else if (repeatX) {\n        patternHeight = 1;\n        patternWidth = patternAttrs.width / boundingRect.width;\n    }\n    else if (repeatY) {\n        patternWidth = 1;\n        patternHeight = patternAttrs.height / boundingRect.height;\n    }\n    else {\n        patternAttrs.patternUnits = 'userSpaceOnUse';\n    }\n    if (patternWidth != null && !isNaN(patternWidth)) {\n        patternAttrs.width = patternWidth;\n    }\n    if (patternHeight != null && !isNaN(patternHeight)) {\n        patternAttrs.height = patternHeight;\n    }\n    var patternTransform = getSRTTransformString(val);\n    patternTransform && (patternAttrs.patternTransform = patternTransform);\n    var patternVNode = createVNode('pattern', '', patternAttrs, [child]);\n    var patternKey = vNodeToString(patternVNode);\n    var patternCache = scope.patternCache;\n    var patternId = patternCache[patternKey];\n    if (!patternId) {\n        patternId = scope.zrId + '-p' + scope.patternIdx++;\n        patternCache[patternKey] = patternId;\n        patternAttrs.id = patternId;\n        patternVNode = scope.defs[patternId] = createVNode('pattern', patternId, patternAttrs, [child]);\n    }\n    attrs[target] = getIdURL(patternId);\n}\nexport function setClipPath(clipPath, attrs, scope) {\n    var clipPathCache = scope.clipPathCache, defs = scope.defs;\n    var clipPathId = clipPathCache[clipPath.id];\n    if (!clipPathId) {\n        clipPathId = scope.zrId + '-c' + scope.clipPathIdx++;\n        var clipPathAttrs = {\n            id: clipPathId\n        };\n        clipPathCache[clipPath.id] = clipPathId;\n        defs[clipPathId] = createVNode('clipPath', clipPathId, clipPathAttrs, [brushSVGPath(clipPath, scope)]);\n    }\n    attrs['clip-path'] = getIdURL(clipPathId);\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,YAAY,EAAEC,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,MAAM,EAAEC,oBAAoB,QAAQ,aAAa;AAC1Q,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,SAASC,WAAW,EAAEC,aAAa,EAAEC,gBAAgB,QAAQ,WAAW;AACxE,SAASC,MAAM,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,SAAS,QAAQ,iBAAiB;AAC/F,SAASC,mBAAmB,QAAQ,4BAA4B;AAChE,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,eAAe,EAAEC,aAAa,QAAQ,oBAAoB;AACnE,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,qBAAqB;AACvE,SAASC,iBAAiB,QAAQ,kBAAkB;AACpD,SAASC,iBAAiB,QAAQ,eAAe;AACjD,IAAIC,KAAK,GAAGC,IAAI,CAACD,KAAK;AACtB,SAASE,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOA,GAAG,IAAIf,QAAQ,CAACe,GAAG,CAACC,GAAG,CAAC;AACnC;AACA,SAASC,YAAYA,CAACF,GAAG,EAAE;EACvB,OAAOA,GAAG,IAAIhB,UAAU,CAACgB,GAAG,CAACG,SAAS,CAAC;AAC3C;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,EAAE;EAC5C9B,eAAe,CAAC,UAAU+B,GAAG,EAAET,GAAG,EAAE;IAChC,IAAIU,YAAY,GAAGD,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,QAAQ;IACrD,IAAIC,YAAY,IAAI7C,UAAU,CAACmC,GAAG,CAAC,EAAE;MACjCW,WAAW,CAACL,KAAK,EAAED,KAAK,EAAEI,GAAG,EAAED,KAAK,CAAC;IACzC,CAAC,MACI,IAAIE,YAAY,IAAI1C,SAAS,CAACgC,GAAG,CAAC,EAAE;MACrCY,UAAU,CAACL,EAAE,EAAEF,KAAK,EAAEI,GAAG,EAAED,KAAK,CAAC;IACrC,CAAC,MACI;MACDH,KAAK,CAACI,GAAG,CAAC,GAAGT,GAAG;IACpB;IACA,IAAIU,YAAY,IAAIF,KAAK,CAACK,GAAG,IAAIb,GAAG,KAAK,MAAM,EAAE;MAC7CK,KAAK,CAAC,gBAAgB,CAAC,GAAG,SAAS;IACvC;EACJ,CAAC,EAAEC,KAAK,EAAEC,EAAE,EAAE,KAAK,CAAC;EACpBO,SAAS,CAACP,EAAE,EAAEF,KAAK,EAAEG,KAAK,CAAC;AAC/B;AACA,SAASO,WAAWA,CAACV,KAAK,EAAEE,EAAE,EAAE;EAC5B,IAAIS,QAAQ,GAAGpB,iBAAiB,CAACW,EAAE,CAAC;EACpC,IAAIS,QAAQ,EAAE;IACVA,QAAQ,CAACC,IAAI,CAAC,UAAUjB,GAAG,EAAES,GAAG,EAAE;MAC9BT,GAAG,IAAI,IAAI,KAAKK,KAAK,CAAC,CAACxB,gBAAgB,GAAG4B,GAAG,EAAES,WAAW,CAAC,CAAC,CAAC,GAAGlB,GAAG,GAAG,EAAE,CAAC;IAC7E,CAAC,CAAC;IACF,IAAIO,EAAE,CAACY,QAAQ,CAAC,CAAC,EAAE;MACfd,KAAK,CAACxB,gBAAgB,GAAG,QAAQ,CAAC,GAAG,MAAM;IAC/C;EACJ;AACJ;AACA,SAASuC,aAAaA,CAACC,CAAC,EAAE;EACtB,OAAOzD,YAAY,CAACyD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IACtBzD,YAAY,CAACyD,CAAC,CAAC,CAAC,CAAC,CAAC,IAClBzD,YAAY,CAACyD,CAAC,CAAC,CAAC,CAAC,CAAC,IAClBzD,YAAY,CAACyD,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACjC;AACA,SAASC,WAAWA,CAACD,CAAC,EAAE;EACpB,OAAOzD,YAAY,CAACyD,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIzD,YAAY,CAACyD,CAAC,CAAC,CAAC,CAAC,CAAC;AACnD;AACA,SAASE,YAAYA,CAAClB,KAAK,EAAEgB,CAAC,EAAEG,QAAQ,EAAE;EACtC,IAAIH,CAAC,IAAI,EAAEC,WAAW,CAACD,CAAC,CAAC,IAAID,aAAa,CAACC,CAAC,CAAC,CAAC,EAAE;IAC5C,IAAII,GAAG,GAAGD,QAAQ,GAAG,EAAE,GAAG,GAAG;IAC7BnB,KAAK,CAACqB,SAAS,GAAGN,aAAa,CAACC,CAAC,CAAC,GAC5B,YAAY,GAAGxB,KAAK,CAACwB,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG,CAAC,GAAGA,GAAG,GAAG,GAAG,GAAG5B,KAAK,CAACwB,CAAC,CAAC,CAAC,CAAC,GAAGI,GAAG,CAAC,GAAGA,GAAG,GAAG,GAAG,GAAGlE,YAAY,CAAC8D,CAAC,CAAC;EACxG;AACJ;AACA,SAASM,gBAAgBA,CAACC,KAAK,EAAEvB,KAAK,EAAEoB,GAAG,EAAE;EACzC,IAAII,MAAM,GAAGD,KAAK,CAACC,MAAM;EACzB,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACpCD,MAAM,CAACG,IAAI,CAACpC,KAAK,CAACgC,MAAM,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,GAAG,CAAC,GAAGA,GAAG,CAAC;IAC5CK,MAAM,CAACG,IAAI,CAACpC,KAAK,CAACgC,MAAM,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGN,GAAG,CAAC,GAAGA,GAAG,CAAC;EAChD;EACApB,KAAK,CAACwB,MAAM,GAAGC,MAAM,CAACI,IAAI,CAAC,GAAG,CAAC;AACnC;AACA,SAASC,iBAAiBA,CAACP,KAAK,EAAE;EAC9B,OAAO,CAACA,KAAK,CAACQ,MAAM;AACxB;AACA,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAC9B,IAAIC,cAAc,GAAGpD,GAAG,CAACmD,IAAI,EAAE,UAAUE,IAAI,EAAE;IAC3C,OAAQ,OAAOA,IAAI,KAAK,QAAQ,GAAG,CAACA,IAAI,EAAEA,IAAI,CAAC,GAAGA,IAAI;EAC1D,CAAC,CAAC;EACF,OAAO,UAAUZ,KAAK,EAAEvB,KAAK,EAAEoB,GAAG,EAAE;IAChC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,cAAc,CAACP,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAIS,IAAI,GAAGD,cAAc,CAACR,CAAC,CAAC;MAC5B,IAAI/B,GAAG,GAAG4B,KAAK,CAACY,IAAI,CAAC,CAAC,CAAC,CAAC;MACxB,IAAIxC,GAAG,IAAI,IAAI,EAAE;QACbK,KAAK,CAACmC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG3C,KAAK,CAACG,GAAG,GAAGyB,GAAG,CAAC,GAAGA,GAAG;MAC3C;IACJ;EACJ,CAAC;AACL;AACA,IAAIgB,gBAAgB,GAAG;EACnBC,MAAM,EAAE,CAACL,kBAAkB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;EAC/CM,QAAQ,EAAE,CAAChB,gBAAgB,EAAEQ,iBAAiB,CAAC;EAC/CS,OAAO,EAAE,CAACjB,gBAAgB,EAAEQ,iBAAiB;AACjD,CAAC;AACD,SAASU,iBAAiBA,CAACtC,EAAE,EAAE;EAC3B,IAAIuC,SAAS,GAAGvC,EAAE,CAACuC,SAAS;EAC5B,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,SAAS,CAACd,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIe,SAAS,CAACf,CAAC,CAAC,CAACgB,UAAU,KAAK,OAAO,EAAE;MACrC,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,OAAO,SAASC,YAAYA,CAACzC,EAAE,EAAEC,KAAK,EAAE;EACpC,IAAIF,KAAK,GAAGC,EAAE,CAACD,KAAK;EACpB,IAAIsB,KAAK,GAAGrB,EAAE,CAACqB,KAAK;EACpB,IAAIqB,aAAa,GAAGR,gBAAgB,CAAClC,EAAE,CAAC2C,IAAI,CAAC;EAC7C,IAAI7C,KAAK,GAAG,CAAC,CAAC;EACd,IAAI8C,YAAY,GAAG3C,KAAK,CAAC4C,SAAS;EAClC,IAAIC,SAAS,GAAG,MAAM;EACtB,IAAIC,aAAa,GAAG/C,EAAE,CAACD,KAAK,CAACgD,aAAa;EAC1C,IAAIC,SAAS,GAAI/C,KAAK,CAACgB,QAAQ,IAAIhE,gBAAgB,CAAC+C,EAAE,CAAC,IAAK,CAAC;EAC7D,IAAI0C,aAAa,IACV,CAACzC,KAAK,CAACgD,UAAU,IACjB,EAAEP,aAAa,CAAC,CAAC,CAAC,IAAI,CAACA,aAAa,CAAC,CAAC,CAAC,CAACrB,KAAK,CAAC,CAAC,IAC/C,EAAEuB,YAAY,IAAIN,iBAAiB,CAACtC,EAAE,CAAC,CAAC,IACxC,EAAE+C,aAAa,GAAG,CAAC,CAAC,EAAE;IACzBD,SAAS,GAAG9C,EAAE,CAAC2C,IAAI;IACnB,IAAIzB,GAAG,GAAG3B,IAAI,CAAC2D,GAAG,CAAC,EAAE,EAAEF,SAAS,CAAC;IACjCN,aAAa,CAAC,CAAC,CAAC,CAACrB,KAAK,EAAEvB,KAAK,EAAEoB,GAAG,CAAC;EACvC,CAAC,MACI;IACD,IAAIiC,aAAa,GAAG,CAACnD,EAAE,CAACoD,IAAI,IAAIpD,EAAE,CAACqD,YAAY,CAAC,CAAC;IACjD,IAAI,CAACrD,EAAE,CAACoD,IAAI,EAAE;MACVpD,EAAE,CAACsD,eAAe,CAAC,CAAC;IACxB;IACA,IAAIF,IAAI,GAAGpD,EAAE,CAACoD,IAAI;IAClB,IAAID,aAAa,EAAE;MACfC,IAAI,CAACG,SAAS,CAAC,CAAC;MAChBvD,EAAE,CAACwD,SAAS,CAACJ,IAAI,EAAEpD,EAAE,CAACqB,KAAK,CAAC;MAC5BrB,EAAE,CAACyD,WAAW,CAAC,CAAC;IACpB;IACA,IAAIC,WAAW,GAAGN,IAAI,CAACO,UAAU,CAAC,CAAC;IACnC,IAAIC,KAAK,GAAG5D,EAAE;IACd,IAAI6D,cAAc,GAAGD,KAAK,CAACE,gBAAgB;IAC3C,IAAIF,KAAK,CAACG,gBAAgB,KAAKL,WAAW,IACnC,CAACG,cAAc,IACfd,aAAa,KAAKa,KAAK,CAACI,sBAAsB,EAAE;MACnD,IAAI,CAACH,cAAc,EAAE;QACjBA,cAAc,GAAGD,KAAK,CAACE,gBAAgB,GAAG,IAAI5F,gBAAgB,CAAC,CAAC;MACpE;MACA2F,cAAc,CAACI,KAAK,CAACjB,SAAS,CAAC;MAC/BI,IAAI,CAACc,WAAW,CAACL,cAAc,EAAEd,aAAa,CAAC;MAC/Cc,cAAc,CAACM,WAAW,CAAC,CAAC;MAC5BP,KAAK,CAACG,gBAAgB,GAAGL,WAAW;MACpCE,KAAK,CAACI,sBAAsB,GAAGjB,aAAa;IAChD;IACAjD,KAAK,CAACsE,CAAC,GAAGP,cAAc,CAACQ,MAAM,CAAC,CAAC;EACrC;EACArD,YAAY,CAAClB,KAAK,EAAEE,EAAE,CAACmB,SAAS,CAAC;EACjCtB,aAAa,CAACC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,CAAC;EACtCO,WAAW,CAACV,KAAK,EAAEE,EAAE,CAAC;EACtBC,KAAK,CAAC4C,SAAS,IAAI9D,kBAAkB,CAACiB,EAAE,EAAEF,KAAK,EAAEG,KAAK,CAAC;EACvDA,KAAK,CAACqE,QAAQ,IAAIlF,iBAAiB,CAACY,EAAE,EAAEF,KAAK,EAAEG,KAAK,CAAC;EACrD,OAAO7B,WAAW,CAAC0E,SAAS,EAAE9C,EAAE,CAACuE,EAAE,GAAG,EAAE,EAAEzE,KAAK,CAAC;AACpD;AACA,OAAO,SAAS0E,aAAaA,CAACxE,EAAE,EAAEC,KAAK,EAAE;EACrC,IAAIF,KAAK,GAAGC,EAAE,CAACD,KAAK;EACpB,IAAI0E,KAAK,GAAG1E,KAAK,CAAC0E,KAAK;EACvB,IAAIA,KAAK,IAAI,CAAC/F,QAAQ,CAAC+F,KAAK,CAAC,EAAE;IAC3B,IAAIjF,WAAW,CAACiF,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAGA,KAAK,CAAC/E,GAAG;IACrB,CAAC,MACI,IAAIC,YAAY,CAAC8E,KAAK,CAAC,EAAE;MAC1BA,KAAK,GAAGA,KAAK,CAAC7E,SAAS,CAAC,CAAC;IAC7B;EACJ;EACA,IAAI,CAAC6E,KAAK,EAAE;IACR;EACJ;EACA,IAAIC,CAAC,GAAG3E,KAAK,CAAC2E,CAAC,IAAI,CAAC;EACpB,IAAIC,CAAC,GAAG5E,KAAK,CAAC4E,CAAC,IAAI,CAAC;EACpB,IAAIC,EAAE,GAAG7E,KAAK,CAAC8E,KAAK;EACpB,IAAIC,EAAE,GAAG/E,KAAK,CAACgF,MAAM;EACrB,IAAIjF,KAAK,GAAG;IACRkF,IAAI,EAAEP,KAAK;IACXI,KAAK,EAAED,EAAE;IACTG,MAAM,EAAED;EACZ,CAAC;EACD,IAAIJ,CAAC,EAAE;IACH5E,KAAK,CAAC4E,CAAC,GAAGA,CAAC;EACf;EACA,IAAIC,CAAC,EAAE;IACH7E,KAAK,CAAC6E,CAAC,GAAGA,CAAC;EACf;EACA3D,YAAY,CAAClB,KAAK,EAAEE,EAAE,CAACmB,SAAS,CAAC;EACjCtB,aAAa,CAACC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,CAAC;EACtCO,WAAW,CAACV,KAAK,EAAEE,EAAE,CAAC;EACtBC,KAAK,CAAC4C,SAAS,IAAI9D,kBAAkB,CAACiB,EAAE,EAAEF,KAAK,EAAEG,KAAK,CAAC;EACvD,OAAO7B,WAAW,CAAC,OAAO,EAAE4B,EAAE,CAACuE,EAAE,GAAG,EAAE,EAAEzE,KAAK,CAAC;AAClD;AACA;AACA,OAAO,SAASmF,aAAaA,CAACjF,EAAE,EAAEC,KAAK,EAAE;EACrC,IAAIF,KAAK,GAAGC,EAAE,CAACD,KAAK;EACpB,IAAImF,IAAI,GAAGnF,KAAK,CAACmF,IAAI;EACrBA,IAAI,IAAI,IAAI,KAAKA,IAAI,IAAI,EAAE,CAAC;EAC5B,IAAI,CAACA,IAAI,IAAIC,KAAK,CAACpF,KAAK,CAAC2E,CAAC,CAAC,IAAIS,KAAK,CAACpF,KAAK,CAAC4E,CAAC,CAAC,EAAE;IAC3C;EACJ;EACA,IAAIS,IAAI,GAAGrF,KAAK,CAACqF,IAAI,IAAIlG,YAAY;EACrC,IAAIwF,CAAC,GAAG3E,KAAK,CAAC2E,CAAC,IAAI,CAAC;EACpB,IAAIC,CAAC,GAAG7H,WAAW,CAACiD,KAAK,CAAC4E,CAAC,IAAI,CAAC,EAAE3G,aAAa,CAACoH,IAAI,CAAC,EAAErF,KAAK,CAACsF,YAAY,CAAC;EAC1E,IAAIC,SAAS,GAAGzH,oBAAoB,CAACkC,KAAK,CAACuF,SAAS,CAAC,IAC9CvF,KAAK,CAACuF,SAAS;EACtB,IAAIxF,KAAK,GAAG;IACR,mBAAmB,EAAE,SAAS;IAC9B,aAAa,EAAEwF;EACnB,CAAC;EACD,IAAItG,eAAe,CAACe,KAAK,CAAC,EAAE;IACxB,IAAIwF,gBAAgB,GAAG,EAAE;IACzB,IAAIC,SAAS,GAAGzF,KAAK,CAACyF,SAAS;IAC/B,IAAIC,QAAQ,GAAGxG,aAAa,CAACc,KAAK,CAAC0F,QAAQ,CAAC;IAC5C,IAAI,CAACC,UAAU,CAACD,QAAQ,CAAC,EAAE;MACvB;IACJ;IACA,IAAIE,UAAU,GAAG5F,KAAK,CAAC4F,UAAU,IAAIxG,mBAAmB;IACxD,IAAIyG,UAAU,GAAG7F,KAAK,CAAC6F,UAAU;IACjCL,gBAAgB,IAAI,YAAY,GAAGE,QAAQ,GAAG,eAAe,GAAGE,UAAU,GAAG,GAAG;IAChF,IAAIH,SAAS,IAAIA,SAAS,KAAK,QAAQ,EAAE;MACrCD,gBAAgB,IAAI,aAAa,GAAGC,SAAS,GAAG,GAAG;IACvD;IACA,IAAII,UAAU,IAAIA,UAAU,KAAK,QAAQ,EAAE;MACvCL,gBAAgB,IAAI,cAAc,GAAGK,UAAU,GAAG,GAAG;IACzD;IACA9F,KAAK,CAACC,KAAK,GAAGwF,gBAAgB;EAClC,CAAC,MACI;IACDzF,KAAK,CAACC,KAAK,GAAG,QAAQ,GAAGqF,IAAI;EACjC;EACA,IAAIF,IAAI,CAACW,KAAK,CAAC,IAAI,CAAC,EAAE;IAClB/F,KAAK,CAAC,WAAW,CAAC,GAAG,UAAU;EACnC;EACA,IAAI4E,CAAC,EAAE;IACH5E,KAAK,CAAC4E,CAAC,GAAGA,CAAC;EACf;EACA,IAAIC,CAAC,EAAE;IACH7E,KAAK,CAAC6E,CAAC,GAAGA,CAAC;EACf;EACA3D,YAAY,CAAClB,KAAK,EAAEE,EAAE,CAACmB,SAAS,CAAC;EACjCtB,aAAa,CAACC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,KAAK,CAAC;EACtCO,WAAW,CAACV,KAAK,EAAEE,EAAE,CAAC;EACtBC,KAAK,CAAC4C,SAAS,IAAI9D,kBAAkB,CAACiB,EAAE,EAAEF,KAAK,EAAEG,KAAK,CAAC;EACvD,OAAO7B,WAAW,CAAC,MAAM,EAAE4B,EAAE,CAACuE,EAAE,GAAG,EAAE,EAAEzE,KAAK,EAAEgG,SAAS,EAAEZ,IAAI,CAAC;AAClE;AACA,OAAO,SAASa,KAAKA,CAAC/F,EAAE,EAAEC,KAAK,EAAE;EAC7B,IAAID,EAAE,YAAYlC,IAAI,EAAE;IACpB,OAAO2E,YAAY,CAACzC,EAAE,EAAEC,KAAK,CAAC;EAClC,CAAC,MACI,IAAID,EAAE,YAAYjC,OAAO,EAAE;IAC5B,OAAOyG,aAAa,CAACxE,EAAE,EAAEC,KAAK,CAAC;EACnC,CAAC,MACI,IAAID,EAAE,YAAY/B,KAAK,EAAE;IAC1B,OAAOgH,aAAa,CAACjF,EAAE,EAAEC,KAAK,CAAC;EACnC;AACJ;AACA,SAASM,SAASA,CAACP,EAAE,EAAEF,KAAK,EAAEG,KAAK,EAAE;EACjC,IAAIF,KAAK,GAAGC,EAAE,CAACD,KAAK;EACpB,IAAI3C,SAAS,CAAC2C,KAAK,CAAC,EAAE;IAClB,IAAIiG,SAAS,GAAG9I,YAAY,CAAC8C,EAAE,CAAC;IAChC,IAAIiG,WAAW,GAAGhG,KAAK,CAACgG,WAAW;IACnC,IAAIC,QAAQ,GAAGD,WAAW,CAACD,SAAS,CAAC;IACrC,IAAI,CAACE,QAAQ,EAAE;MACX,IAAIC,WAAW,GAAGnG,EAAE,CAACoG,cAAc,CAAC,CAAC;MACrC,IAAIC,MAAM,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC3B,IAAIG,MAAM,GAAGH,WAAW,CAAC,CAAC,CAAC;MAC3B,IAAI,CAACE,MAAM,IAAI,CAACC,MAAM,EAAE;QACpB;MACJ;MACA,IAAIC,OAAO,GAAGxG,KAAK,CAACyG,aAAa,IAAI,CAAC;MACtC,IAAIC,OAAO,GAAG1G,KAAK,CAAC2G,aAAa,IAAI,CAAC;MACtC,IAAIC,MAAM,GAAG5G,KAAK,CAAC6G,UAAU;MAC7B,IAAIC,EAAE,GAAGlJ,cAAc,CAACoC,KAAK,CAAC+G,WAAW,CAAC;QAAEC,OAAO,GAAGF,EAAE,CAACE,OAAO;QAAEC,KAAK,GAAGH,EAAE,CAACG,KAAK;MAClF,IAAIC,KAAK,GAAGN,MAAM,GAAG,CAAC,GAAGN,MAAM;MAC/B,IAAIa,KAAK,GAAGP,MAAM,GAAG,CAAC,GAAGL,MAAM;MAC/B,IAAIa,YAAY,GAAGF,KAAK,GAAG,GAAG,GAAGC,KAAK;MACtChB,QAAQ,GAAGjG,KAAK,CAACmH,IAAI,GAAG,IAAI,GAAGnH,KAAK,CAACoH,SAAS,EAAE;MAChDpH,KAAK,CAACqH,IAAI,CAACpB,QAAQ,CAAC,GAAG9H,WAAW,CAAC,QAAQ,EAAE8H,QAAQ,EAAE;QACnD,IAAI,EAAEA,QAAQ;QACd,GAAG,EAAE,OAAO;QACZ,GAAG,EAAE,OAAO;QACZ,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;MACd,CAAC,EAAE,CACC9H,WAAW,CAAC,cAAc,EAAE,EAAE,EAAE;QAC5B,IAAI,EAAEmI,OAAO,GAAGF,MAAM;QACtB,IAAI,EAAEI,OAAO,GAAGH,MAAM;QACtB,cAAc,EAAEa,YAAY;QAC5B,aAAa,EAAEH,KAAK;QACpB,eAAe,EAAED;MACrB,CAAC,CAAC,CACL,CAAC;MACFd,WAAW,CAACD,SAAS,CAAC,GAAGE,QAAQ;IACrC;IACApG,KAAK,CAACyH,MAAM,GAAGxK,QAAQ,CAACmJ,QAAQ,CAAC;EACrC;AACJ;AACA,OAAO,SAAS9F,WAAWA,CAACL,KAAK,EAAED,KAAK,EAAE0H,MAAM,EAAEvH,KAAK,EAAE;EACrD,IAAIR,GAAG,GAAGM,KAAK,CAACyH,MAAM,CAAC;EACvB,IAAIC,WAAW;EACf,IAAIC,aAAa,GAAG;IAChB,eAAe,EAAEjI,GAAG,CAACkI,MAAM,GACrB,gBAAgB,GAChB;EACV,CAAC;EACD,IAAInK,gBAAgB,CAACiC,GAAG,CAAC,EAAE;IACvBgI,WAAW,GAAG,gBAAgB;IAC9BC,aAAa,CAACE,EAAE,GAAGnI,GAAG,CAACiF,CAAC;IACxBgD,aAAa,CAACG,EAAE,GAAGpI,GAAG,CAACkF,CAAC;IACxB+C,aAAa,CAACI,EAAE,GAAGrI,GAAG,CAACqI,EAAE;IACzBJ,aAAa,CAACK,EAAE,GAAGtI,GAAG,CAACsI,EAAE;EAC7B,CAAC,MACI,IAAIrK,gBAAgB,CAAC+B,GAAG,CAAC,EAAE;IAC5BgI,WAAW,GAAG,gBAAgB;IAC9BC,aAAa,CAACM,EAAE,GAAGnJ,SAAS,CAACY,GAAG,CAACiF,CAAC,EAAE,GAAG,CAAC;IACxCgD,aAAa,CAACO,EAAE,GAAGpJ,SAAS,CAACY,GAAG,CAACkF,CAAC,EAAE,GAAG,CAAC;IACxC+C,aAAa,CAACQ,CAAC,GAAGrJ,SAAS,CAACY,GAAG,CAACyI,CAAC,EAAE,GAAG,CAAC;EAC3C,CAAC,MACI;IACD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC1J,QAAQ,CAAC,wBAAwB,CAAC;IACtC;IACA;EACJ;EACA,IAAI2J,MAAM,GAAG7I,GAAG,CAAC8I,UAAU;EAC3B,IAAIA,UAAU,GAAG,EAAE;EACnB,KAAK,IAAI/G,CAAC,GAAG,CAAC,EAAEgH,GAAG,GAAGF,MAAM,CAAC7G,MAAM,EAAED,CAAC,GAAGgH,GAAG,EAAE,EAAEhH,CAAC,EAAE;IAC/C,IAAIiH,MAAM,GAAG7K,MAAM,CAAC0K,MAAM,CAAC9G,CAAC,CAAC,CAACiH,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG;IACjD,IAAIC,SAAS,GAAGJ,MAAM,CAAC9G,CAAC,CAAC,CAACwF,KAAK;IAC/B,IAAIH,EAAE,GAAGlJ,cAAc,CAAC+K,SAAS,CAAC;MAAE1B,KAAK,GAAGH,EAAE,CAACG,KAAK;MAAED,OAAO,GAAGF,EAAE,CAACE,OAAO;IAC1E,IAAI4B,UAAU,GAAG;MACb,QAAQ,EAAEF;IACd,CAAC;IACDE,UAAU,CAAC,YAAY,CAAC,GAAG3B,KAAK;IAChC,IAAID,OAAO,GAAG,CAAC,EAAE;MACb4B,UAAU,CAAC,cAAc,CAAC,GAAG5B,OAAO;IACxC;IACAwB,UAAU,CAAC7G,IAAI,CAACtD,WAAW,CAAC,MAAM,EAAEoD,CAAC,GAAG,EAAE,EAAEmH,UAAU,CAAC,CAAC;EAC5D;EACA,IAAIC,aAAa,GAAGxK,WAAW,CAACqJ,WAAW,EAAE,EAAE,EAAEC,aAAa,EAAEa,UAAU,CAAC;EAC3E,IAAIM,WAAW,GAAGxK,aAAa,CAACuK,aAAa,CAAC;EAC9C,IAAIE,aAAa,GAAG7I,KAAK,CAAC6I,aAAa;EACvC,IAAIC,UAAU,GAAGD,aAAa,CAACD,WAAW,CAAC;EAC3C,IAAI,CAACE,UAAU,EAAE;IACbA,UAAU,GAAG9I,KAAK,CAACmH,IAAI,GAAG,IAAI,GAAGnH,KAAK,CAAC+I,WAAW,EAAE;IACpDF,aAAa,CAACD,WAAW,CAAC,GAAGE,UAAU;IACvCrB,aAAa,CAACnD,EAAE,GAAGwE,UAAU;IAC7B9I,KAAK,CAACqH,IAAI,CAACyB,UAAU,CAAC,GAAG3K,WAAW,CAACqJ,WAAW,EAAEsB,UAAU,EAAErB,aAAa,EAAEa,UAAU,CAAC;EAC5F;EACAzI,KAAK,CAAC0H,MAAM,CAAC,GAAGzK,QAAQ,CAACgM,UAAU,CAAC;AACxC;AACA,OAAO,SAAS1I,UAAUA,CAACL,EAAE,EAAEF,KAAK,EAAE0H,MAAM,EAAEvH,KAAK,EAAE;EACjD,IAAIR,GAAG,GAAGO,EAAE,CAACD,KAAK,CAACyH,MAAM,CAAC;EAC1B,IAAIyB,YAAY,GAAGjJ,EAAE,CAACkJ,eAAe,CAAC,CAAC;EACvC,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIC,MAAM,GAAG3J,GAAG,CAAC2J,MAAM;EACvB,IAAIC,QAAQ,GAAGD,MAAM,KAAK,WAAW;EACrC,IAAIE,OAAO,GAAGF,MAAM,KAAK,UAAU;EACnC,IAAIG,OAAO,GAAGH,MAAM,KAAK,UAAU;EACnC,IAAII,KAAK;EACT,IAAIjM,cAAc,CAACkC,GAAG,CAAC,EAAE;IACrB,IAAIgK,YAAY,GAAGhK,GAAG,CAACiK,UAAU;IACjC,IAAIC,aAAa,GAAGlK,GAAG,CAACmK,WAAW;IACnC,IAAIC,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAIC,YAAY,GAAGrK,GAAG,CAACgF,KAAK;IAC5B,IAAI/F,QAAQ,CAACoL,YAAY,CAAC,EAAE;MACxBD,QAAQ,GAAGC,YAAY;IAC3B,CAAC,MACI,IAAItK,WAAW,CAACsK,YAAY,CAAC,EAAE;MAChCD,QAAQ,GAAGC,YAAY,CAACpK,GAAG;IAC/B,CAAC,MACI,IAAIC,YAAY,CAACmK,YAAY,CAAC,EAAE;MACjCD,QAAQ,GAAGC,YAAY,CAAClK,SAAS,CAAC,CAAC;IACvC;IACA,IAAI,OAAOmK,KAAK,KAAK,WAAW,EAAE;MAC9B,IAAIC,MAAM,GAAG,mEAAmE;MAChFzL,MAAM,CAACkL,YAAY,EAAEO,MAAM,CAAC;MAC5BzL,MAAM,CAACoL,aAAa,EAAEK,MAAM,CAAC;IACjC,CAAC,MACI,IAAIP,YAAY,IAAI,IAAI,IAAIE,aAAa,IAAI,IAAI,EAAE;MACpD,IAAIM,gBAAgB,GAAG,SAAAA,CAAUC,KAAK,EAAEC,GAAG,EAAE;QACzC,IAAID,KAAK,EAAE;UACP,IAAIE,KAAK,GAAGF,KAAK,CAACG,GAAG;UACrB,IAAIxF,KAAK,GAAG4E,YAAY,IAAIU,GAAG,CAACtF,KAAK;UACrC,IAAIE,MAAM,GAAG4E,aAAa,IAAIQ,GAAG,CAACpF,MAAM;UACxC,IAAImF,KAAK,CAACI,GAAG,KAAK,SAAS,EAAE;YACzB,IAAIhB,OAAO,EAAE;cACTvE,MAAM,GAAG,CAAC;cACVF,KAAK,IAAIoE,YAAY,CAACpE,KAAK;YAC/B,CAAC,MACI,IAAI0E,OAAO,EAAE;cACd1E,KAAK,GAAG,CAAC;cACTE,MAAM,IAAIkE,YAAY,CAAClE,MAAM;YACjC;UACJ;UACAmF,KAAK,CAACpK,KAAK,CAAC+E,KAAK,GAAGA,KAAK;UACzBqF,KAAK,CAACpK,KAAK,CAACiF,MAAM,GAAGA,MAAM;UAC3B,IAAIqF,KAAK,EAAE;YACPA,KAAK,CAACG,YAAY,CAAC,OAAO,EAAE1F,KAAK,CAAC;YAClCuF,KAAK,CAACG,YAAY,CAAC,QAAQ,EAAExF,MAAM,CAAC;UACxC;QACJ;MACJ,CAAC;MACD,IAAIyF,YAAY,GAAG1L,mBAAmB,CAAC+K,QAAQ,EAAE,IAAI,EAAE7J,EAAE,EAAE,UAAUmK,GAAG,EAAE;QACtEd,QAAQ,IAAIY,gBAAgB,CAACQ,YAAY,EAAEN,GAAG,CAAC;QAC/CF,gBAAgB,CAACT,KAAK,EAAEW,GAAG,CAAC;MAChC,CAAC,CAAC;MACF,IAAIK,YAAY,IAAIA,YAAY,CAAC3F,KAAK,IAAI2F,YAAY,CAACzF,MAAM,EAAE;QAC3D0E,YAAY,GAAGA,YAAY,IAAIe,YAAY,CAAC3F,KAAK;QACjD8E,aAAa,GAAGA,aAAa,IAAIa,YAAY,CAACzF,MAAM;MACxD;IACJ;IACAyE,KAAK,GAAGpL,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE;MAChC4G,IAAI,EAAE6E,QAAQ;MACdhF,KAAK,EAAE4E,YAAY;MACnB1E,MAAM,EAAE4E;IACZ,CAAC,CAAC;IACFR,YAAY,CAACtE,KAAK,GAAG4E,YAAY;IACjCN,YAAY,CAACpE,MAAM,GAAG4E,aAAa;EACvC,CAAC,MACI,IAAIlK,GAAG,CAACiL,UAAU,EAAE;IACrBlB,KAAK,GAAGhL,KAAK,CAACiB,GAAG,CAACiL,UAAU,CAAC;IAC7BvB,YAAY,CAACtE,KAAK,GAAGpF,GAAG,CAACkL,QAAQ;IACjCxB,YAAY,CAACpE,MAAM,GAAGtF,GAAG,CAACmL,SAAS;EACvC;EACA,IAAI,CAACpB,KAAK,EAAE;IACR;EACJ;EACA,IAAIqB,YAAY;EAChB,IAAIC,aAAa;EACjB,IAAIzB,QAAQ,EAAE;IACVwB,YAAY,GAAGC,aAAa,GAAG,CAAC;EACpC,CAAC,MACI,IAAIxB,OAAO,EAAE;IACdwB,aAAa,GAAG,CAAC;IACjBD,YAAY,GAAG1B,YAAY,CAACtE,KAAK,GAAGoE,YAAY,CAACpE,KAAK;EAC1D,CAAC,MACI,IAAI0E,OAAO,EAAE;IACdsB,YAAY,GAAG,CAAC;IAChBC,aAAa,GAAG3B,YAAY,CAACpE,MAAM,GAAGkE,YAAY,CAAClE,MAAM;EAC7D,CAAC,MACI;IACDoE,YAAY,CAAC4B,YAAY,GAAG,gBAAgB;EAChD;EACA,IAAIF,YAAY,IAAI,IAAI,IAAI,CAAC1F,KAAK,CAAC0F,YAAY,CAAC,EAAE;IAC9C1B,YAAY,CAACtE,KAAK,GAAGgG,YAAY;EACrC;EACA,IAAIC,aAAa,IAAI,IAAI,IAAI,CAAC3F,KAAK,CAAC2F,aAAa,CAAC,EAAE;IAChD3B,YAAY,CAACpE,MAAM,GAAG+F,aAAa;EACvC;EACA,IAAIE,gBAAgB,GAAG7N,qBAAqB,CAACsC,GAAG,CAAC;EACjDuL,gBAAgB,KAAK7B,YAAY,CAAC6B,gBAAgB,GAAGA,gBAAgB,CAAC;EACtE,IAAIP,YAAY,GAAGrM,WAAW,CAAC,SAAS,EAAE,EAAE,EAAE+K,YAAY,EAAE,CAACK,KAAK,CAAC,CAAC;EACpE,IAAIyB,UAAU,GAAG5M,aAAa,CAACoM,YAAY,CAAC;EAC5C,IAAIS,YAAY,GAAGjL,KAAK,CAACiL,YAAY;EACrC,IAAIC,SAAS,GAAGD,YAAY,CAACD,UAAU,CAAC;EACxC,IAAI,CAACE,SAAS,EAAE;IACZA,SAAS,GAAGlL,KAAK,CAACmH,IAAI,GAAG,IAAI,GAAGnH,KAAK,CAACmL,UAAU,EAAE;IAClDF,YAAY,CAACD,UAAU,CAAC,GAAGE,SAAS;IACpChC,YAAY,CAAC5E,EAAE,GAAG4G,SAAS;IAC3BV,YAAY,GAAGxK,KAAK,CAACqH,IAAI,CAAC6D,SAAS,CAAC,GAAG/M,WAAW,CAAC,SAAS,EAAE+M,SAAS,EAAEhC,YAAY,EAAE,CAACK,KAAK,CAAC,CAAC;EACnG;EACA1J,KAAK,CAAC0H,MAAM,CAAC,GAAGzK,QAAQ,CAACoO,SAAS,CAAC;AACvC;AACA,OAAO,SAASE,WAAWA,CAACC,QAAQ,EAAExL,KAAK,EAAEG,KAAK,EAAE;EAChD,IAAIsL,aAAa,GAAGtL,KAAK,CAACsL,aAAa;IAAEjE,IAAI,GAAGrH,KAAK,CAACqH,IAAI;EAC1D,IAAIkE,UAAU,GAAGD,aAAa,CAACD,QAAQ,CAAC/G,EAAE,CAAC;EAC3C,IAAI,CAACiH,UAAU,EAAE;IACbA,UAAU,GAAGvL,KAAK,CAACmH,IAAI,GAAG,IAAI,GAAGnH,KAAK,CAACwL,WAAW,EAAE;IACpD,IAAIC,aAAa,GAAG;MAChBnH,EAAE,EAAEiH;IACR,CAAC;IACDD,aAAa,CAACD,QAAQ,CAAC/G,EAAE,CAAC,GAAGiH,UAAU;IACvClE,IAAI,CAACkE,UAAU,CAAC,GAAGpN,WAAW,CAAC,UAAU,EAAEoN,UAAU,EAAEE,aAAa,EAAE,CAACjJ,YAAY,CAAC6I,QAAQ,EAAErL,KAAK,CAAC,CAAC,CAAC;EAC1G;EACAH,KAAK,CAAC,WAAW,CAAC,GAAG/C,QAAQ,CAACyO,UAAU,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}