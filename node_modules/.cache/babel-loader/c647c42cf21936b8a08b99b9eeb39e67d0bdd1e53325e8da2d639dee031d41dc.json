{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport SeriesModel from '../../model/Series.js';\nvar GaugeSeriesModel = /** @class */function (_super) {\n  __extends(GaugeSeriesModel, _super);\n  function GaugeSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GaugeSeriesModel.type;\n    _this.visualStyleAccessPath = 'itemStyle';\n    return _this;\n  }\n  GaugeSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, ['value']);\n  };\n  GaugeSeriesModel.type = 'series.gauge';\n  GaugeSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    colorBy: 'data',\n    // 默认全局居中\n    center: ['50%', '50%'],\n    legendHoverLink: true,\n    radius: '75%',\n    startAngle: 225,\n    endAngle: -45,\n    clockwise: true,\n    // 最小值\n    min: 0,\n    // 最大值\n    max: 100,\n    // 分割段数，默认为10\n    splitNumber: 10,\n    // 坐标轴线\n    axisLine: {\n      // 默认显示，属性show控制显示与否\n      show: true,\n      roundCap: false,\n      lineStyle: {\n        color: [[1, '#E6EBF8']],\n        width: 10\n      }\n    },\n    // 坐标轴线\n    progress: {\n      // 默认显示，属性show控制显示与否\n      show: false,\n      overlap: true,\n      width: 10,\n      roundCap: false,\n      clip: true\n    },\n    // 分隔线\n    splitLine: {\n      // 默认显示，属性show控制显示与否\n      show: true,\n      // 属性length控制线长\n      length: 10,\n      distance: 10,\n      // 属性lineStyle（详见lineStyle）控制线条样式\n      lineStyle: {\n        color: '#63677A',\n        width: 3,\n        type: 'solid'\n      }\n    },\n    // 坐标轴小标记\n    axisTick: {\n      // 属性show控制显示与否，默认不显示\n      show: true,\n      // 每份split细分多少段\n      splitNumber: 5,\n      // 属性length控制线长\n      length: 6,\n      distance: 10,\n      // 属性lineStyle控制线条样式\n      lineStyle: {\n        color: '#63677A',\n        width: 1,\n        type: 'solid'\n      }\n    },\n    axisLabel: {\n      show: true,\n      distance: 15,\n      // formatter: null,\n      color: '#464646',\n      fontSize: 12,\n      rotate: 0\n    },\n    pointer: {\n      icon: null,\n      offsetCenter: [0, 0],\n      show: true,\n      showAbove: true,\n      length: '60%',\n      width: 6,\n      keepAspect: false\n    },\n    anchor: {\n      show: false,\n      showAbove: false,\n      size: 6,\n      icon: 'circle',\n      offsetCenter: [0, 0],\n      keepAspect: false,\n      itemStyle: {\n        color: '#fff',\n        borderWidth: 0,\n        borderColor: '#5470c6'\n      }\n    },\n    title: {\n      show: true,\n      // x, y，单位px\n      offsetCenter: [0, '20%'],\n      // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n      color: '#464646',\n      fontSize: 16,\n      valueAnimation: false\n    },\n    detail: {\n      show: true,\n      backgroundColor: 'rgba(0,0,0,0)',\n      borderWidth: 0,\n      borderColor: '#ccc',\n      width: 100,\n      height: null,\n      padding: [5, 10],\n      // x, y，单位px\n      offsetCenter: [0, '40%'],\n      // formatter: null,\n      // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n      color: '#464646',\n      fontSize: 30,\n      fontWeight: 'bold',\n      lineHeight: 30,\n      valueAnimation: false\n    }\n  };\n  return GaugeSeriesModel;\n}(SeriesModel);\nexport default GaugeSeriesModel;", "map": {"version": 3, "names": ["__extends", "createSeriesDataSimply", "SeriesModel", "GaugeSeriesModel", "_super", "_this", "apply", "arguments", "type", "visualStyleAccessPath", "prototype", "getInitialData", "option", "ecModel", "defaultOption", "z", "colorBy", "center", "legendHoverLink", "radius", "startAngle", "endAngle", "clockwise", "min", "max", "splitNumber", "axisLine", "show", "roundCap", "lineStyle", "color", "width", "progress", "overlap", "clip", "splitLine", "length", "distance", "axisTick", "axisLabel", "fontSize", "rotate", "pointer", "icon", "offsetCenter", "showAbove", "keepAspect", "anchor", "size", "itemStyle", "borderWidth", "borderColor", "title", "valueAnimation", "detail", "backgroundColor", "height", "padding", "fontWeight", "lineHeight"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/chart/gauge/GaugeSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport createSeriesDataSimply from '../helper/createSeriesDataSimply.js';\nimport SeriesModel from '../../model/Series.js';\nvar GaugeSeriesModel = /** @class */function (_super) {\n  __extends(GaugeSeriesModel, _super);\n  function GaugeSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GaugeSeriesModel.type;\n    _this.visualStyleAccessPath = 'itemStyle';\n    return _this;\n  }\n  GaugeSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesDataSimply(this, ['value']);\n  };\n  GaugeSeriesModel.type = 'series.gauge';\n  GaugeSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    colorBy: 'data',\n    // 默认全局居中\n    center: ['50%', '50%'],\n    legendHoverLink: true,\n    radius: '75%',\n    startAngle: 225,\n    endAngle: -45,\n    clockwise: true,\n    // 最小值\n    min: 0,\n    // 最大值\n    max: 100,\n    // 分割段数，默认为10\n    splitNumber: 10,\n    // 坐标轴线\n    axisLine: {\n      // 默认显示，属性show控制显示与否\n      show: true,\n      roundCap: false,\n      lineStyle: {\n        color: [[1, '#E6EBF8']],\n        width: 10\n      }\n    },\n    // 坐标轴线\n    progress: {\n      // 默认显示，属性show控制显示与否\n      show: false,\n      overlap: true,\n      width: 10,\n      roundCap: false,\n      clip: true\n    },\n    // 分隔线\n    splitLine: {\n      // 默认显示，属性show控制显示与否\n      show: true,\n      // 属性length控制线长\n      length: 10,\n      distance: 10,\n      // 属性lineStyle（详见lineStyle）控制线条样式\n      lineStyle: {\n        color: '#63677A',\n        width: 3,\n        type: 'solid'\n      }\n    },\n    // 坐标轴小标记\n    axisTick: {\n      // 属性show控制显示与否，默认不显示\n      show: true,\n      // 每份split细分多少段\n      splitNumber: 5,\n      // 属性length控制线长\n      length: 6,\n      distance: 10,\n      // 属性lineStyle控制线条样式\n      lineStyle: {\n        color: '#63677A',\n        width: 1,\n        type: 'solid'\n      }\n    },\n    axisLabel: {\n      show: true,\n      distance: 15,\n      // formatter: null,\n      color: '#464646',\n      fontSize: 12,\n      rotate: 0\n    },\n    pointer: {\n      icon: null,\n      offsetCenter: [0, 0],\n      show: true,\n      showAbove: true,\n      length: '60%',\n      width: 6,\n      keepAspect: false\n    },\n    anchor: {\n      show: false,\n      showAbove: false,\n      size: 6,\n      icon: 'circle',\n      offsetCenter: [0, 0],\n      keepAspect: false,\n      itemStyle: {\n        color: '#fff',\n        borderWidth: 0,\n        borderColor: '#5470c6'\n      }\n    },\n    title: {\n      show: true,\n      // x, y，单位px\n      offsetCenter: [0, '20%'],\n      // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n      color: '#464646',\n      fontSize: 16,\n      valueAnimation: false\n    },\n    detail: {\n      show: true,\n      backgroundColor: 'rgba(0,0,0,0)',\n      borderWidth: 0,\n      borderColor: '#ccc',\n      width: 100,\n      height: null,\n      padding: [5, 10],\n      // x, y，单位px\n      offsetCenter: [0, '40%'],\n      // formatter: null,\n      // 其余属性默认使用全局文本样式，详见TEXTSTYLE\n      color: '#464646',\n      fontSize: 30,\n      fontWeight: 'bold',\n      lineHeight: 30,\n      valueAnimation: false\n    }\n  };\n  return GaugeSeriesModel;\n}(SeriesModel);\nexport default GaugeSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACpDJ,SAAS,CAACG,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClCH,KAAK,CAACI,qBAAqB,GAAG,WAAW;IACzC,OAAOJ,KAAK;EACd;EACAF,gBAAgB,CAACO,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACrE,OAAOZ,sBAAsB,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;EAChD,CAAC;EACDE,gBAAgB,CAACK,IAAI,GAAG,cAAc;EACtCL,gBAAgB,CAACW,aAAa,GAAG;IAC/B;IACAC,CAAC,EAAE,CAAC;IACJC,OAAO,EAAE,MAAM;IACf;IACAC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACtBC,eAAe,EAAE,IAAI;IACrBC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,CAAC,EAAE;IACbC,SAAS,EAAE,IAAI;IACf;IACAC,GAAG,EAAE,CAAC;IACN;IACAC,GAAG,EAAE,GAAG;IACR;IACAC,WAAW,EAAE,EAAE;IACf;IACAC,QAAQ,EAAE;MACR;MACAC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QACvBC,KAAK,EAAE;MACT;IACF,CAAC;IACD;IACAC,QAAQ,EAAE;MACR;MACAL,IAAI,EAAE,KAAK;MACXM,OAAO,EAAE,IAAI;MACbF,KAAK,EAAE,EAAE;MACTH,QAAQ,EAAE,KAAK;MACfM,IAAI,EAAE;IACR,CAAC;IACD;IACAC,SAAS,EAAE;MACT;MACAR,IAAI,EAAE,IAAI;MACV;MACAS,MAAM,EAAE,EAAE;MACVC,QAAQ,EAAE,EAAE;MACZ;MACAR,SAAS,EAAE;QACTC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,CAAC;QACRvB,IAAI,EAAE;MACR;IACF,CAAC;IACD;IACA8B,QAAQ,EAAE;MACR;MACAX,IAAI,EAAE,IAAI;MACV;MACAF,WAAW,EAAE,CAAC;MACd;MACAW,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZ;MACAR,SAAS,EAAE;QACTC,KAAK,EAAE,SAAS;QAChBC,KAAK,EAAE,CAAC;QACRvB,IAAI,EAAE;MACR;IACF,CAAC;IACD+B,SAAS,EAAE;MACTZ,IAAI,EAAE,IAAI;MACVU,QAAQ,EAAE,EAAE;MACZ;MACAP,KAAK,EAAE,SAAS;MAChBU,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE;IACV,CAAC;IACDC,OAAO,EAAE;MACPC,IAAI,EAAE,IAAI;MACVC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACpBjB,IAAI,EAAE,IAAI;MACVkB,SAAS,EAAE,IAAI;MACfT,MAAM,EAAE,KAAK;MACbL,KAAK,EAAE,CAAC;MACRe,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNpB,IAAI,EAAE,KAAK;MACXkB,SAAS,EAAE,KAAK;MAChBG,IAAI,EAAE,CAAC;MACPL,IAAI,EAAE,QAAQ;MACdC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACpBE,UAAU,EAAE,KAAK;MACjBG,SAAS,EAAE;QACTnB,KAAK,EAAE,MAAM;QACboB,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,KAAK,EAAE;MACLzB,IAAI,EAAE,IAAI;MACV;MACAiB,YAAY,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;MACxB;MACAd,KAAK,EAAE,SAAS;MAChBU,QAAQ,EAAE,EAAE;MACZa,cAAc,EAAE;IAClB,CAAC;IACDC,MAAM,EAAE;MACN3B,IAAI,EAAE,IAAI;MACV4B,eAAe,EAAE,eAAe;MAChCL,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,MAAM;MACnBpB,KAAK,EAAE,GAAG;MACVyB,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MAChB;MACAb,YAAY,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;MACxB;MACA;MACAd,KAAK,EAAE,SAAS;MAChBU,QAAQ,EAAE,EAAE;MACZkB,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE,EAAE;MACdN,cAAc,EAAE;IAClB;EACF,CAAC;EACD,OAAOlD,gBAAgB;AACzB,CAAC,CAACD,WAAW,CAAC;AACd,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}