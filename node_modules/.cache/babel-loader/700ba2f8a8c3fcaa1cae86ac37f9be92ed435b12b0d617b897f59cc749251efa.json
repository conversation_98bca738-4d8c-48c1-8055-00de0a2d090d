{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { isArray } from 'zrender/lib/core/util.js';\n/**\r\n * @return label string. Not null/undefined\r\n */\nexport function getDefaultLabel(data, dataIndex) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  var len = labelDims.length;\n  // Simple optimization (in lots of cases, label dims length is 1)\n  if (len === 1) {\n    var rawVal = retrieveRawValue(data, dataIndex, labelDims[0]);\n    return rawVal != null ? rawVal + '' : null;\n  } else if (len) {\n    var vals = [];\n    for (var i = 0; i < labelDims.length; i++) {\n      vals.push(retrieveRawValue(data, dataIndex, labelDims[i]));\n    }\n    return vals.join(' ');\n  }\n}\nexport function getDefaultInterpolatedLabel(data, interpolatedValue) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  if (!isArray(interpolatedValue)) {\n    return interpolatedValue + '';\n  }\n  var vals = [];\n  for (var i = 0; i < labelDims.length; i++) {\n    var dimIndex = data.getDimensionIndex(labelDims[i]);\n    if (dimIndex >= 0) {\n      vals.push(interpolatedValue[dimIndex]);\n    }\n  }\n  return vals.join(' ');\n}", "map": {"version": 3, "names": ["retrieveRawValue", "isArray", "getDefaultLabel", "data", "dataIndex", "labelDims", "mapDimensionsAll", "len", "length", "rawVal", "vals", "i", "push", "join", "getDefaultInterpolatedLabel", "interpolatedV<PERSON>ue", "dimIndex", "getDimensionIndex"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/chart/helper/labelHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { isArray } from 'zrender/lib/core/util.js';\n/**\r\n * @return label string. Not null/undefined\r\n */\nexport function getDefaultLabel(data, dataIndex) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  var len = labelDims.length;\n  // Simple optimization (in lots of cases, label dims length is 1)\n  if (len === 1) {\n    var rawVal = retrieveRawValue(data, dataIndex, labelDims[0]);\n    return rawVal != null ? rawVal + '' : null;\n  } else if (len) {\n    var vals = [];\n    for (var i = 0; i < labelDims.length; i++) {\n      vals.push(retrieveRawValue(data, dataIndex, labelDims[i]));\n    }\n    return vals.join(' ');\n  }\n}\nexport function getDefaultInterpolatedLabel(data, interpolatedValue) {\n  var labelDims = data.mapDimensionsAll('defaultedLabel');\n  if (!isArray(interpolatedValue)) {\n    return interpolatedValue + '';\n  }\n  var vals = [];\n  for (var i = 0; i < labelDims.length; i++) {\n    var dimIndex = data.getDimensionIndex(labelDims[i]);\n    if (dimIndex >= 0) {\n      vals.push(interpolatedValue[dimIndex]);\n    }\n  }\n  return vals.join(' ');\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,gBAAgB,QAAQ,mCAAmC;AACpE,SAASC,OAAO,QAAQ,0BAA0B;AAClD;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAC/C,IAAIC,SAAS,GAAGF,IAAI,CAACG,gBAAgB,CAAC,gBAAgB,CAAC;EACvD,IAAIC,GAAG,GAAGF,SAAS,CAACG,MAAM;EAC1B;EACA,IAAID,GAAG,KAAK,CAAC,EAAE;IACb,IAAIE,MAAM,GAAGT,gBAAgB,CAACG,IAAI,EAAEC,SAAS,EAAEC,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5D,OAAOI,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAG,EAAE,GAAG,IAAI;EAC5C,CAAC,MAAM,IAAIF,GAAG,EAAE;IACd,IAAIG,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,SAAS,CAACG,MAAM,EAAEG,CAAC,EAAE,EAAE;MACzCD,IAAI,CAACE,IAAI,CAACZ,gBAAgB,CAACG,IAAI,EAAEC,SAAS,EAAEC,SAAS,CAACM,CAAC,CAAC,CAAC,CAAC;IAC5D;IACA,OAAOD,IAAI,CAACG,IAAI,CAAC,GAAG,CAAC;EACvB;AACF;AACA,OAAO,SAASC,2BAA2BA,CAACX,IAAI,EAAEY,iBAAiB,EAAE;EACnE,IAAIV,SAAS,GAAGF,IAAI,CAACG,gBAAgB,CAAC,gBAAgB,CAAC;EACvD,IAAI,CAACL,OAAO,CAACc,iBAAiB,CAAC,EAAE;IAC/B,OAAOA,iBAAiB,GAAG,EAAE;EAC/B;EACA,IAAIL,IAAI,GAAG,EAAE;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,SAAS,CAACG,MAAM,EAAEG,CAAC,EAAE,EAAE;IACzC,IAAIK,QAAQ,GAAGb,IAAI,CAACc,iBAAiB,CAACZ,SAAS,CAACM,CAAC,CAAC,CAAC;IACnD,IAAIK,QAAQ,IAAI,CAAC,EAAE;MACjBN,IAAI,CAACE,IAAI,CAACG,iBAAiB,CAACC,QAAQ,CAAC,CAAC;IACxC;EACF;EACA,OAAON,IAAI,CAACG,IAAI,CAAC,GAAG,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}