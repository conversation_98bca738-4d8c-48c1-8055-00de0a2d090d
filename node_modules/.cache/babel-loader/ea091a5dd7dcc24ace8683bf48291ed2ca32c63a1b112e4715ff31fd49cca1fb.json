{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getLayoutRect } from '../../util/layout.js';\nvar paramsSet = [['left', 'right', 'width'], ['top', 'bottom', 'height']];\n/**\r\n * @param visualMapModel\r\n * @param api\r\n * @param itemSize always [short, long]\r\n * @return {string} 'left' or 'right' or 'top' or 'bottom'\r\n */\nexport function getItemAlign(visualMapModel, api, itemSize) {\n  var modelOption = visualMapModel.option;\n  var itemAlign = modelOption.align;\n  if (itemAlign != null && itemAlign !== 'auto') {\n    return itemAlign;\n  }\n  // Auto decision align.\n  var ecSize = {\n    width: api.getWidth(),\n    height: api.getHeight()\n  };\n  var realIndex = modelOption.orient === 'horizontal' ? 1 : 0;\n  var reals = paramsSet[realIndex];\n  var fakeValue = [0, null, 10];\n  var layoutInput = {};\n  for (var i = 0; i < 3; i++) {\n    layoutInput[paramsSet[1 - realIndex][i]] = fakeValue[i];\n    layoutInput[reals[i]] = i === 2 ? itemSize[0] : modelOption[reals[i]];\n  }\n  var rParam = [['x', 'width', 3], ['y', 'height', 0]][realIndex];\n  var rect = getLayoutRect(layoutInput, ecSize, modelOption.padding);\n  return reals[(rect.margin[rParam[2]] || 0) + rect[rParam[0]] + rect[rParam[1]] * 0.5 < ecSize[rParam[1]] * 0.5 ? 0 : 1];\n}\n/**\r\n * Prepare dataIndex for outside usage, where dataIndex means rawIndex, and\r\n * dataIndexInside means filtered index.\r\n */\n// TODO: TYPE more specified payload types.\nexport function makeHighDownBatch(batch, visualMapModel) {\n  zrUtil.each(batch || [], function (batchItem) {\n    if (batchItem.dataIndex != null) {\n      batchItem.dataIndexInside = batchItem.dataIndex;\n      batchItem.dataIndex = null;\n    }\n    batchItem.highlightKey = 'visualMap' + (visualMapModel ? visualMapModel.componentIndex : '');\n  });\n  return batch;\n}", "map": {"version": 3, "names": ["zrUtil", "getLayoutRect", "paramsSet", "getItemAlign", "visualMapModel", "api", "itemSize", "modelOption", "option", "itemAlign", "align", "ecSize", "width", "getWidth", "height", "getHeight", "realIndex", "orient", "reals", "fakeValue", "layoutInput", "i", "rParam", "rect", "padding", "margin", "makeHighDownBatch", "batch", "each", "batchItem", "dataIndex", "dataIndexInside", "highlight<PERSON><PERSON>", "componentIndex"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/visualMap/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getLayoutRect } from '../../util/layout.js';\nvar paramsSet = [['left', 'right', 'width'], ['top', 'bottom', 'height']];\n/**\r\n * @param visualMapModel\r\n * @param api\r\n * @param itemSize always [short, long]\r\n * @return {string} 'left' or 'right' or 'top' or 'bottom'\r\n */\nexport function getItemAlign(visualMapModel, api, itemSize) {\n  var modelOption = visualMapModel.option;\n  var itemAlign = modelOption.align;\n  if (itemAlign != null && itemAlign !== 'auto') {\n    return itemAlign;\n  }\n  // Auto decision align.\n  var ecSize = {\n    width: api.getWidth(),\n    height: api.getHeight()\n  };\n  var realIndex = modelOption.orient === 'horizontal' ? 1 : 0;\n  var reals = paramsSet[realIndex];\n  var fakeValue = [0, null, 10];\n  var layoutInput = {};\n  for (var i = 0; i < 3; i++) {\n    layoutInput[paramsSet[1 - realIndex][i]] = fakeValue[i];\n    layoutInput[reals[i]] = i === 2 ? itemSize[0] : modelOption[reals[i]];\n  }\n  var rParam = [['x', 'width', 3], ['y', 'height', 0]][realIndex];\n  var rect = getLayoutRect(layoutInput, ecSize, modelOption.padding);\n  return reals[(rect.margin[rParam[2]] || 0) + rect[rParam[0]] + rect[rParam[1]] * 0.5 < ecSize[rParam[1]] * 0.5 ? 0 : 1];\n}\n/**\r\n * Prepare dataIndex for outside usage, where dataIndex means rawIndex, and\r\n * dataIndexInside means filtered index.\r\n */\n// TODO: TYPE more specified payload types.\nexport function makeHighDownBatch(batch, visualMapModel) {\n  zrUtil.each(batch || [], function (batchItem) {\n    if (batchItem.dataIndex != null) {\n      batchItem.dataIndexInside = batchItem.dataIndex;\n      batchItem.dataIndex = null;\n    }\n    batchItem.highlightKey = 'visualMap' + (visualMapModel ? visualMapModel.componentIndex : '');\n  });\n  return batch;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,IAAIC,SAAS,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,cAAc,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EAC1D,IAAIC,WAAW,GAAGH,cAAc,CAACI,MAAM;EACvC,IAAIC,SAAS,GAAGF,WAAW,CAACG,KAAK;EACjC,IAAID,SAAS,IAAI,IAAI,IAAIA,SAAS,KAAK,MAAM,EAAE;IAC7C,OAAOA,SAAS;EAClB;EACA;EACA,IAAIE,MAAM,GAAG;IACXC,KAAK,EAAEP,GAAG,CAACQ,QAAQ,CAAC,CAAC;IACrBC,MAAM,EAAET,GAAG,CAACU,SAAS,CAAC;EACxB,CAAC;EACD,IAAIC,SAAS,GAAGT,WAAW,CAACU,MAAM,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;EAC3D,IAAIC,KAAK,GAAGhB,SAAS,CAACc,SAAS,CAAC;EAChC,IAAIG,SAAS,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC;EAC7B,IAAIC,WAAW,GAAG,CAAC,CAAC;EACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BD,WAAW,CAAClB,SAAS,CAAC,CAAC,GAAGc,SAAS,CAAC,CAACK,CAAC,CAAC,CAAC,GAAGF,SAAS,CAACE,CAAC,CAAC;IACvDD,WAAW,CAACF,KAAK,CAACG,CAAC,CAAC,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC,GAAGC,WAAW,CAACW,KAAK,CAACG,CAAC,CAAC,CAAC;EACvE;EACA,IAAIC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAACN,SAAS,CAAC;EAC/D,IAAIO,IAAI,GAAGtB,aAAa,CAACmB,WAAW,EAAET,MAAM,EAAEJ,WAAW,CAACiB,OAAO,CAAC;EAClE,OAAON,KAAK,CAAC,CAACK,IAAI,CAACE,MAAM,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIC,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGC,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGX,MAAM,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACzH;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,iBAAiBA,CAACC,KAAK,EAAEvB,cAAc,EAAE;EACvDJ,MAAM,CAAC4B,IAAI,CAACD,KAAK,IAAI,EAAE,EAAE,UAAUE,SAAS,EAAE;IAC5C,IAAIA,SAAS,CAACC,SAAS,IAAI,IAAI,EAAE;MAC/BD,SAAS,CAACE,eAAe,GAAGF,SAAS,CAACC,SAAS;MAC/CD,SAAS,CAACC,SAAS,GAAG,IAAI;IAC5B;IACAD,SAAS,CAACG,YAAY,GAAG,WAAW,IAAI5B,cAAc,GAAGA,cAAc,CAAC6B,cAAc,GAAG,EAAE,CAAC;EAC9F,CAAC,CAAC;EACF,OAAON,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}