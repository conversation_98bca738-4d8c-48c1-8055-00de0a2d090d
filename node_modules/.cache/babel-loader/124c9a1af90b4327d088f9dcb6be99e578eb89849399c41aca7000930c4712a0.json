{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar each = zrUtil.each;\n/**\r\n * @param {string} key\r\n * @param {module:echarts/ExtensionAPI} api\r\n * @param {Function} handler\r\n *      param: {string} currTrigger\r\n *      param: {Array.<number>} point\r\n */\nexport function register(key, api, handler) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  inner(zr).records || (inner(zr).records = {});\n  initGlobalListeners(zr, api);\n  var record = inner(zr).records[key] || (inner(zr).records[key] = {});\n  record.handler = handler;\n}\nfunction initGlobalListeners(zr, api) {\n  if (inner(zr).initialized) {\n    return;\n  }\n  inner(zr).initialized = true;\n  useHandler('click', zrUtil.curry(doEnter, 'click'));\n  useHandler('mousemove', zrUtil.curry(doEnter, 'mousemove'));\n  // useHandler('mouseout', onLeave);\n  useHandler('globalout', onLeave);\n  function useHandler(eventType, cb) {\n    zr.on(eventType, function (e) {\n      var dis = makeDispatchAction(api);\n      each(inner(zr).records, function (record) {\n        record && cb(record, e, dis.dispatchAction);\n      });\n      dispatchTooltipFinally(dis.pendings, api);\n    });\n  }\n}\nfunction dispatchTooltipFinally(pendings, api) {\n  var showLen = pendings.showTip.length;\n  var hideLen = pendings.hideTip.length;\n  var actuallyPayload;\n  if (showLen) {\n    actuallyPayload = pendings.showTip[showLen - 1];\n  } else if (hideLen) {\n    actuallyPayload = pendings.hideTip[hideLen - 1];\n  }\n  if (actuallyPayload) {\n    actuallyPayload.dispatchAction = null;\n    api.dispatchAction(actuallyPayload);\n  }\n}\nfunction onLeave(record, e, dispatchAction) {\n  record.handler('leave', null, dispatchAction);\n}\nfunction doEnter(currTrigger, record, e, dispatchAction) {\n  record.handler(currTrigger, e, dispatchAction);\n}\nfunction makeDispatchAction(api) {\n  var pendings = {\n    showTip: [],\n    hideTip: []\n  };\n  // FIXME\n  // better approach?\n  // 'showTip' and 'hideTip' can be triggered by axisPointer and tooltip,\n  // which may be conflict, (axisPointer call showTip but tooltip call hideTip);\n  // So we have to add \"final stage\" to merge those dispatched actions.\n  var dispatchAction = function (payload) {\n    var pendingList = pendings[payload.type];\n    if (pendingList) {\n      pendingList.push(payload);\n    } else {\n      payload.dispatchAction = dispatchAction;\n      api.dispatchAction(payload);\n    }\n  };\n  return {\n    dispatchAction: dispatchAction,\n    pendings: pendings\n  };\n}\nexport function unregister(key, api) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  var record = (inner(zr).records || {})[key];\n  if (record) {\n    inner(zr).records[key] = null;\n  }\n}", "map": {"version": 3, "names": ["zrUtil", "env", "makeInner", "inner", "each", "register", "key", "api", "handler", "node", "zr", "getZr", "records", "initGlobalListeners", "record", "initialized", "useHandler", "curry", "do<PERSON>nter", "onLeave", "eventType", "cb", "on", "e", "dis", "makeDispatchAction", "dispatchAction", "dispatchTooltipFinally", "pendings", "showLen", "showTip", "length", "hideLen", "hideTip", "actuallyPayload", "currTrigger", "payload", "pendingList", "type", "push", "unregister"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/axisPointer/globalListener.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar each = zrUtil.each;\n/**\r\n * @param {string} key\r\n * @param {module:echarts/ExtensionAPI} api\r\n * @param {Function} handler\r\n *      param: {string} currTrigger\r\n *      param: {Array.<number>} point\r\n */\nexport function register(key, api, handler) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  inner(zr).records || (inner(zr).records = {});\n  initGlobalListeners(zr, api);\n  var record = inner(zr).records[key] || (inner(zr).records[key] = {});\n  record.handler = handler;\n}\nfunction initGlobalListeners(zr, api) {\n  if (inner(zr).initialized) {\n    return;\n  }\n  inner(zr).initialized = true;\n  useHandler('click', zrUtil.curry(doEnter, 'click'));\n  useHandler('mousemove', zrUtil.curry(doEnter, 'mousemove'));\n  // useHandler('mouseout', onLeave);\n  useHandler('globalout', onLeave);\n  function useHandler(eventType, cb) {\n    zr.on(eventType, function (e) {\n      var dis = makeDispatchAction(api);\n      each(inner(zr).records, function (record) {\n        record && cb(record, e, dis.dispatchAction);\n      });\n      dispatchTooltipFinally(dis.pendings, api);\n    });\n  }\n}\nfunction dispatchTooltipFinally(pendings, api) {\n  var showLen = pendings.showTip.length;\n  var hideLen = pendings.hideTip.length;\n  var actuallyPayload;\n  if (showLen) {\n    actuallyPayload = pendings.showTip[showLen - 1];\n  } else if (hideLen) {\n    actuallyPayload = pendings.hideTip[hideLen - 1];\n  }\n  if (actuallyPayload) {\n    actuallyPayload.dispatchAction = null;\n    api.dispatchAction(actuallyPayload);\n  }\n}\nfunction onLeave(record, e, dispatchAction) {\n  record.handler('leave', null, dispatchAction);\n}\nfunction doEnter(currTrigger, record, e, dispatchAction) {\n  record.handler(currTrigger, e, dispatchAction);\n}\nfunction makeDispatchAction(api) {\n  var pendings = {\n    showTip: [],\n    hideTip: []\n  };\n  // FIXME\n  // better approach?\n  // 'showTip' and 'hideTip' can be triggered by axisPointer and tooltip,\n  // which may be conflict, (axisPointer call showTip but tooltip call hideTip);\n  // So we have to add \"final stage\" to merge those dispatched actions.\n  var dispatchAction = function (payload) {\n    var pendingList = pendings[payload.type];\n    if (pendingList) {\n      pendingList.push(payload);\n    } else {\n      payload.dispatchAction = dispatchAction;\n      api.dispatchAction(payload);\n    }\n  };\n  return {\n    dispatchAction: dispatchAction,\n    pendings: pendings\n  };\n}\nexport function unregister(key, api) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  var record = (inner(zr).records || {})[key];\n  if (record) {\n    inner(zr).records[key] = null;\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,GAAG,MAAM,yBAAyB;AACzC,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,KAAK,GAAGD,SAAS,CAAC,CAAC;AACvB,IAAIE,IAAI,GAAGJ,MAAM,CAACI,IAAI;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,GAAG,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC1C,IAAIP,GAAG,CAACQ,IAAI,EAAE;IACZ;EACF;EACA,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC;EACpBR,KAAK,CAACO,EAAE,CAAC,CAACE,OAAO,KAAKT,KAAK,CAACO,EAAE,CAAC,CAACE,OAAO,GAAG,CAAC,CAAC,CAAC;EAC7CC,mBAAmB,CAACH,EAAE,EAAEH,GAAG,CAAC;EAC5B,IAAIO,MAAM,GAAGX,KAAK,CAACO,EAAE,CAAC,CAACE,OAAO,CAACN,GAAG,CAAC,KAAKH,KAAK,CAACO,EAAE,CAAC,CAACE,OAAO,CAACN,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACpEQ,MAAM,CAACN,OAAO,GAAGA,OAAO;AAC1B;AACA,SAASK,mBAAmBA,CAACH,EAAE,EAAEH,GAAG,EAAE;EACpC,IAAIJ,KAAK,CAACO,EAAE,CAAC,CAACK,WAAW,EAAE;IACzB;EACF;EACAZ,KAAK,CAACO,EAAE,CAAC,CAACK,WAAW,GAAG,IAAI;EAC5BC,UAAU,CAAC,OAAO,EAAEhB,MAAM,CAACiB,KAAK,CAACC,OAAO,EAAE,OAAO,CAAC,CAAC;EACnDF,UAAU,CAAC,WAAW,EAAEhB,MAAM,CAACiB,KAAK,CAACC,OAAO,EAAE,WAAW,CAAC,CAAC;EAC3D;EACAF,UAAU,CAAC,WAAW,EAAEG,OAAO,CAAC;EAChC,SAASH,UAAUA,CAACI,SAAS,EAAEC,EAAE,EAAE;IACjCX,EAAE,CAACY,EAAE,CAACF,SAAS,EAAE,UAAUG,CAAC,EAAE;MAC5B,IAAIC,GAAG,GAAGC,kBAAkB,CAAClB,GAAG,CAAC;MACjCH,IAAI,CAACD,KAAK,CAACO,EAAE,CAAC,CAACE,OAAO,EAAE,UAAUE,MAAM,EAAE;QACxCA,MAAM,IAAIO,EAAE,CAACP,MAAM,EAAES,CAAC,EAAEC,GAAG,CAACE,cAAc,CAAC;MAC7C,CAAC,CAAC;MACFC,sBAAsB,CAACH,GAAG,CAACI,QAAQ,EAAErB,GAAG,CAAC;IAC3C,CAAC,CAAC;EACJ;AACF;AACA,SAASoB,sBAAsBA,CAACC,QAAQ,EAAErB,GAAG,EAAE;EAC7C,IAAIsB,OAAO,GAAGD,QAAQ,CAACE,OAAO,CAACC,MAAM;EACrC,IAAIC,OAAO,GAAGJ,QAAQ,CAACK,OAAO,CAACF,MAAM;EACrC,IAAIG,eAAe;EACnB,IAAIL,OAAO,EAAE;IACXK,eAAe,GAAGN,QAAQ,CAACE,OAAO,CAACD,OAAO,GAAG,CAAC,CAAC;EACjD,CAAC,MAAM,IAAIG,OAAO,EAAE;IAClBE,eAAe,GAAGN,QAAQ,CAACK,OAAO,CAACD,OAAO,GAAG,CAAC,CAAC;EACjD;EACA,IAAIE,eAAe,EAAE;IACnBA,eAAe,CAACR,cAAc,GAAG,IAAI;IACrCnB,GAAG,CAACmB,cAAc,CAACQ,eAAe,CAAC;EACrC;AACF;AACA,SAASf,OAAOA,CAACL,MAAM,EAAES,CAAC,EAAEG,cAAc,EAAE;EAC1CZ,MAAM,CAACN,OAAO,CAAC,OAAO,EAAE,IAAI,EAAEkB,cAAc,CAAC;AAC/C;AACA,SAASR,OAAOA,CAACiB,WAAW,EAAErB,MAAM,EAAES,CAAC,EAAEG,cAAc,EAAE;EACvDZ,MAAM,CAACN,OAAO,CAAC2B,WAAW,EAAEZ,CAAC,EAAEG,cAAc,CAAC;AAChD;AACA,SAASD,kBAAkBA,CAAClB,GAAG,EAAE;EAC/B,IAAIqB,QAAQ,GAAG;IACbE,OAAO,EAAE,EAAE;IACXG,OAAO,EAAE;EACX,CAAC;EACD;EACA;EACA;EACA;EACA;EACA,IAAIP,cAAc,GAAG,SAAAA,CAAUU,OAAO,EAAE;IACtC,IAAIC,WAAW,GAAGT,QAAQ,CAACQ,OAAO,CAACE,IAAI,CAAC;IACxC,IAAID,WAAW,EAAE;MACfA,WAAW,CAACE,IAAI,CAACH,OAAO,CAAC;IAC3B,CAAC,MAAM;MACLA,OAAO,CAACV,cAAc,GAAGA,cAAc;MACvCnB,GAAG,CAACmB,cAAc,CAACU,OAAO,CAAC;IAC7B;EACF,CAAC;EACD,OAAO;IACLV,cAAc,EAAEA,cAAc;IAC9BE,QAAQ,EAAEA;EACZ,CAAC;AACH;AACA,OAAO,SAASY,UAAUA,CAAClC,GAAG,EAAEC,GAAG,EAAE;EACnC,IAAIN,GAAG,CAACQ,IAAI,EAAE;IACZ;EACF;EACA,IAAIC,EAAE,GAAGH,GAAG,CAACI,KAAK,CAAC,CAAC;EACpB,IAAIG,MAAM,GAAG,CAACX,KAAK,CAACO,EAAE,CAAC,CAACE,OAAO,IAAI,CAAC,CAAC,EAAEN,GAAG,CAAC;EAC3C,IAAIQ,MAAM,EAAE;IACVX,KAAK,CAACO,EAAE,CAAC,CAACE,OAAO,CAACN,GAAG,CAAC,GAAG,IAAI;EAC/B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}