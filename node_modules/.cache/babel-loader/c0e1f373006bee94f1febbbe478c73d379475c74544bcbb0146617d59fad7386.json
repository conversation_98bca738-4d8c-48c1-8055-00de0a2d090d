{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport dataZoomProcessor from './dataZoomProcessor.js';\nimport installDataZoomAction from './dataZoomAction.js';\nvar installed = false;\nexport default function installCommon(registers) {\n  if (installed) {\n    return;\n  }\n  installed = true;\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.FILTER, dataZoomProcessor);\n  installDataZoomAction(registers);\n  registers.registerSubTypeDefaulter('dataZoom', function () {\n    // Default 'slider' when no type specified.\n    return 'slider';\n  });\n}", "map": {"version": 3, "names": ["dataZoomProcessor", "installDataZoomAction", "installed", "installCommon", "registers", "registerProcessor", "PRIORITY", "PROCESSOR", "FILTER", "registerSubTypeDefaulter"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/dataZoom/installCommon.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport dataZoomProcessor from './dataZoomProcessor.js';\nimport installDataZoomAction from './dataZoomAction.js';\nvar installed = false;\nexport default function installCommon(registers) {\n  if (installed) {\n    return;\n  }\n  installed = true;\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.FILTER, dataZoomProcessor);\n  installDataZoomAction(registers);\n  registers.registerSubTypeDefaulter('dataZoom', function () {\n    // Default 'slider' when no type specified.\n    return 'slider';\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,qBAAqB,MAAM,qBAAqB;AACvD,IAAIC,SAAS,GAAG,KAAK;AACrB,eAAe,SAASC,aAAaA,CAACC,SAAS,EAAE;EAC/C,IAAIF,SAAS,EAAE;IACb;EACF;EACAA,SAAS,GAAG,IAAI;EAChBE,SAAS,CAACC,iBAAiB,CAACD,SAAS,CAACE,QAAQ,CAACC,SAAS,CAACC,MAAM,EAAER,iBAAiB,CAAC;EACnFC,qBAAqB,CAACG,SAAS,CAAC;EAChCA,SAAS,CAACK,wBAAwB,CAAC,UAAU,EAAE,YAAY;IACzD;IACA,OAAO,QAAQ;EACjB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}