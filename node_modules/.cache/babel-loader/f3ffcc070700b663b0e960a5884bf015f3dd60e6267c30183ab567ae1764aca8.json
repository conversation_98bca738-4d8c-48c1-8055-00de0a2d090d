{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isFunction, isString } from 'zrender/lib/core/util.js';\nvar samplers = {\n  average: function (frame) {\n    var sum = 0;\n    var count = 0;\n    for (var i = 0; i < frame.length; i++) {\n      if (!isNaN(frame[i])) {\n        sum += frame[i];\n        count++;\n      }\n    }\n    // Return NaN if count is 0\n    return count === 0 ? NaN : sum / count;\n  },\n  sum: function (frame) {\n    var sum = 0;\n    for (var i = 0; i < frame.length; i++) {\n      // Ignore NaN\n      sum += frame[i] || 0;\n    }\n    return sum;\n  },\n  max: function (frame) {\n    var max = -Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] > max && (max = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(max) ? max : NaN;\n  },\n  min: function (frame) {\n    var min = Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] < min && (min = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(min) ? min : NaN;\n  },\n  // TODO\n  // Median\n  nearest: function (frame) {\n    return frame[0];\n  }\n};\nvar indexSampler = function (frame) {\n  return Math.round(frame.length / 2);\n};\nexport default function dataSample(seriesType) {\n  return {\n    seriesType: seriesType,\n    // FIXME:TS never used, so comment it\n    // modifyOutputEnd: true,\n    reset: function (seriesModel, ecModel, api) {\n      var data = seriesModel.getData();\n      var sampling = seriesModel.get('sampling');\n      var coordSys = seriesModel.coordinateSystem;\n      var count = data.count();\n      // Only cartesian2d support down sampling. Disable it when there is few data.\n      if (count > 10 && coordSys.type === 'cartesian2d' && sampling) {\n        var baseAxis = coordSys.getBaseAxis();\n        var valueAxis = coordSys.getOtherAxis(baseAxis);\n        var extent = baseAxis.getExtent();\n        var dpr = api.getDevicePixelRatio();\n        // Coordinste system has been resized\n        var size = Math.abs(extent[1] - extent[0]) * (dpr || 1);\n        var rate = Math.round(count / size);\n        if (isFinite(rate) && rate > 1) {\n          if (sampling === 'lttb') {\n            seriesModel.setData(data.lttbDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          } else if (sampling === 'minmax') {\n            seriesModel.setData(data.minmaxDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          }\n          var sampler = void 0;\n          if (isString(sampling)) {\n            sampler = samplers[sampling];\n          } else if (isFunction(sampling)) {\n            sampler = sampling;\n          }\n          if (sampler) {\n            // Only support sample the first dim mapped from value axis.\n            seriesModel.setData(data.downSample(data.mapDimension(valueAxis.dim), 1 / rate, sampler, indexSampler));\n          }\n        }\n      }\n    }\n  };\n}", "map": {"version": 3, "names": ["isFunction", "isString", "samplers", "average", "frame", "sum", "count", "i", "length", "isNaN", "NaN", "max", "Infinity", "isFinite", "min", "nearest", "indexSampler", "Math", "round", "dataSample", "seriesType", "reset", "seriesModel", "ecModel", "api", "data", "getData", "sampling", "get", "coordSys", "coordinateSystem", "type", "baseAxis", "getBaseAxis", "valueAxis", "getOtherAxis", "extent", "getExtent", "dpr", "getDevicePixelRatio", "size", "abs", "rate", "setData", "lttbDownSample", "mapDimension", "dim", "minmaxDownSample", "sampler", "downSample"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/processor/dataSample.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isFunction, isString } from 'zrender/lib/core/util.js';\nvar samplers = {\n  average: function (frame) {\n    var sum = 0;\n    var count = 0;\n    for (var i = 0; i < frame.length; i++) {\n      if (!isNaN(frame[i])) {\n        sum += frame[i];\n        count++;\n      }\n    }\n    // Return NaN if count is 0\n    return count === 0 ? NaN : sum / count;\n  },\n  sum: function (frame) {\n    var sum = 0;\n    for (var i = 0; i < frame.length; i++) {\n      // Ignore NaN\n      sum += frame[i] || 0;\n    }\n    return sum;\n  },\n  max: function (frame) {\n    var max = -Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] > max && (max = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(max) ? max : NaN;\n  },\n  min: function (frame) {\n    var min = Infinity;\n    for (var i = 0; i < frame.length; i++) {\n      frame[i] < min && (min = frame[i]);\n    }\n    // NaN will cause illegal axis extent.\n    return isFinite(min) ? min : NaN;\n  },\n  // TODO\n  // Median\n  nearest: function (frame) {\n    return frame[0];\n  }\n};\nvar indexSampler = function (frame) {\n  return Math.round(frame.length / 2);\n};\nexport default function dataSample(seriesType) {\n  return {\n    seriesType: seriesType,\n    // FIXME:TS never used, so comment it\n    // modifyOutputEnd: true,\n    reset: function (seriesModel, ecModel, api) {\n      var data = seriesModel.getData();\n      var sampling = seriesModel.get('sampling');\n      var coordSys = seriesModel.coordinateSystem;\n      var count = data.count();\n      // Only cartesian2d support down sampling. Disable it when there is few data.\n      if (count > 10 && coordSys.type === 'cartesian2d' && sampling) {\n        var baseAxis = coordSys.getBaseAxis();\n        var valueAxis = coordSys.getOtherAxis(baseAxis);\n        var extent = baseAxis.getExtent();\n        var dpr = api.getDevicePixelRatio();\n        // Coordinste system has been resized\n        var size = Math.abs(extent[1] - extent[0]) * (dpr || 1);\n        var rate = Math.round(count / size);\n        if (isFinite(rate) && rate > 1) {\n          if (sampling === 'lttb') {\n            seriesModel.setData(data.lttbDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          } else if (sampling === 'minmax') {\n            seriesModel.setData(data.minmaxDownSample(data.mapDimension(valueAxis.dim), 1 / rate));\n          }\n          var sampler = void 0;\n          if (isString(sampling)) {\n            sampler = samplers[sampling];\n          } else if (isFunction(sampling)) {\n            sampler = sampling;\n          }\n          if (sampler) {\n            // Only support sample the first dim mapped from value axis.\n            seriesModel.setData(data.downSample(data.mapDimension(valueAxis.dim), 1 / rate, sampler, indexSampler));\n          }\n        }\n      }\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAU,EAAEC,QAAQ,QAAQ,0BAA0B;AAC/D,IAAIC,QAAQ,GAAG;EACbC,OAAO,EAAE,SAAAA,CAAUC,KAAK,EAAE;IACxB,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAI,CAACE,KAAK,CAACL,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE;QACpBF,GAAG,IAAID,KAAK,CAACG,CAAC,CAAC;QACfD,KAAK,EAAE;MACT;IACF;IACA;IACA,OAAOA,KAAK,KAAK,CAAC,GAAGI,GAAG,GAAGL,GAAG,GAAGC,KAAK;EACxC,CAAC;EACDD,GAAG,EAAE,SAAAA,CAAUD,KAAK,EAAE;IACpB,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC;MACAF,GAAG,IAAID,KAAK,CAACG,CAAC,CAAC,IAAI,CAAC;IACtB;IACA,OAAOF,GAAG;EACZ,CAAC;EACDM,GAAG,EAAE,SAAAA,CAAUP,KAAK,EAAE;IACpB,IAAIO,GAAG,GAAG,CAACC,QAAQ;IACnB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCH,KAAK,CAACG,CAAC,CAAC,GAAGI,GAAG,KAAKA,GAAG,GAAGP,KAAK,CAACG,CAAC,CAAC,CAAC;IACpC;IACA;IACA,OAAOM,QAAQ,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAGD,GAAG;EAClC,CAAC;EACDI,GAAG,EAAE,SAAAA,CAAUV,KAAK,EAAE;IACpB,IAAIU,GAAG,GAAGF,QAAQ;IAClB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCH,KAAK,CAACG,CAAC,CAAC,GAAGO,GAAG,KAAKA,GAAG,GAAGV,KAAK,CAACG,CAAC,CAAC,CAAC;IACpC;IACA;IACA,OAAOM,QAAQ,CAACC,GAAG,CAAC,GAAGA,GAAG,GAAGJ,GAAG;EAClC,CAAC;EACD;EACA;EACAK,OAAO,EAAE,SAAAA,CAAUX,KAAK,EAAE;IACxB,OAAOA,KAAK,CAAC,CAAC,CAAC;EACjB;AACF,CAAC;AACD,IAAIY,YAAY,GAAG,SAAAA,CAAUZ,KAAK,EAAE;EAClC,OAAOa,IAAI,CAACC,KAAK,CAACd,KAAK,CAACI,MAAM,GAAG,CAAC,CAAC;AACrC,CAAC;AACD,eAAe,SAASW,UAAUA,CAACC,UAAU,EAAE;EAC7C,OAAO;IACLA,UAAU,EAAEA,UAAU;IACtB;IACA;IACAC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;MAC1C,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;MAChC,IAAIC,QAAQ,GAAGL,WAAW,CAACM,GAAG,CAAC,UAAU,CAAC;MAC1C,IAAIC,QAAQ,GAAGP,WAAW,CAACQ,gBAAgB;MAC3C,IAAIxB,KAAK,GAAGmB,IAAI,CAACnB,KAAK,CAAC,CAAC;MACxB;MACA,IAAIA,KAAK,GAAG,EAAE,IAAIuB,QAAQ,CAACE,IAAI,KAAK,aAAa,IAAIJ,QAAQ,EAAE;QAC7D,IAAIK,QAAQ,GAAGH,QAAQ,CAACI,WAAW,CAAC,CAAC;QACrC,IAAIC,SAAS,GAAGL,QAAQ,CAACM,YAAY,CAACH,QAAQ,CAAC;QAC/C,IAAII,MAAM,GAAGJ,QAAQ,CAACK,SAAS,CAAC,CAAC;QACjC,IAAIC,GAAG,GAAGd,GAAG,CAACe,mBAAmB,CAAC,CAAC;QACnC;QACA,IAAIC,IAAI,GAAGvB,IAAI,CAACwB,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIE,GAAG,IAAI,CAAC,CAAC;QACvD,IAAII,IAAI,GAAGzB,IAAI,CAACC,KAAK,CAACZ,KAAK,GAAGkC,IAAI,CAAC;QACnC,IAAI3B,QAAQ,CAAC6B,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE;UAC9B,IAAIf,QAAQ,KAAK,MAAM,EAAE;YACvBL,WAAW,CAACqB,OAAO,CAAClB,IAAI,CAACmB,cAAc,CAACnB,IAAI,CAACoB,YAAY,CAACX,SAAS,CAACY,GAAG,CAAC,EAAE,CAAC,GAAGJ,IAAI,CAAC,CAAC;UACtF,CAAC,MAAM,IAAIf,QAAQ,KAAK,QAAQ,EAAE;YAChCL,WAAW,CAACqB,OAAO,CAAClB,IAAI,CAACsB,gBAAgB,CAACtB,IAAI,CAACoB,YAAY,CAACX,SAAS,CAACY,GAAG,CAAC,EAAE,CAAC,GAAGJ,IAAI,CAAC,CAAC;UACxF;UACA,IAAIM,OAAO,GAAG,KAAK,CAAC;UACpB,IAAI/C,QAAQ,CAAC0B,QAAQ,CAAC,EAAE;YACtBqB,OAAO,GAAG9C,QAAQ,CAACyB,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAI3B,UAAU,CAAC2B,QAAQ,CAAC,EAAE;YAC/BqB,OAAO,GAAGrB,QAAQ;UACpB;UACA,IAAIqB,OAAO,EAAE;YACX;YACA1B,WAAW,CAACqB,OAAO,CAAClB,IAAI,CAACwB,UAAU,CAACxB,IAAI,CAACoB,YAAY,CAACX,SAAS,CAACY,GAAG,CAAC,EAAE,CAAC,GAAGJ,IAAI,EAAEM,OAAO,EAAEhC,YAAY,CAAC,CAAC;UACzG;QACF;MACF;IACF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}