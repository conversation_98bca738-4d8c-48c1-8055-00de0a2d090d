{"ast": null, "code": "import env from '../core/env.js';\nvar requestAnimationFrame;\nrequestAnimationFrame = env.hasGlobalWindow && (window.requestAnimationFrame && window.requestAnimationFrame.bind(window) || window.msRequestAnimationFrame && window.msRequestAnimationFrame.bind(window) || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame) || function (func) {\n  return setTimeout(func, 16);\n};\nexport default requestAnimationFrame;", "map": {"version": 3, "names": ["env", "requestAnimationFrame", "hasGlobalWindow", "window", "bind", "msRequestAnimationFrame", "mozRequestAnimationFrame", "webkitRequestAnimationFrame", "func", "setTimeout"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/zrender/lib/animation/requestAnimationFrame.js"], "sourcesContent": ["import env from '../core/env.js';\nvar requestAnimationFrame;\nrequestAnimationFrame = (env.hasGlobalWindow\n    && ((window.requestAnimationFrame && window.requestAnimationFrame.bind(window))\n        || (window.msRequestAnimationFrame && window.msRequestAnimationFrame.bind(window))\n        || window.mozRequestAnimationFrame\n        || window.webkitRequestAnimationFrame)) || function (func) {\n    return setTimeout(func, 16);\n};\nexport default requestAnimationFrame;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,IAAIC,qBAAqB;AACzBA,qBAAqB,GAAID,GAAG,CAACE,eAAe,KACnCC,MAAM,CAACF,qBAAqB,IAAIE,MAAM,CAACF,qBAAqB,CAACG,IAAI,CAACD,MAAM,CAAC,IACtEA,MAAM,CAACE,uBAAuB,IAAIF,MAAM,CAACE,uBAAuB,CAACD,IAAI,CAACD,MAAM,CAAE,IAC/EA,MAAM,CAACG,wBAAwB,IAC/BH,MAAM,CAACI,2BAA2B,CAAC,IAAK,UAAUC,IAAI,EAAE;EAC/D,OAAOC,UAAU,CAACD,IAAI,EAAE,EAAE,CAAC;AAC/B,CAAC;AACD,eAAeP,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}