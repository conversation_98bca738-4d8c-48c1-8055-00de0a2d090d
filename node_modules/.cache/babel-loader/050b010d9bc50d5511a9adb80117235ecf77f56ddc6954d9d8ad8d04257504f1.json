{"ast": null, "code": "import Group from '../graphic/Group.js';\nimport ZRImage from '../graphic/Image.js';\nimport Circle from '../graphic/shape/Circle.js';\nimport Rect from '../graphic/shape/Rect.js';\nimport Ellipse from '../graphic/shape/Ellipse.js';\nimport Line from '../graphic/shape/Line.js';\nimport Polygon from '../graphic/shape/Polygon.js';\nimport Polyline from '../graphic/shape/Polyline.js';\nimport * as matrix from '../core/matrix.js';\nimport { createFromString } from './path.js';\nimport { defaults, trim, each, map, keys, hasOwn } from '../core/util.js';\nimport LinearGradient from '../graphic/LinearGradient.js';\nimport RadialGradient from '../graphic/RadialGradient.js';\nimport TSpan from '../graphic/TSpan.js';\nimport { parseXML } from './parseXML.js';\n;\nvar nodeParsers;\nvar INHERITABLE_STYLE_ATTRIBUTES_MAP = {\n  'fill': 'fill',\n  'stroke': 'stroke',\n  'stroke-width': 'lineWidth',\n  'opacity': 'opacity',\n  'fill-opacity': 'fillOpacity',\n  'stroke-opacity': 'strokeOpacity',\n  'stroke-dasharray': 'lineDash',\n  'stroke-dashoffset': 'lineDashOffset',\n  'stroke-linecap': 'lineCap',\n  'stroke-linejoin': 'lineJoin',\n  'stroke-miterlimit': 'miterLimit',\n  'font-family': 'fontFamily',\n  'font-size': 'fontSize',\n  'font-style': 'fontStyle',\n  'font-weight': 'fontWeight',\n  'text-anchor': 'textAlign',\n  'visibility': 'visibility',\n  'display': 'display'\n};\nvar INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS = keys(INHERITABLE_STYLE_ATTRIBUTES_MAP);\nvar SELF_STYLE_ATTRIBUTES_MAP = {\n  'alignment-baseline': 'textBaseline',\n  'stop-color': 'stopColor'\n};\nvar SELF_STYLE_ATTRIBUTES_MAP_KEYS = keys(SELF_STYLE_ATTRIBUTES_MAP);\nvar SVGParser = function () {\n  function SVGParser() {\n    this._defs = {};\n    this._root = null;\n  }\n  SVGParser.prototype.parse = function (xml, opt) {\n    opt = opt || {};\n    var svg = parseXML(xml);\n    if (process.env.NODE_ENV !== 'production') {\n      if (!svg) {\n        throw new Error('Illegal svg');\n      }\n    }\n    this._defsUsePending = [];\n    var root = new Group();\n    this._root = root;\n    var named = [];\n    var viewBox = svg.getAttribute('viewBox') || '';\n    var width = parseFloat(svg.getAttribute('width') || opt.width);\n    var height = parseFloat(svg.getAttribute('height') || opt.height);\n    isNaN(width) && (width = null);\n    isNaN(height) && (height = null);\n    parseAttributes(svg, root, null, true, false);\n    var child = svg.firstChild;\n    while (child) {\n      this._parseNode(child, root, named, null, false, false);\n      child = child.nextSibling;\n    }\n    applyDefs(this._defs, this._defsUsePending);\n    this._defsUsePending = [];\n    var viewBoxRect;\n    var viewBoxTransform;\n    if (viewBox) {\n      var viewBoxArr = splitNumberSequence(viewBox);\n      if (viewBoxArr.length >= 4) {\n        viewBoxRect = {\n          x: parseFloat(viewBoxArr[0] || 0),\n          y: parseFloat(viewBoxArr[1] || 0),\n          width: parseFloat(viewBoxArr[2]),\n          height: parseFloat(viewBoxArr[3])\n        };\n      }\n    }\n    if (viewBoxRect && width != null && height != null) {\n      viewBoxTransform = makeViewBoxTransform(viewBoxRect, {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      });\n      if (!opt.ignoreViewBox) {\n        var elRoot = root;\n        root = new Group();\n        root.add(elRoot);\n        elRoot.scaleX = elRoot.scaleY = viewBoxTransform.scale;\n        elRoot.x = viewBoxTransform.x;\n        elRoot.y = viewBoxTransform.y;\n      }\n    }\n    if (!opt.ignoreRootClip && width != null && height != null) {\n      root.setClipPath(new Rect({\n        shape: {\n          x: 0,\n          y: 0,\n          width: width,\n          height: height\n        }\n      }));\n    }\n    return {\n      root: root,\n      width: width,\n      height: height,\n      viewBoxRect: viewBoxRect,\n      viewBoxTransform: viewBoxTransform,\n      named: named\n    };\n  };\n  SVGParser.prototype._parseNode = function (xmlNode, parentGroup, named, namedFrom, isInDefs, isInText) {\n    var nodeName = xmlNode.nodeName.toLowerCase();\n    var el;\n    var namedFromForSub = namedFrom;\n    if (nodeName === 'defs') {\n      isInDefs = true;\n    }\n    if (nodeName === 'text') {\n      isInText = true;\n    }\n    if (nodeName === 'defs' || nodeName === 'switch') {\n      el = parentGroup;\n    } else {\n      if (!isInDefs) {\n        var parser_1 = nodeParsers[nodeName];\n        if (parser_1 && hasOwn(nodeParsers, nodeName)) {\n          el = parser_1.call(this, xmlNode, parentGroup);\n          var nameAttr = xmlNode.getAttribute('name');\n          if (nameAttr) {\n            var newNamed = {\n              name: nameAttr,\n              namedFrom: null,\n              svgNodeTagLower: nodeName,\n              el: el\n            };\n            named.push(newNamed);\n            if (nodeName === 'g') {\n              namedFromForSub = newNamed;\n            }\n          } else if (namedFrom) {\n            named.push({\n              name: namedFrom.name,\n              namedFrom: namedFrom,\n              svgNodeTagLower: nodeName,\n              el: el\n            });\n          }\n          parentGroup.add(el);\n        }\n      }\n      var parser = paintServerParsers[nodeName];\n      if (parser && hasOwn(paintServerParsers, nodeName)) {\n        var def = parser.call(this, xmlNode);\n        var id = xmlNode.getAttribute('id');\n        if (id) {\n          this._defs[id] = def;\n        }\n      }\n    }\n    if (el && el.isGroup) {\n      var child = xmlNode.firstChild;\n      while (child) {\n        if (child.nodeType === 1) {\n          this._parseNode(child, el, named, namedFromForSub, isInDefs, isInText);\n        } else if (child.nodeType === 3 && isInText) {\n          this._parseText(child, el);\n        }\n        child = child.nextSibling;\n      }\n    }\n  };\n  SVGParser.prototype._parseText = function (xmlNode, parentGroup) {\n    var text = new TSpan({\n      style: {\n        text: xmlNode.textContent\n      },\n      silent: true,\n      x: this._textX || 0,\n      y: this._textY || 0\n    });\n    inheritStyle(parentGroup, text);\n    parseAttributes(xmlNode, text, this._defsUsePending, false, false);\n    applyTextAlignment(text, parentGroup);\n    var textStyle = text.style;\n    var fontSize = textStyle.fontSize;\n    if (fontSize && fontSize < 9) {\n      textStyle.fontSize = 9;\n      text.scaleX *= fontSize / 9;\n      text.scaleY *= fontSize / 9;\n    }\n    var font = (textStyle.fontSize || textStyle.fontFamily) && [textStyle.fontStyle, textStyle.fontWeight, (textStyle.fontSize || 12) + 'px', textStyle.fontFamily || 'sans-serif'].join(' ');\n    textStyle.font = font;\n    var rect = text.getBoundingRect();\n    this._textX += rect.width;\n    parentGroup.add(text);\n    return text;\n  };\n  SVGParser.internalField = function () {\n    nodeParsers = {\n      'g': function (xmlNode, parentGroup) {\n        var g = new Group();\n        inheritStyle(parentGroup, g);\n        parseAttributes(xmlNode, g, this._defsUsePending, false, false);\n        return g;\n      },\n      'rect': function (xmlNode, parentGroup) {\n        var rect = new Rect();\n        inheritStyle(parentGroup, rect);\n        parseAttributes(xmlNode, rect, this._defsUsePending, false, false);\n        rect.setShape({\n          x: parseFloat(xmlNode.getAttribute('x') || '0'),\n          y: parseFloat(xmlNode.getAttribute('y') || '0'),\n          width: parseFloat(xmlNode.getAttribute('width') || '0'),\n          height: parseFloat(xmlNode.getAttribute('height') || '0')\n        });\n        rect.silent = true;\n        return rect;\n      },\n      'circle': function (xmlNode, parentGroup) {\n        var circle = new Circle();\n        inheritStyle(parentGroup, circle);\n        parseAttributes(xmlNode, circle, this._defsUsePending, false, false);\n        circle.setShape({\n          cx: parseFloat(xmlNode.getAttribute('cx') || '0'),\n          cy: parseFloat(xmlNode.getAttribute('cy') || '0'),\n          r: parseFloat(xmlNode.getAttribute('r') || '0')\n        });\n        circle.silent = true;\n        return circle;\n      },\n      'line': function (xmlNode, parentGroup) {\n        var line = new Line();\n        inheritStyle(parentGroup, line);\n        parseAttributes(xmlNode, line, this._defsUsePending, false, false);\n        line.setShape({\n          x1: parseFloat(xmlNode.getAttribute('x1') || '0'),\n          y1: parseFloat(xmlNode.getAttribute('y1') || '0'),\n          x2: parseFloat(xmlNode.getAttribute('x2') || '0'),\n          y2: parseFloat(xmlNode.getAttribute('y2') || '0')\n        });\n        line.silent = true;\n        return line;\n      },\n      'ellipse': function (xmlNode, parentGroup) {\n        var ellipse = new Ellipse();\n        inheritStyle(parentGroup, ellipse);\n        parseAttributes(xmlNode, ellipse, this._defsUsePending, false, false);\n        ellipse.setShape({\n          cx: parseFloat(xmlNode.getAttribute('cx') || '0'),\n          cy: parseFloat(xmlNode.getAttribute('cy') || '0'),\n          rx: parseFloat(xmlNode.getAttribute('rx') || '0'),\n          ry: parseFloat(xmlNode.getAttribute('ry') || '0')\n        });\n        ellipse.silent = true;\n        return ellipse;\n      },\n      'polygon': function (xmlNode, parentGroup) {\n        var pointsStr = xmlNode.getAttribute('points');\n        var pointsArr;\n        if (pointsStr) {\n          pointsArr = parsePoints(pointsStr);\n        }\n        var polygon = new Polygon({\n          shape: {\n            points: pointsArr || []\n          },\n          silent: true\n        });\n        inheritStyle(parentGroup, polygon);\n        parseAttributes(xmlNode, polygon, this._defsUsePending, false, false);\n        return polygon;\n      },\n      'polyline': function (xmlNode, parentGroup) {\n        var pointsStr = xmlNode.getAttribute('points');\n        var pointsArr;\n        if (pointsStr) {\n          pointsArr = parsePoints(pointsStr);\n        }\n        var polyline = new Polyline({\n          shape: {\n            points: pointsArr || []\n          },\n          silent: true\n        });\n        inheritStyle(parentGroup, polyline);\n        parseAttributes(xmlNode, polyline, this._defsUsePending, false, false);\n        return polyline;\n      },\n      'image': function (xmlNode, parentGroup) {\n        var img = new ZRImage();\n        inheritStyle(parentGroup, img);\n        parseAttributes(xmlNode, img, this._defsUsePending, false, false);\n        img.setStyle({\n          image: xmlNode.getAttribute('xlink:href') || xmlNode.getAttribute('href'),\n          x: +xmlNode.getAttribute('x'),\n          y: +xmlNode.getAttribute('y'),\n          width: +xmlNode.getAttribute('width'),\n          height: +xmlNode.getAttribute('height')\n        });\n        img.silent = true;\n        return img;\n      },\n      'text': function (xmlNode, parentGroup) {\n        var x = xmlNode.getAttribute('x') || '0';\n        var y = xmlNode.getAttribute('y') || '0';\n        var dx = xmlNode.getAttribute('dx') || '0';\n        var dy = xmlNode.getAttribute('dy') || '0';\n        this._textX = parseFloat(x) + parseFloat(dx);\n        this._textY = parseFloat(y) + parseFloat(dy);\n        var g = new Group();\n        inheritStyle(parentGroup, g);\n        parseAttributes(xmlNode, g, this._defsUsePending, false, true);\n        return g;\n      },\n      'tspan': function (xmlNode, parentGroup) {\n        var x = xmlNode.getAttribute('x');\n        var y = xmlNode.getAttribute('y');\n        if (x != null) {\n          this._textX = parseFloat(x);\n        }\n        if (y != null) {\n          this._textY = parseFloat(y);\n        }\n        var dx = xmlNode.getAttribute('dx') || '0';\n        var dy = xmlNode.getAttribute('dy') || '0';\n        var g = new Group();\n        inheritStyle(parentGroup, g);\n        parseAttributes(xmlNode, g, this._defsUsePending, false, true);\n        this._textX += parseFloat(dx);\n        this._textY += parseFloat(dy);\n        return g;\n      },\n      'path': function (xmlNode, parentGroup) {\n        var d = xmlNode.getAttribute('d') || '';\n        var path = createFromString(d);\n        inheritStyle(parentGroup, path);\n        parseAttributes(xmlNode, path, this._defsUsePending, false, false);\n        path.silent = true;\n        return path;\n      }\n    };\n  }();\n  return SVGParser;\n}();\nvar paintServerParsers = {\n  'lineargradient': function (xmlNode) {\n    var x1 = parseInt(xmlNode.getAttribute('x1') || '0', 10);\n    var y1 = parseInt(xmlNode.getAttribute('y1') || '0', 10);\n    var x2 = parseInt(xmlNode.getAttribute('x2') || '10', 10);\n    var y2 = parseInt(xmlNode.getAttribute('y2') || '0', 10);\n    var gradient = new LinearGradient(x1, y1, x2, y2);\n    parsePaintServerUnit(xmlNode, gradient);\n    parseGradientColorStops(xmlNode, gradient);\n    return gradient;\n  },\n  'radialgradient': function (xmlNode) {\n    var cx = parseInt(xmlNode.getAttribute('cx') || '0', 10);\n    var cy = parseInt(xmlNode.getAttribute('cy') || '0', 10);\n    var r = parseInt(xmlNode.getAttribute('r') || '0', 10);\n    var gradient = new RadialGradient(cx, cy, r);\n    parsePaintServerUnit(xmlNode, gradient);\n    parseGradientColorStops(xmlNode, gradient);\n    return gradient;\n  }\n};\nfunction parsePaintServerUnit(xmlNode, gradient) {\n  var gradientUnits = xmlNode.getAttribute('gradientUnits');\n  if (gradientUnits === 'userSpaceOnUse') {\n    gradient.global = true;\n  }\n}\nfunction parseGradientColorStops(xmlNode, gradient) {\n  var stop = xmlNode.firstChild;\n  while (stop) {\n    if (stop.nodeType === 1 && stop.nodeName.toLocaleLowerCase() === 'stop') {\n      var offsetStr = stop.getAttribute('offset');\n      var offset = void 0;\n      if (offsetStr && offsetStr.indexOf('%') > 0) {\n        offset = parseInt(offsetStr, 10) / 100;\n      } else if (offsetStr) {\n        offset = parseFloat(offsetStr);\n      } else {\n        offset = 0;\n      }\n      var styleVals = {};\n      parseInlineStyle(stop, styleVals, styleVals);\n      var stopColor = styleVals.stopColor || stop.getAttribute('stop-color') || '#000000';\n      gradient.colorStops.push({\n        offset: offset,\n        color: stopColor\n      });\n    }\n    stop = stop.nextSibling;\n  }\n}\nfunction inheritStyle(parent, child) {\n  if (parent && parent.__inheritedStyle) {\n    if (!child.__inheritedStyle) {\n      child.__inheritedStyle = {};\n    }\n    defaults(child.__inheritedStyle, parent.__inheritedStyle);\n  }\n}\nfunction parsePoints(pointsString) {\n  var list = splitNumberSequence(pointsString);\n  var points = [];\n  for (var i = 0; i < list.length; i += 2) {\n    var x = parseFloat(list[i]);\n    var y = parseFloat(list[i + 1]);\n    points.push([x, y]);\n  }\n  return points;\n}\nfunction parseAttributes(xmlNode, el, defsUsePending, onlyInlineStyle, isTextGroup) {\n  var disp = el;\n  var inheritedStyle = disp.__inheritedStyle = disp.__inheritedStyle || {};\n  var selfStyle = {};\n  if (xmlNode.nodeType === 1) {\n    parseTransformAttribute(xmlNode, el);\n    parseInlineStyle(xmlNode, inheritedStyle, selfStyle);\n    if (!onlyInlineStyle) {\n      parseAttributeStyle(xmlNode, inheritedStyle, selfStyle);\n    }\n  }\n  disp.style = disp.style || {};\n  if (inheritedStyle.fill != null) {\n    disp.style.fill = getFillStrokeStyle(disp, 'fill', inheritedStyle.fill, defsUsePending);\n  }\n  if (inheritedStyle.stroke != null) {\n    disp.style.stroke = getFillStrokeStyle(disp, 'stroke', inheritedStyle.stroke, defsUsePending);\n  }\n  each(['lineWidth', 'opacity', 'fillOpacity', 'strokeOpacity', 'miterLimit', 'fontSize'], function (propName) {\n    if (inheritedStyle[propName] != null) {\n      disp.style[propName] = parseFloat(inheritedStyle[propName]);\n    }\n  });\n  each(['lineDashOffset', 'lineCap', 'lineJoin', 'fontWeight', 'fontFamily', 'fontStyle', 'textAlign'], function (propName) {\n    if (inheritedStyle[propName] != null) {\n      disp.style[propName] = inheritedStyle[propName];\n    }\n  });\n  if (isTextGroup) {\n    disp.__selfStyle = selfStyle;\n  }\n  if (inheritedStyle.lineDash) {\n    disp.style.lineDash = map(splitNumberSequence(inheritedStyle.lineDash), function (str) {\n      return parseFloat(str);\n    });\n  }\n  if (inheritedStyle.visibility === 'hidden' || inheritedStyle.visibility === 'collapse') {\n    disp.invisible = true;\n  }\n  if (inheritedStyle.display === 'none') {\n    disp.ignore = true;\n  }\n}\nfunction applyTextAlignment(text, parentGroup) {\n  var parentSelfStyle = parentGroup.__selfStyle;\n  if (parentSelfStyle) {\n    var textBaseline = parentSelfStyle.textBaseline;\n    var zrTextBaseline = textBaseline;\n    if (!textBaseline || textBaseline === 'auto') {\n      zrTextBaseline = 'alphabetic';\n    } else if (textBaseline === 'baseline') {\n      zrTextBaseline = 'alphabetic';\n    } else if (textBaseline === 'before-edge' || textBaseline === 'text-before-edge') {\n      zrTextBaseline = 'top';\n    } else if (textBaseline === 'after-edge' || textBaseline === 'text-after-edge') {\n      zrTextBaseline = 'bottom';\n    } else if (textBaseline === 'central' || textBaseline === 'mathematical') {\n      zrTextBaseline = 'middle';\n    }\n    text.style.textBaseline = zrTextBaseline;\n  }\n  var parentInheritedStyle = parentGroup.__inheritedStyle;\n  if (parentInheritedStyle) {\n    var textAlign = parentInheritedStyle.textAlign;\n    var zrTextAlign = textAlign;\n    if (textAlign) {\n      if (textAlign === 'middle') {\n        zrTextAlign = 'center';\n      }\n      text.style.textAlign = zrTextAlign;\n    }\n  }\n}\nvar urlRegex = /^url\\(\\s*#(.*?)\\)/;\nfunction getFillStrokeStyle(el, method, str, defsUsePending) {\n  var urlMatch = str && str.match(urlRegex);\n  if (urlMatch) {\n    var url = trim(urlMatch[1]);\n    defsUsePending.push([el, method, url]);\n    return;\n  }\n  if (str === 'none') {\n    str = null;\n  }\n  return str;\n}\nfunction applyDefs(defs, defsUsePending) {\n  for (var i = 0; i < defsUsePending.length; i++) {\n    var item = defsUsePending[i];\n    item[0].style[item[1]] = defs[item[2]];\n  }\n}\nvar numberReg = /-?([0-9]*\\.)?[0-9]+([eE]-?[0-9]+)?/g;\nfunction splitNumberSequence(rawStr) {\n  return rawStr.match(numberReg) || [];\n}\nvar transformRegex = /(translate|scale|rotate|skewX|skewY|matrix)\\(([\\-\\s0-9\\.eE,]*)\\)/g;\nvar DEGREE_TO_ANGLE = Math.PI / 180;\nfunction parseTransformAttribute(xmlNode, node) {\n  var transform = xmlNode.getAttribute('transform');\n  if (transform) {\n    transform = transform.replace(/,/g, ' ');\n    var transformOps_1 = [];\n    var mt = null;\n    transform.replace(transformRegex, function (str, type, value) {\n      transformOps_1.push(type, value);\n      return '';\n    });\n    for (var i = transformOps_1.length - 1; i > 0; i -= 2) {\n      var value = transformOps_1[i];\n      var type = transformOps_1[i - 1];\n      var valueArr = splitNumberSequence(value);\n      mt = mt || matrix.create();\n      switch (type) {\n        case 'translate':\n          matrix.translate(mt, mt, [parseFloat(valueArr[0]), parseFloat(valueArr[1] || '0')]);\n          break;\n        case 'scale':\n          matrix.scale(mt, mt, [parseFloat(valueArr[0]), parseFloat(valueArr[1] || valueArr[0])]);\n          break;\n        case 'rotate':\n          matrix.rotate(mt, mt, -parseFloat(valueArr[0]) * DEGREE_TO_ANGLE, [parseFloat(valueArr[1] || '0'), parseFloat(valueArr[2] || '0')]);\n          break;\n        case 'skewX':\n          var sx = Math.tan(parseFloat(valueArr[0]) * DEGREE_TO_ANGLE);\n          matrix.mul(mt, [1, 0, sx, 1, 0, 0], mt);\n          break;\n        case 'skewY':\n          var sy = Math.tan(parseFloat(valueArr[0]) * DEGREE_TO_ANGLE);\n          matrix.mul(mt, [1, sy, 0, 1, 0, 0], mt);\n          break;\n        case 'matrix':\n          mt[0] = parseFloat(valueArr[0]);\n          mt[1] = parseFloat(valueArr[1]);\n          mt[2] = parseFloat(valueArr[2]);\n          mt[3] = parseFloat(valueArr[3]);\n          mt[4] = parseFloat(valueArr[4]);\n          mt[5] = parseFloat(valueArr[5]);\n          break;\n      }\n    }\n    node.setLocalTransform(mt);\n  }\n}\nvar styleRegex = /([^\\s:;]+)\\s*:\\s*([^:;]+)/g;\nfunction parseInlineStyle(xmlNode, inheritableStyleResult, selfStyleResult) {\n  var style = xmlNode.getAttribute('style');\n  if (!style) {\n    return;\n  }\n  styleRegex.lastIndex = 0;\n  var styleRegResult;\n  while ((styleRegResult = styleRegex.exec(style)) != null) {\n    var svgStlAttr = styleRegResult[1];\n    var zrInheritableStlAttr = hasOwn(INHERITABLE_STYLE_ATTRIBUTES_MAP, svgStlAttr) ? INHERITABLE_STYLE_ATTRIBUTES_MAP[svgStlAttr] : null;\n    if (zrInheritableStlAttr) {\n      inheritableStyleResult[zrInheritableStlAttr] = styleRegResult[2];\n    }\n    var zrSelfStlAttr = hasOwn(SELF_STYLE_ATTRIBUTES_MAP, svgStlAttr) ? SELF_STYLE_ATTRIBUTES_MAP[svgStlAttr] : null;\n    if (zrSelfStlAttr) {\n      selfStyleResult[zrSelfStlAttr] = styleRegResult[2];\n    }\n  }\n}\nfunction parseAttributeStyle(xmlNode, inheritableStyleResult, selfStyleResult) {\n  for (var i = 0; i < INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS.length; i++) {\n    var svgAttrName = INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS[i];\n    var attrValue = xmlNode.getAttribute(svgAttrName);\n    if (attrValue != null) {\n      inheritableStyleResult[INHERITABLE_STYLE_ATTRIBUTES_MAP[svgAttrName]] = attrValue;\n    }\n  }\n  for (var i = 0; i < SELF_STYLE_ATTRIBUTES_MAP_KEYS.length; i++) {\n    var svgAttrName = SELF_STYLE_ATTRIBUTES_MAP_KEYS[i];\n    var attrValue = xmlNode.getAttribute(svgAttrName);\n    if (attrValue != null) {\n      selfStyleResult[SELF_STYLE_ATTRIBUTES_MAP[svgAttrName]] = attrValue;\n    }\n  }\n}\nexport function makeViewBoxTransform(viewBoxRect, boundingRect) {\n  var scaleX = boundingRect.width / viewBoxRect.width;\n  var scaleY = boundingRect.height / viewBoxRect.height;\n  var scale = Math.min(scaleX, scaleY);\n  return {\n    scale: scale,\n    x: -(viewBoxRect.x + viewBoxRect.width / 2) * scale + (boundingRect.x + boundingRect.width / 2),\n    y: -(viewBoxRect.y + viewBoxRect.height / 2) * scale + (boundingRect.y + boundingRect.height / 2)\n  };\n}\nexport function parseSVG(xml, opt) {\n  var parser = new SVGParser();\n  return parser.parse(xml, opt);\n}\nexport { parseXML };", "map": {"version": 3, "names": ["Group", "ZRImage", "Circle", "Rect", "Ellipse", "Line", "Polygon", "Polyline", "matrix", "createFromString", "defaults", "trim", "each", "map", "keys", "hasOwn", "LinearGradient", "RadialGrad<PERSON>", "TSpan", "parseXML", "nodeParsers", "INHERITABLE_STYLE_ATTRIBUTES_MAP", "INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS", "SELF_STYLE_ATTRIBUTES_MAP", "SELF_STYLE_ATTRIBUTES_MAP_KEYS", "SV<PERSON><PERSON><PERSON>", "_defs", "_root", "prototype", "parse", "xml", "opt", "svg", "process", "env", "NODE_ENV", "Error", "_defsUsePending", "root", "named", "viewBox", "getAttribute", "width", "parseFloat", "height", "isNaN", "parseAttributes", "child", "<PERSON><PERSON><PERSON><PERSON>", "_parseNode", "nextS<PERSON>ling", "applyDefs", "viewBoxRect", "viewBoxTransform", "viewBoxArr", "splitNumberSequence", "length", "x", "y", "makeViewBoxTransform", "ignoreViewBox", "elRoot", "add", "scaleX", "scaleY", "scale", "ignoreRootClip", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shape", "xmlNode", "parentGroup", "named<PERSON><PERSON>", "isInDefs", "isInText", "nodeName", "toLowerCase", "el", "namedFromForSub", "parser_1", "call", "nameAttr", "newNamed", "name", "svgNodeTagLower", "push", "parser", "paintServerParsers", "def", "id", "isGroup", "nodeType", "_parseText", "text", "style", "textContent", "silent", "_textX", "_textY", "inheritStyle", "applyTextAlignment", "textStyle", "fontSize", "font", "fontFamily", "fontStyle", "fontWeight", "join", "rect", "getBoundingRect", "internalField", "g", "setShape", "circle", "cx", "cy", "r", "line", "x1", "y1", "x2", "y2", "ellipse", "rx", "ry", "polygon", "pointsStr", "pointsArr", "parsePoints", "points", "polyline", "image", "img", "setStyle", "dx", "dy", "tspan", "path", "d", "lineargradient", "parseInt", "gradient", "parsePaintServerUnit", "parseGradientColorStops", "radialgradient", "gradientUnits", "global", "stop", "toLocaleLowerCase", "offsetStr", "offset", "indexOf", "styleVals", "parseInlineStyle", "stopColor", "colorStops", "color", "parent", "__inheritedStyle", "pointsString", "list", "i", "defsUsePending", "onlyInlineStyle", "isTextGroup", "disp", "inheritedStyle", "selfStyle", "parseTransformAttribute", "parseAttributeStyle", "fill", "getFillStrokeStyle", "stroke", "propName", "__selfStyle", "lineDash", "str", "visibility", "invisible", "display", "ignore", "parentSelfStyle", "textBaseline", "zrTextBaseline", "parentInheritedStyle", "textAlign", "zrTextAlign", "urlRegex", "method", "urlMatch", "match", "url", "defs", "item", "numberReg", "rawStr", "transformRegex", "DEGREE_TO_ANGLE", "Math", "PI", "node", "transform", "replace", "transformOps_1", "mt", "type", "value", "valueArr", "create", "translate", "rotate", "sx", "tan", "mul", "sy", "setLocalTransform", "styleRegex", "inheritableStyleResult", "selfStyleResult", "lastIndex", "styleRegResult", "exec", "svgStlAttr", "zrInheritableStlAttr", "zrSelfStlAttr", "svgAttrName", "attrValue", "boundingRect", "min", "parseSVG"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/zrender/lib/tool/parseSVG.js"], "sourcesContent": ["import Group from '../graphic/Group.js';\nimport ZRImage from '../graphic/Image.js';\nimport Circle from '../graphic/shape/Circle.js';\nimport Rect from '../graphic/shape/Rect.js';\nimport Ellipse from '../graphic/shape/Ellipse.js';\nimport Line from '../graphic/shape/Line.js';\nimport Polygon from '../graphic/shape/Polygon.js';\nimport Polyline from '../graphic/shape/Polyline.js';\nimport * as matrix from '../core/matrix.js';\nimport { createFromString } from './path.js';\nimport { defaults, trim, each, map, keys, hasOwn } from '../core/util.js';\nimport LinearGradient from '../graphic/LinearGradient.js';\nimport RadialGradient from '../graphic/RadialGradient.js';\nimport TSpan from '../graphic/TSpan.js';\nimport { parseXML } from './parseXML.js';\n;\nvar nodeParsers;\nvar INHERITABLE_STYLE_ATTRIBUTES_MAP = {\n    'fill': 'fill',\n    'stroke': 'stroke',\n    'stroke-width': 'lineWidth',\n    'opacity': 'opacity',\n    'fill-opacity': 'fillOpacity',\n    'stroke-opacity': 'strokeOpacity',\n    'stroke-dasharray': 'lineDash',\n    'stroke-dashoffset': 'lineDashOffset',\n    'stroke-linecap': 'lineCap',\n    'stroke-linejoin': 'lineJoin',\n    'stroke-miterlimit': 'miterLimit',\n    'font-family': 'fontFamily',\n    'font-size': 'fontSize',\n    'font-style': 'fontStyle',\n    'font-weight': 'fontWeight',\n    'text-anchor': 'textAlign',\n    'visibility': 'visibility',\n    'display': 'display'\n};\nvar INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS = keys(INHERITABLE_STYLE_ATTRIBUTES_MAP);\nvar SELF_STYLE_ATTRIBUTES_MAP = {\n    'alignment-baseline': 'textBaseline',\n    'stop-color': 'stopColor'\n};\nvar SELF_STYLE_ATTRIBUTES_MAP_KEYS = keys(SELF_STYLE_ATTRIBUTES_MAP);\nvar SVGParser = (function () {\n    function SVGParser() {\n        this._defs = {};\n        this._root = null;\n    }\n    SVGParser.prototype.parse = function (xml, opt) {\n        opt = opt || {};\n        var svg = parseXML(xml);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!svg) {\n                throw new Error('Illegal svg');\n            }\n        }\n        this._defsUsePending = [];\n        var root = new Group();\n        this._root = root;\n        var named = [];\n        var viewBox = svg.getAttribute('viewBox') || '';\n        var width = parseFloat((svg.getAttribute('width') || opt.width));\n        var height = parseFloat((svg.getAttribute('height') || opt.height));\n        isNaN(width) && (width = null);\n        isNaN(height) && (height = null);\n        parseAttributes(svg, root, null, true, false);\n        var child = svg.firstChild;\n        while (child) {\n            this._parseNode(child, root, named, null, false, false);\n            child = child.nextSibling;\n        }\n        applyDefs(this._defs, this._defsUsePending);\n        this._defsUsePending = [];\n        var viewBoxRect;\n        var viewBoxTransform;\n        if (viewBox) {\n            var viewBoxArr = splitNumberSequence(viewBox);\n            if (viewBoxArr.length >= 4) {\n                viewBoxRect = {\n                    x: parseFloat((viewBoxArr[0] || 0)),\n                    y: parseFloat((viewBoxArr[1] || 0)),\n                    width: parseFloat(viewBoxArr[2]),\n                    height: parseFloat(viewBoxArr[3])\n                };\n            }\n        }\n        if (viewBoxRect && width != null && height != null) {\n            viewBoxTransform = makeViewBoxTransform(viewBoxRect, { x: 0, y: 0, width: width, height: height });\n            if (!opt.ignoreViewBox) {\n                var elRoot = root;\n                root = new Group();\n                root.add(elRoot);\n                elRoot.scaleX = elRoot.scaleY = viewBoxTransform.scale;\n                elRoot.x = viewBoxTransform.x;\n                elRoot.y = viewBoxTransform.y;\n            }\n        }\n        if (!opt.ignoreRootClip && width != null && height != null) {\n            root.setClipPath(new Rect({\n                shape: { x: 0, y: 0, width: width, height: height }\n            }));\n        }\n        return {\n            root: root,\n            width: width,\n            height: height,\n            viewBoxRect: viewBoxRect,\n            viewBoxTransform: viewBoxTransform,\n            named: named\n        };\n    };\n    SVGParser.prototype._parseNode = function (xmlNode, parentGroup, named, namedFrom, isInDefs, isInText) {\n        var nodeName = xmlNode.nodeName.toLowerCase();\n        var el;\n        var namedFromForSub = namedFrom;\n        if (nodeName === 'defs') {\n            isInDefs = true;\n        }\n        if (nodeName === 'text') {\n            isInText = true;\n        }\n        if (nodeName === 'defs' || nodeName === 'switch') {\n            el = parentGroup;\n        }\n        else {\n            if (!isInDefs) {\n                var parser_1 = nodeParsers[nodeName];\n                if (parser_1 && hasOwn(nodeParsers, nodeName)) {\n                    el = parser_1.call(this, xmlNode, parentGroup);\n                    var nameAttr = xmlNode.getAttribute('name');\n                    if (nameAttr) {\n                        var newNamed = {\n                            name: nameAttr,\n                            namedFrom: null,\n                            svgNodeTagLower: nodeName,\n                            el: el\n                        };\n                        named.push(newNamed);\n                        if (nodeName === 'g') {\n                            namedFromForSub = newNamed;\n                        }\n                    }\n                    else if (namedFrom) {\n                        named.push({\n                            name: namedFrom.name,\n                            namedFrom: namedFrom,\n                            svgNodeTagLower: nodeName,\n                            el: el\n                        });\n                    }\n                    parentGroup.add(el);\n                }\n            }\n            var parser = paintServerParsers[nodeName];\n            if (parser && hasOwn(paintServerParsers, nodeName)) {\n                var def = parser.call(this, xmlNode);\n                var id = xmlNode.getAttribute('id');\n                if (id) {\n                    this._defs[id] = def;\n                }\n            }\n        }\n        if (el && el.isGroup) {\n            var child = xmlNode.firstChild;\n            while (child) {\n                if (child.nodeType === 1) {\n                    this._parseNode(child, el, named, namedFromForSub, isInDefs, isInText);\n                }\n                else if (child.nodeType === 3 && isInText) {\n                    this._parseText(child, el);\n                }\n                child = child.nextSibling;\n            }\n        }\n    };\n    SVGParser.prototype._parseText = function (xmlNode, parentGroup) {\n        var text = new TSpan({\n            style: {\n                text: xmlNode.textContent\n            },\n            silent: true,\n            x: this._textX || 0,\n            y: this._textY || 0\n        });\n        inheritStyle(parentGroup, text);\n        parseAttributes(xmlNode, text, this._defsUsePending, false, false);\n        applyTextAlignment(text, parentGroup);\n        var textStyle = text.style;\n        var fontSize = textStyle.fontSize;\n        if (fontSize && fontSize < 9) {\n            textStyle.fontSize = 9;\n            text.scaleX *= fontSize / 9;\n            text.scaleY *= fontSize / 9;\n        }\n        var font = (textStyle.fontSize || textStyle.fontFamily) && [\n            textStyle.fontStyle,\n            textStyle.fontWeight,\n            (textStyle.fontSize || 12) + 'px',\n            textStyle.fontFamily || 'sans-serif'\n        ].join(' ');\n        textStyle.font = font;\n        var rect = text.getBoundingRect();\n        this._textX += rect.width;\n        parentGroup.add(text);\n        return text;\n    };\n    SVGParser.internalField = (function () {\n        nodeParsers = {\n            'g': function (xmlNode, parentGroup) {\n                var g = new Group();\n                inheritStyle(parentGroup, g);\n                parseAttributes(xmlNode, g, this._defsUsePending, false, false);\n                return g;\n            },\n            'rect': function (xmlNode, parentGroup) {\n                var rect = new Rect();\n                inheritStyle(parentGroup, rect);\n                parseAttributes(xmlNode, rect, this._defsUsePending, false, false);\n                rect.setShape({\n                    x: parseFloat(xmlNode.getAttribute('x') || '0'),\n                    y: parseFloat(xmlNode.getAttribute('y') || '0'),\n                    width: parseFloat(xmlNode.getAttribute('width') || '0'),\n                    height: parseFloat(xmlNode.getAttribute('height') || '0')\n                });\n                rect.silent = true;\n                return rect;\n            },\n            'circle': function (xmlNode, parentGroup) {\n                var circle = new Circle();\n                inheritStyle(parentGroup, circle);\n                parseAttributes(xmlNode, circle, this._defsUsePending, false, false);\n                circle.setShape({\n                    cx: parseFloat(xmlNode.getAttribute('cx') || '0'),\n                    cy: parseFloat(xmlNode.getAttribute('cy') || '0'),\n                    r: parseFloat(xmlNode.getAttribute('r') || '0')\n                });\n                circle.silent = true;\n                return circle;\n            },\n            'line': function (xmlNode, parentGroup) {\n                var line = new Line();\n                inheritStyle(parentGroup, line);\n                parseAttributes(xmlNode, line, this._defsUsePending, false, false);\n                line.setShape({\n                    x1: parseFloat(xmlNode.getAttribute('x1') || '0'),\n                    y1: parseFloat(xmlNode.getAttribute('y1') || '0'),\n                    x2: parseFloat(xmlNode.getAttribute('x2') || '0'),\n                    y2: parseFloat(xmlNode.getAttribute('y2') || '0')\n                });\n                line.silent = true;\n                return line;\n            },\n            'ellipse': function (xmlNode, parentGroup) {\n                var ellipse = new Ellipse();\n                inheritStyle(parentGroup, ellipse);\n                parseAttributes(xmlNode, ellipse, this._defsUsePending, false, false);\n                ellipse.setShape({\n                    cx: parseFloat(xmlNode.getAttribute('cx') || '0'),\n                    cy: parseFloat(xmlNode.getAttribute('cy') || '0'),\n                    rx: parseFloat(xmlNode.getAttribute('rx') || '0'),\n                    ry: parseFloat(xmlNode.getAttribute('ry') || '0')\n                });\n                ellipse.silent = true;\n                return ellipse;\n            },\n            'polygon': function (xmlNode, parentGroup) {\n                var pointsStr = xmlNode.getAttribute('points');\n                var pointsArr;\n                if (pointsStr) {\n                    pointsArr = parsePoints(pointsStr);\n                }\n                var polygon = new Polygon({\n                    shape: {\n                        points: pointsArr || []\n                    },\n                    silent: true\n                });\n                inheritStyle(parentGroup, polygon);\n                parseAttributes(xmlNode, polygon, this._defsUsePending, false, false);\n                return polygon;\n            },\n            'polyline': function (xmlNode, parentGroup) {\n                var pointsStr = xmlNode.getAttribute('points');\n                var pointsArr;\n                if (pointsStr) {\n                    pointsArr = parsePoints(pointsStr);\n                }\n                var polyline = new Polyline({\n                    shape: {\n                        points: pointsArr || []\n                    },\n                    silent: true\n                });\n                inheritStyle(parentGroup, polyline);\n                parseAttributes(xmlNode, polyline, this._defsUsePending, false, false);\n                return polyline;\n            },\n            'image': function (xmlNode, parentGroup) {\n                var img = new ZRImage();\n                inheritStyle(parentGroup, img);\n                parseAttributes(xmlNode, img, this._defsUsePending, false, false);\n                img.setStyle({\n                    image: xmlNode.getAttribute('xlink:href') || xmlNode.getAttribute('href'),\n                    x: +xmlNode.getAttribute('x'),\n                    y: +xmlNode.getAttribute('y'),\n                    width: +xmlNode.getAttribute('width'),\n                    height: +xmlNode.getAttribute('height')\n                });\n                img.silent = true;\n                return img;\n            },\n            'text': function (xmlNode, parentGroup) {\n                var x = xmlNode.getAttribute('x') || '0';\n                var y = xmlNode.getAttribute('y') || '0';\n                var dx = xmlNode.getAttribute('dx') || '0';\n                var dy = xmlNode.getAttribute('dy') || '0';\n                this._textX = parseFloat(x) + parseFloat(dx);\n                this._textY = parseFloat(y) + parseFloat(dy);\n                var g = new Group();\n                inheritStyle(parentGroup, g);\n                parseAttributes(xmlNode, g, this._defsUsePending, false, true);\n                return g;\n            },\n            'tspan': function (xmlNode, parentGroup) {\n                var x = xmlNode.getAttribute('x');\n                var y = xmlNode.getAttribute('y');\n                if (x != null) {\n                    this._textX = parseFloat(x);\n                }\n                if (y != null) {\n                    this._textY = parseFloat(y);\n                }\n                var dx = xmlNode.getAttribute('dx') || '0';\n                var dy = xmlNode.getAttribute('dy') || '0';\n                var g = new Group();\n                inheritStyle(parentGroup, g);\n                parseAttributes(xmlNode, g, this._defsUsePending, false, true);\n                this._textX += parseFloat(dx);\n                this._textY += parseFloat(dy);\n                return g;\n            },\n            'path': function (xmlNode, parentGroup) {\n                var d = xmlNode.getAttribute('d') || '';\n                var path = createFromString(d);\n                inheritStyle(parentGroup, path);\n                parseAttributes(xmlNode, path, this._defsUsePending, false, false);\n                path.silent = true;\n                return path;\n            }\n        };\n    })();\n    return SVGParser;\n}());\nvar paintServerParsers = {\n    'lineargradient': function (xmlNode) {\n        var x1 = parseInt(xmlNode.getAttribute('x1') || '0', 10);\n        var y1 = parseInt(xmlNode.getAttribute('y1') || '0', 10);\n        var x2 = parseInt(xmlNode.getAttribute('x2') || '10', 10);\n        var y2 = parseInt(xmlNode.getAttribute('y2') || '0', 10);\n        var gradient = new LinearGradient(x1, y1, x2, y2);\n        parsePaintServerUnit(xmlNode, gradient);\n        parseGradientColorStops(xmlNode, gradient);\n        return gradient;\n    },\n    'radialgradient': function (xmlNode) {\n        var cx = parseInt(xmlNode.getAttribute('cx') || '0', 10);\n        var cy = parseInt(xmlNode.getAttribute('cy') || '0', 10);\n        var r = parseInt(xmlNode.getAttribute('r') || '0', 10);\n        var gradient = new RadialGradient(cx, cy, r);\n        parsePaintServerUnit(xmlNode, gradient);\n        parseGradientColorStops(xmlNode, gradient);\n        return gradient;\n    }\n};\nfunction parsePaintServerUnit(xmlNode, gradient) {\n    var gradientUnits = xmlNode.getAttribute('gradientUnits');\n    if (gradientUnits === 'userSpaceOnUse') {\n        gradient.global = true;\n    }\n}\nfunction parseGradientColorStops(xmlNode, gradient) {\n    var stop = xmlNode.firstChild;\n    while (stop) {\n        if (stop.nodeType === 1\n            && stop.nodeName.toLocaleLowerCase() === 'stop') {\n            var offsetStr = stop.getAttribute('offset');\n            var offset = void 0;\n            if (offsetStr && offsetStr.indexOf('%') > 0) {\n                offset = parseInt(offsetStr, 10) / 100;\n            }\n            else if (offsetStr) {\n                offset = parseFloat(offsetStr);\n            }\n            else {\n                offset = 0;\n            }\n            var styleVals = {};\n            parseInlineStyle(stop, styleVals, styleVals);\n            var stopColor = styleVals.stopColor\n                || stop.getAttribute('stop-color')\n                || '#000000';\n            gradient.colorStops.push({\n                offset: offset,\n                color: stopColor\n            });\n        }\n        stop = stop.nextSibling;\n    }\n}\nfunction inheritStyle(parent, child) {\n    if (parent && parent.__inheritedStyle) {\n        if (!child.__inheritedStyle) {\n            child.__inheritedStyle = {};\n        }\n        defaults(child.__inheritedStyle, parent.__inheritedStyle);\n    }\n}\nfunction parsePoints(pointsString) {\n    var list = splitNumberSequence(pointsString);\n    var points = [];\n    for (var i = 0; i < list.length; i += 2) {\n        var x = parseFloat(list[i]);\n        var y = parseFloat(list[i + 1]);\n        points.push([x, y]);\n    }\n    return points;\n}\nfunction parseAttributes(xmlNode, el, defsUsePending, onlyInlineStyle, isTextGroup) {\n    var disp = el;\n    var inheritedStyle = disp.__inheritedStyle = disp.__inheritedStyle || {};\n    var selfStyle = {};\n    if (xmlNode.nodeType === 1) {\n        parseTransformAttribute(xmlNode, el);\n        parseInlineStyle(xmlNode, inheritedStyle, selfStyle);\n        if (!onlyInlineStyle) {\n            parseAttributeStyle(xmlNode, inheritedStyle, selfStyle);\n        }\n    }\n    disp.style = disp.style || {};\n    if (inheritedStyle.fill != null) {\n        disp.style.fill = getFillStrokeStyle(disp, 'fill', inheritedStyle.fill, defsUsePending);\n    }\n    if (inheritedStyle.stroke != null) {\n        disp.style.stroke = getFillStrokeStyle(disp, 'stroke', inheritedStyle.stroke, defsUsePending);\n    }\n    each([\n        'lineWidth', 'opacity', 'fillOpacity', 'strokeOpacity', 'miterLimit', 'fontSize'\n    ], function (propName) {\n        if (inheritedStyle[propName] != null) {\n            disp.style[propName] = parseFloat(inheritedStyle[propName]);\n        }\n    });\n    each([\n        'lineDashOffset', 'lineCap', 'lineJoin', 'fontWeight', 'fontFamily', 'fontStyle', 'textAlign'\n    ], function (propName) {\n        if (inheritedStyle[propName] != null) {\n            disp.style[propName] = inheritedStyle[propName];\n        }\n    });\n    if (isTextGroup) {\n        disp.__selfStyle = selfStyle;\n    }\n    if (inheritedStyle.lineDash) {\n        disp.style.lineDash = map(splitNumberSequence(inheritedStyle.lineDash), function (str) {\n            return parseFloat(str);\n        });\n    }\n    if (inheritedStyle.visibility === 'hidden' || inheritedStyle.visibility === 'collapse') {\n        disp.invisible = true;\n    }\n    if (inheritedStyle.display === 'none') {\n        disp.ignore = true;\n    }\n}\nfunction applyTextAlignment(text, parentGroup) {\n    var parentSelfStyle = parentGroup.__selfStyle;\n    if (parentSelfStyle) {\n        var textBaseline = parentSelfStyle.textBaseline;\n        var zrTextBaseline = textBaseline;\n        if (!textBaseline || textBaseline === 'auto') {\n            zrTextBaseline = 'alphabetic';\n        }\n        else if (textBaseline === 'baseline') {\n            zrTextBaseline = 'alphabetic';\n        }\n        else if (textBaseline === 'before-edge' || textBaseline === 'text-before-edge') {\n            zrTextBaseline = 'top';\n        }\n        else if (textBaseline === 'after-edge' || textBaseline === 'text-after-edge') {\n            zrTextBaseline = 'bottom';\n        }\n        else if (textBaseline === 'central' || textBaseline === 'mathematical') {\n            zrTextBaseline = 'middle';\n        }\n        text.style.textBaseline = zrTextBaseline;\n    }\n    var parentInheritedStyle = parentGroup.__inheritedStyle;\n    if (parentInheritedStyle) {\n        var textAlign = parentInheritedStyle.textAlign;\n        var zrTextAlign = textAlign;\n        if (textAlign) {\n            if (textAlign === 'middle') {\n                zrTextAlign = 'center';\n            }\n            text.style.textAlign = zrTextAlign;\n        }\n    }\n}\nvar urlRegex = /^url\\(\\s*#(.*?)\\)/;\nfunction getFillStrokeStyle(el, method, str, defsUsePending) {\n    var urlMatch = str && str.match(urlRegex);\n    if (urlMatch) {\n        var url = trim(urlMatch[1]);\n        defsUsePending.push([el, method, url]);\n        return;\n    }\n    if (str === 'none') {\n        str = null;\n    }\n    return str;\n}\nfunction applyDefs(defs, defsUsePending) {\n    for (var i = 0; i < defsUsePending.length; i++) {\n        var item = defsUsePending[i];\n        item[0].style[item[1]] = defs[item[2]];\n    }\n}\nvar numberReg = /-?([0-9]*\\.)?[0-9]+([eE]-?[0-9]+)?/g;\nfunction splitNumberSequence(rawStr) {\n    return rawStr.match(numberReg) || [];\n}\nvar transformRegex = /(translate|scale|rotate|skewX|skewY|matrix)\\(([\\-\\s0-9\\.eE,]*)\\)/g;\nvar DEGREE_TO_ANGLE = Math.PI / 180;\nfunction parseTransformAttribute(xmlNode, node) {\n    var transform = xmlNode.getAttribute('transform');\n    if (transform) {\n        transform = transform.replace(/,/g, ' ');\n        var transformOps_1 = [];\n        var mt = null;\n        transform.replace(transformRegex, function (str, type, value) {\n            transformOps_1.push(type, value);\n            return '';\n        });\n        for (var i = transformOps_1.length - 1; i > 0; i -= 2) {\n            var value = transformOps_1[i];\n            var type = transformOps_1[i - 1];\n            var valueArr = splitNumberSequence(value);\n            mt = mt || matrix.create();\n            switch (type) {\n                case 'translate':\n                    matrix.translate(mt, mt, [parseFloat(valueArr[0]), parseFloat(valueArr[1] || '0')]);\n                    break;\n                case 'scale':\n                    matrix.scale(mt, mt, [parseFloat(valueArr[0]), parseFloat(valueArr[1] || valueArr[0])]);\n                    break;\n                case 'rotate':\n                    matrix.rotate(mt, mt, -parseFloat(valueArr[0]) * DEGREE_TO_ANGLE, [\n                        parseFloat(valueArr[1] || '0'),\n                        parseFloat(valueArr[2] || '0')\n                    ]);\n                    break;\n                case 'skewX':\n                    var sx = Math.tan(parseFloat(valueArr[0]) * DEGREE_TO_ANGLE);\n                    matrix.mul(mt, [1, 0, sx, 1, 0, 0], mt);\n                    break;\n                case 'skewY':\n                    var sy = Math.tan(parseFloat(valueArr[0]) * DEGREE_TO_ANGLE);\n                    matrix.mul(mt, [1, sy, 0, 1, 0, 0], mt);\n                    break;\n                case 'matrix':\n                    mt[0] = parseFloat(valueArr[0]);\n                    mt[1] = parseFloat(valueArr[1]);\n                    mt[2] = parseFloat(valueArr[2]);\n                    mt[3] = parseFloat(valueArr[3]);\n                    mt[4] = parseFloat(valueArr[4]);\n                    mt[5] = parseFloat(valueArr[5]);\n                    break;\n            }\n        }\n        node.setLocalTransform(mt);\n    }\n}\nvar styleRegex = /([^\\s:;]+)\\s*:\\s*([^:;]+)/g;\nfunction parseInlineStyle(xmlNode, inheritableStyleResult, selfStyleResult) {\n    var style = xmlNode.getAttribute('style');\n    if (!style) {\n        return;\n    }\n    styleRegex.lastIndex = 0;\n    var styleRegResult;\n    while ((styleRegResult = styleRegex.exec(style)) != null) {\n        var svgStlAttr = styleRegResult[1];\n        var zrInheritableStlAttr = hasOwn(INHERITABLE_STYLE_ATTRIBUTES_MAP, svgStlAttr)\n            ? INHERITABLE_STYLE_ATTRIBUTES_MAP[svgStlAttr]\n            : null;\n        if (zrInheritableStlAttr) {\n            inheritableStyleResult[zrInheritableStlAttr] = styleRegResult[2];\n        }\n        var zrSelfStlAttr = hasOwn(SELF_STYLE_ATTRIBUTES_MAP, svgStlAttr)\n            ? SELF_STYLE_ATTRIBUTES_MAP[svgStlAttr]\n            : null;\n        if (zrSelfStlAttr) {\n            selfStyleResult[zrSelfStlAttr] = styleRegResult[2];\n        }\n    }\n}\nfunction parseAttributeStyle(xmlNode, inheritableStyleResult, selfStyleResult) {\n    for (var i = 0; i < INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS.length; i++) {\n        var svgAttrName = INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS[i];\n        var attrValue = xmlNode.getAttribute(svgAttrName);\n        if (attrValue != null) {\n            inheritableStyleResult[INHERITABLE_STYLE_ATTRIBUTES_MAP[svgAttrName]] = attrValue;\n        }\n    }\n    for (var i = 0; i < SELF_STYLE_ATTRIBUTES_MAP_KEYS.length; i++) {\n        var svgAttrName = SELF_STYLE_ATTRIBUTES_MAP_KEYS[i];\n        var attrValue = xmlNode.getAttribute(svgAttrName);\n        if (attrValue != null) {\n            selfStyleResult[SELF_STYLE_ATTRIBUTES_MAP[svgAttrName]] = attrValue;\n        }\n    }\n}\nexport function makeViewBoxTransform(viewBoxRect, boundingRect) {\n    var scaleX = boundingRect.width / viewBoxRect.width;\n    var scaleY = boundingRect.height / viewBoxRect.height;\n    var scale = Math.min(scaleX, scaleY);\n    return {\n        scale: scale,\n        x: -(viewBoxRect.x + viewBoxRect.width / 2) * scale + (boundingRect.x + boundingRect.width / 2),\n        y: -(viewBoxRect.y + viewBoxRect.height / 2) * scale + (boundingRect.y + boundingRect.height / 2)\n    };\n}\nexport function parseSVG(xml, opt) {\n    var parser = new SVGParser();\n    return parser.parse(xml, opt);\n}\nexport { parseXML };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,qBAAqB;AACvC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,OAAO,MAAM,6BAA6B;AACjD,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAO,KAAKC,MAAM,MAAM,mBAAmB;AAC3C,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,SAASC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,iBAAiB;AACzE,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,KAAK,MAAM,qBAAqB;AACvC,SAASC,QAAQ,QAAQ,eAAe;AACxC;AACA,IAAIC,WAAW;AACf,IAAIC,gCAAgC,GAAG;EACnC,MAAM,EAAE,MAAM;EACd,QAAQ,EAAE,QAAQ;EAClB,cAAc,EAAE,WAAW;EAC3B,SAAS,EAAE,SAAS;EACpB,cAAc,EAAE,aAAa;EAC7B,gBAAgB,EAAE,eAAe;EACjC,kBAAkB,EAAE,UAAU;EAC9B,mBAAmB,EAAE,gBAAgB;EACrC,gBAAgB,EAAE,SAAS;EAC3B,iBAAiB,EAAE,UAAU;EAC7B,mBAAmB,EAAE,YAAY;EACjC,aAAa,EAAE,YAAY;EAC3B,WAAW,EAAE,UAAU;EACvB,YAAY,EAAE,WAAW;EACzB,aAAa,EAAE,YAAY;EAC3B,aAAa,EAAE,WAAW;EAC1B,YAAY,EAAE,YAAY;EAC1B,SAAS,EAAE;AACf,CAAC;AACD,IAAIC,qCAAqC,GAAGR,IAAI,CAACO,gCAAgC,CAAC;AAClF,IAAIE,yBAAyB,GAAG;EAC5B,oBAAoB,EAAE,cAAc;EACpC,YAAY,EAAE;AAClB,CAAC;AACD,IAAIC,8BAA8B,GAAGV,IAAI,CAACS,yBAAyB,CAAC;AACpE,IAAIE,SAAS,GAAI,YAAY;EACzB,SAASA,SAASA,CAAA,EAAG;IACjB,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,KAAK,GAAG,IAAI;EACrB;EACAF,SAAS,CAACG,SAAS,CAACC,KAAK,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAE;IAC5CA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;IACf,IAAIC,GAAG,GAAGb,QAAQ,CAACW,GAAG,CAAC;IACvB,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC,IAAI,CAACH,GAAG,EAAE;QACN,MAAM,IAAII,KAAK,CAAC,aAAa,CAAC;MAClC;IACJ;IACA,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAIC,IAAI,GAAG,IAAItC,KAAK,CAAC,CAAC;IACtB,IAAI,CAAC2B,KAAK,GAAGW,IAAI;IACjB,IAAIC,KAAK,GAAG,EAAE;IACd,IAAIC,OAAO,GAAGR,GAAG,CAACS,YAAY,CAAC,SAAS,CAAC,IAAI,EAAE;IAC/C,IAAIC,KAAK,GAAGC,UAAU,CAAEX,GAAG,CAACS,YAAY,CAAC,OAAO,CAAC,IAAIV,GAAG,CAACW,KAAM,CAAC;IAChE,IAAIE,MAAM,GAAGD,UAAU,CAAEX,GAAG,CAACS,YAAY,CAAC,QAAQ,CAAC,IAAIV,GAAG,CAACa,MAAO,CAAC;IACnEC,KAAK,CAACH,KAAK,CAAC,KAAKA,KAAK,GAAG,IAAI,CAAC;IAC9BG,KAAK,CAACD,MAAM,CAAC,KAAKA,MAAM,GAAG,IAAI,CAAC;IAChCE,eAAe,CAACd,GAAG,EAAEM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;IAC7C,IAAIS,KAAK,GAAGf,GAAG,CAACgB,UAAU;IAC1B,OAAOD,KAAK,EAAE;MACV,IAAI,CAACE,UAAU,CAACF,KAAK,EAAET,IAAI,EAAEC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;MACvDQ,KAAK,GAAGA,KAAK,CAACG,WAAW;IAC7B;IACAC,SAAS,CAAC,IAAI,CAACzB,KAAK,EAAE,IAAI,CAACW,eAAe,CAAC;IAC3C,IAAI,CAACA,eAAe,GAAG,EAAE;IACzB,IAAIe,WAAW;IACf,IAAIC,gBAAgB;IACpB,IAAIb,OAAO,EAAE;MACT,IAAIc,UAAU,GAAGC,mBAAmB,CAACf,OAAO,CAAC;MAC7C,IAAIc,UAAU,CAACE,MAAM,IAAI,CAAC,EAAE;QACxBJ,WAAW,GAAG;UACVK,CAAC,EAAEd,UAAU,CAAEW,UAAU,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC;UACnCI,CAAC,EAAEf,UAAU,CAAEW,UAAU,CAAC,CAAC,CAAC,IAAI,CAAE,CAAC;UACnCZ,KAAK,EAAEC,UAAU,CAACW,UAAU,CAAC,CAAC,CAAC,CAAC;UAChCV,MAAM,EAAED,UAAU,CAACW,UAAU,CAAC,CAAC,CAAC;QACpC,CAAC;MACL;IACJ;IACA,IAAIF,WAAW,IAAIV,KAAK,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAE;MAChDS,gBAAgB,GAAGM,oBAAoB,CAACP,WAAW,EAAE;QAAEK,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAEhB,KAAK,EAAEA,KAAK;QAAEE,MAAM,EAAEA;MAAO,CAAC,CAAC;MAClG,IAAI,CAACb,GAAG,CAAC6B,aAAa,EAAE;QACpB,IAAIC,MAAM,GAAGvB,IAAI;QACjBA,IAAI,GAAG,IAAItC,KAAK,CAAC,CAAC;QAClBsC,IAAI,CAACwB,GAAG,CAACD,MAAM,CAAC;QAChBA,MAAM,CAACE,MAAM,GAAGF,MAAM,CAACG,MAAM,GAAGX,gBAAgB,CAACY,KAAK;QACtDJ,MAAM,CAACJ,CAAC,GAAGJ,gBAAgB,CAACI,CAAC;QAC7BI,MAAM,CAACH,CAAC,GAAGL,gBAAgB,CAACK,CAAC;MACjC;IACJ;IACA,IAAI,CAAC3B,GAAG,CAACmC,cAAc,IAAIxB,KAAK,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAAE;MACxDN,IAAI,CAAC6B,WAAW,CAAC,IAAIhE,IAAI,CAAC;QACtBiE,KAAK,EAAE;UAAEX,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEhB,KAAK,EAAEA,KAAK;UAAEE,MAAM,EAAEA;QAAO;MACtD,CAAC,CAAC,CAAC;IACP;IACA,OAAO;MACHN,IAAI,EAAEA,IAAI;MACVI,KAAK,EAAEA,KAAK;MACZE,MAAM,EAAEA,MAAM;MACdQ,WAAW,EAAEA,WAAW;MACxBC,gBAAgB,EAAEA,gBAAgB;MAClCd,KAAK,EAAEA;IACX,CAAC;EACL,CAAC;EACDd,SAAS,CAACG,SAAS,CAACqB,UAAU,GAAG,UAAUoB,OAAO,EAAEC,WAAW,EAAE/B,KAAK,EAAEgC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACnG,IAAIC,QAAQ,GAAGL,OAAO,CAACK,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC7C,IAAIC,EAAE;IACN,IAAIC,eAAe,GAAGN,SAAS;IAC/B,IAAIG,QAAQ,KAAK,MAAM,EAAE;MACrBF,QAAQ,GAAG,IAAI;IACnB;IACA,IAAIE,QAAQ,KAAK,MAAM,EAAE;MACrBD,QAAQ,GAAG,IAAI;IACnB;IACA,IAAIC,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MAC9CE,EAAE,GAAGN,WAAW;IACpB,CAAC,MACI;MACD,IAAI,CAACE,QAAQ,EAAE;QACX,IAAIM,QAAQ,GAAG1D,WAAW,CAACsD,QAAQ,CAAC;QACpC,IAAII,QAAQ,IAAI/D,MAAM,CAACK,WAAW,EAAEsD,QAAQ,CAAC,EAAE;UAC3CE,EAAE,GAAGE,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAEV,OAAO,EAAEC,WAAW,CAAC;UAC9C,IAAIU,QAAQ,GAAGX,OAAO,CAAC5B,YAAY,CAAC,MAAM,CAAC;UAC3C,IAAIuC,QAAQ,EAAE;YACV,IAAIC,QAAQ,GAAG;cACXC,IAAI,EAAEF,QAAQ;cACdT,SAAS,EAAE,IAAI;cACfY,eAAe,EAAET,QAAQ;cACzBE,EAAE,EAAEA;YACR,CAAC;YACDrC,KAAK,CAAC6C,IAAI,CAACH,QAAQ,CAAC;YACpB,IAAIP,QAAQ,KAAK,GAAG,EAAE;cAClBG,eAAe,GAAGI,QAAQ;YAC9B;UACJ,CAAC,MACI,IAAIV,SAAS,EAAE;YAChBhC,KAAK,CAAC6C,IAAI,CAAC;cACPF,IAAI,EAAEX,SAAS,CAACW,IAAI;cACpBX,SAAS,EAAEA,SAAS;cACpBY,eAAe,EAAET,QAAQ;cACzBE,EAAE,EAAEA;YACR,CAAC,CAAC;UACN;UACAN,WAAW,CAACR,GAAG,CAACc,EAAE,CAAC;QACvB;MACJ;MACA,IAAIS,MAAM,GAAGC,kBAAkB,CAACZ,QAAQ,CAAC;MACzC,IAAIW,MAAM,IAAItE,MAAM,CAACuE,kBAAkB,EAAEZ,QAAQ,CAAC,EAAE;QAChD,IAAIa,GAAG,GAAGF,MAAM,CAACN,IAAI,CAAC,IAAI,EAAEV,OAAO,CAAC;QACpC,IAAImB,EAAE,GAAGnB,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC;QACnC,IAAI+C,EAAE,EAAE;UACJ,IAAI,CAAC9D,KAAK,CAAC8D,EAAE,CAAC,GAAGD,GAAG;QACxB;MACJ;IACJ;IACA,IAAIX,EAAE,IAAIA,EAAE,CAACa,OAAO,EAAE;MAClB,IAAI1C,KAAK,GAAGsB,OAAO,CAACrB,UAAU;MAC9B,OAAOD,KAAK,EAAE;QACV,IAAIA,KAAK,CAAC2C,QAAQ,KAAK,CAAC,EAAE;UACtB,IAAI,CAACzC,UAAU,CAACF,KAAK,EAAE6B,EAAE,EAAErC,KAAK,EAAEsC,eAAe,EAAEL,QAAQ,EAAEC,QAAQ,CAAC;QAC1E,CAAC,MACI,IAAI1B,KAAK,CAAC2C,QAAQ,KAAK,CAAC,IAAIjB,QAAQ,EAAE;UACvC,IAAI,CAACkB,UAAU,CAAC5C,KAAK,EAAE6B,EAAE,CAAC;QAC9B;QACA7B,KAAK,GAAGA,KAAK,CAACG,WAAW;MAC7B;IACJ;EACJ,CAAC;EACDzB,SAAS,CAACG,SAAS,CAAC+D,UAAU,GAAG,UAAUtB,OAAO,EAAEC,WAAW,EAAE;IAC7D,IAAIsB,IAAI,GAAG,IAAI1E,KAAK,CAAC;MACjB2E,KAAK,EAAE;QACHD,IAAI,EAAEvB,OAAO,CAACyB;MAClB,CAAC;MACDC,MAAM,EAAE,IAAI;MACZtC,CAAC,EAAE,IAAI,CAACuC,MAAM,IAAI,CAAC;MACnBtC,CAAC,EAAE,IAAI,CAACuC,MAAM,IAAI;IACtB,CAAC,CAAC;IACFC,YAAY,CAAC5B,WAAW,EAAEsB,IAAI,CAAC;IAC/B9C,eAAe,CAACuB,OAAO,EAAEuB,IAAI,EAAE,IAAI,CAACvD,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;IAClE8D,kBAAkB,CAACP,IAAI,EAAEtB,WAAW,CAAC;IACrC,IAAI8B,SAAS,GAAGR,IAAI,CAACC,KAAK;IAC1B,IAAIQ,QAAQ,GAAGD,SAAS,CAACC,QAAQ;IACjC,IAAIA,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE;MAC1BD,SAAS,CAACC,QAAQ,GAAG,CAAC;MACtBT,IAAI,CAAC7B,MAAM,IAAIsC,QAAQ,GAAG,CAAC;MAC3BT,IAAI,CAAC5B,MAAM,IAAIqC,QAAQ,GAAG,CAAC;IAC/B;IACA,IAAIC,IAAI,GAAG,CAACF,SAAS,CAACC,QAAQ,IAAID,SAAS,CAACG,UAAU,KAAK,CACvDH,SAAS,CAACI,SAAS,EACnBJ,SAAS,CAACK,UAAU,EACpB,CAACL,SAAS,CAACC,QAAQ,IAAI,EAAE,IAAI,IAAI,EACjCD,SAAS,CAACG,UAAU,IAAI,YAAY,CACvC,CAACG,IAAI,CAAC,GAAG,CAAC;IACXN,SAAS,CAACE,IAAI,GAAGA,IAAI;IACrB,IAAIK,IAAI,GAAGf,IAAI,CAACgB,eAAe,CAAC,CAAC;IACjC,IAAI,CAACZ,MAAM,IAAIW,IAAI,CAACjE,KAAK;IACzB4B,WAAW,CAACR,GAAG,CAAC8B,IAAI,CAAC;IACrB,OAAOA,IAAI;EACf,CAAC;EACDnE,SAAS,CAACoF,aAAa,GAAI,YAAY;IACnCzF,WAAW,GAAG;MACV,GAAG,EAAE,SAAA0F,CAAUzC,OAAO,EAAEC,WAAW,EAAE;QACjC,IAAIwC,CAAC,GAAG,IAAI9G,KAAK,CAAC,CAAC;QACnBkG,YAAY,CAAC5B,WAAW,EAAEwC,CAAC,CAAC;QAC5BhE,eAAe,CAACuB,OAAO,EAAEyC,CAAC,EAAE,IAAI,CAACzE,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QAC/D,OAAOyE,CAAC;MACZ,CAAC;MACD,MAAM,EAAE,SAAAH,CAAUtC,OAAO,EAAEC,WAAW,EAAE;QACpC,IAAIqC,IAAI,GAAG,IAAIxG,IAAI,CAAC,CAAC;QACrB+F,YAAY,CAAC5B,WAAW,EAAEqC,IAAI,CAAC;QAC/B7D,eAAe,CAACuB,OAAO,EAAEsC,IAAI,EAAE,IAAI,CAACtE,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QAClEsE,IAAI,CAACI,QAAQ,CAAC;UACVtD,CAAC,EAAEd,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;UAC/CiB,CAAC,EAAEf,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;UAC/CC,KAAK,EAAEC,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;UACvDG,MAAM,EAAED,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,QAAQ,CAAC,IAAI,GAAG;QAC5D,CAAC,CAAC;QACFkE,IAAI,CAACZ,MAAM,GAAG,IAAI;QAClB,OAAOY,IAAI;MACf,CAAC;MACD,QAAQ,EAAE,SAAAK,CAAU3C,OAAO,EAAEC,WAAW,EAAE;QACtC,IAAI0C,MAAM,GAAG,IAAI9G,MAAM,CAAC,CAAC;QACzBgG,YAAY,CAAC5B,WAAW,EAAE0C,MAAM,CAAC;QACjClE,eAAe,CAACuB,OAAO,EAAE2C,MAAM,EAAE,IAAI,CAAC3E,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QACpE2E,MAAM,CAACD,QAAQ,CAAC;UACZE,EAAE,EAAEtE,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjDyE,EAAE,EAAEvE,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjD0E,CAAC,EAAExE,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG;QAClD,CAAC,CAAC;QACFuE,MAAM,CAACjB,MAAM,GAAG,IAAI;QACpB,OAAOiB,MAAM;MACjB,CAAC;MACD,MAAM,EAAE,SAAAI,CAAU/C,OAAO,EAAEC,WAAW,EAAE;QACpC,IAAI8C,IAAI,GAAG,IAAI/G,IAAI,CAAC,CAAC;QACrB6F,YAAY,CAAC5B,WAAW,EAAE8C,IAAI,CAAC;QAC/BtE,eAAe,CAACuB,OAAO,EAAE+C,IAAI,EAAE,IAAI,CAAC/E,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QAClE+E,IAAI,CAACL,QAAQ,CAAC;UACVM,EAAE,EAAE1E,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjD6E,EAAE,EAAE3E,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjD8E,EAAE,EAAE5E,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjD+E,EAAE,EAAE7E,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG;QACpD,CAAC,CAAC;QACF2E,IAAI,CAACrB,MAAM,GAAG,IAAI;QAClB,OAAOqB,IAAI;MACf,CAAC;MACD,SAAS,EAAE,SAAAK,CAAUpD,OAAO,EAAEC,WAAW,EAAE;QACvC,IAAImD,OAAO,GAAG,IAAIrH,OAAO,CAAC,CAAC;QAC3B8F,YAAY,CAAC5B,WAAW,EAAEmD,OAAO,CAAC;QAClC3E,eAAe,CAACuB,OAAO,EAAEoD,OAAO,EAAE,IAAI,CAACpF,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QACrEoF,OAAO,CAACV,QAAQ,CAAC;UACbE,EAAE,EAAEtE,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjDyE,EAAE,EAAEvE,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjDiF,EAAE,EAAE/E,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;UACjDkF,EAAE,EAAEhF,UAAU,CAAC0B,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG;QACpD,CAAC,CAAC;QACFgF,OAAO,CAAC1B,MAAM,GAAG,IAAI;QACrB,OAAO0B,OAAO;MAClB,CAAC;MACD,SAAS,EAAE,SAAAG,CAAUvD,OAAO,EAAEC,WAAW,EAAE;QACvC,IAAIuD,SAAS,GAAGxD,OAAO,CAAC5B,YAAY,CAAC,QAAQ,CAAC;QAC9C,IAAIqF,SAAS;QACb,IAAID,SAAS,EAAE;UACXC,SAAS,GAAGC,WAAW,CAACF,SAAS,CAAC;QACtC;QACA,IAAID,OAAO,GAAG,IAAItH,OAAO,CAAC;UACtB8D,KAAK,EAAE;YACH4D,MAAM,EAAEF,SAAS,IAAI;UACzB,CAAC;UACD/B,MAAM,EAAE;QACZ,CAAC,CAAC;QACFG,YAAY,CAAC5B,WAAW,EAAEsD,OAAO,CAAC;QAClC9E,eAAe,CAACuB,OAAO,EAAEuD,OAAO,EAAE,IAAI,CAACvF,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QACrE,OAAOuF,OAAO;MAClB,CAAC;MACD,UAAU,EAAE,SAAAK,CAAU5D,OAAO,EAAEC,WAAW,EAAE;QACxC,IAAIuD,SAAS,GAAGxD,OAAO,CAAC5B,YAAY,CAAC,QAAQ,CAAC;QAC9C,IAAIqF,SAAS;QACb,IAAID,SAAS,EAAE;UACXC,SAAS,GAAGC,WAAW,CAACF,SAAS,CAAC;QACtC;QACA,IAAII,QAAQ,GAAG,IAAI1H,QAAQ,CAAC;UACxB6D,KAAK,EAAE;YACH4D,MAAM,EAAEF,SAAS,IAAI;UACzB,CAAC;UACD/B,MAAM,EAAE;QACZ,CAAC,CAAC;QACFG,YAAY,CAAC5B,WAAW,EAAE2D,QAAQ,CAAC;QACnCnF,eAAe,CAACuB,OAAO,EAAE4D,QAAQ,EAAE,IAAI,CAAC5F,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QACtE,OAAO4F,QAAQ;MACnB,CAAC;MACD,OAAO,EAAE,SAAAC,CAAU7D,OAAO,EAAEC,WAAW,EAAE;QACrC,IAAI6D,GAAG,GAAG,IAAIlI,OAAO,CAAC,CAAC;QACvBiG,YAAY,CAAC5B,WAAW,EAAE6D,GAAG,CAAC;QAC9BrF,eAAe,CAACuB,OAAO,EAAE8D,GAAG,EAAE,IAAI,CAAC9F,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QACjE8F,GAAG,CAACC,QAAQ,CAAC;UACTF,KAAK,EAAE7D,OAAO,CAAC5B,YAAY,CAAC,YAAY,CAAC,IAAI4B,OAAO,CAAC5B,YAAY,CAAC,MAAM,CAAC;UACzEgB,CAAC,EAAE,CAACY,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC;UAC7BiB,CAAC,EAAE,CAACW,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC;UAC7BC,KAAK,EAAE,CAAC2B,OAAO,CAAC5B,YAAY,CAAC,OAAO,CAAC;UACrCG,MAAM,EAAE,CAACyB,OAAO,CAAC5B,YAAY,CAAC,QAAQ;QAC1C,CAAC,CAAC;QACF0F,GAAG,CAACpC,MAAM,GAAG,IAAI;QACjB,OAAOoC,GAAG;MACd,CAAC;MACD,MAAM,EAAE,SAAAvC,CAAUvB,OAAO,EAAEC,WAAW,EAAE;QACpC,IAAIb,CAAC,GAAGY,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG;QACxC,IAAIiB,CAAC,GAAGW,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG;QACxC,IAAI4F,EAAE,GAAGhE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG;QAC1C,IAAI6F,EAAE,GAAGjE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG;QAC1C,IAAI,CAACuD,MAAM,GAAGrD,UAAU,CAACc,CAAC,CAAC,GAAGd,UAAU,CAAC0F,EAAE,CAAC;QAC5C,IAAI,CAACpC,MAAM,GAAGtD,UAAU,CAACe,CAAC,CAAC,GAAGf,UAAU,CAAC2F,EAAE,CAAC;QAC5C,IAAIxB,CAAC,GAAG,IAAI9G,KAAK,CAAC,CAAC;QACnBkG,YAAY,CAAC5B,WAAW,EAAEwC,CAAC,CAAC;QAC5BhE,eAAe,CAACuB,OAAO,EAAEyC,CAAC,EAAE,IAAI,CAACzE,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC;QAC9D,OAAOyE,CAAC;MACZ,CAAC;MACD,OAAO,EAAE,SAAAyB,CAAUlE,OAAO,EAAEC,WAAW,EAAE;QACrC,IAAIb,CAAC,GAAGY,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC;QACjC,IAAIiB,CAAC,GAAGW,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC;QACjC,IAAIgB,CAAC,IAAI,IAAI,EAAE;UACX,IAAI,CAACuC,MAAM,GAAGrD,UAAU,CAACc,CAAC,CAAC;QAC/B;QACA,IAAIC,CAAC,IAAI,IAAI,EAAE;UACX,IAAI,CAACuC,MAAM,GAAGtD,UAAU,CAACe,CAAC,CAAC;QAC/B;QACA,IAAI2E,EAAE,GAAGhE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG;QAC1C,IAAI6F,EAAE,GAAGjE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG;QAC1C,IAAIqE,CAAC,GAAG,IAAI9G,KAAK,CAAC,CAAC;QACnBkG,YAAY,CAAC5B,WAAW,EAAEwC,CAAC,CAAC;QAC5BhE,eAAe,CAACuB,OAAO,EAAEyC,CAAC,EAAE,IAAI,CAACzE,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC;QAC9D,IAAI,CAAC2D,MAAM,IAAIrD,UAAU,CAAC0F,EAAE,CAAC;QAC7B,IAAI,CAACpC,MAAM,IAAItD,UAAU,CAAC2F,EAAE,CAAC;QAC7B,OAAOxB,CAAC;MACZ,CAAC;MACD,MAAM,EAAE,SAAA0B,CAAUnE,OAAO,EAAEC,WAAW,EAAE;QACpC,IAAImE,CAAC,GAAGpE,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE;QACvC,IAAI+F,IAAI,GAAG/H,gBAAgB,CAACgI,CAAC,CAAC;QAC9BvC,YAAY,CAAC5B,WAAW,EAAEkE,IAAI,CAAC;QAC/B1F,eAAe,CAACuB,OAAO,EAAEmE,IAAI,EAAE,IAAI,CAACnG,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC;QAClEmG,IAAI,CAACzC,MAAM,GAAG,IAAI;QAClB,OAAOyC,IAAI;MACf;IACJ,CAAC;EACL,CAAC,CAAE,CAAC;EACJ,OAAO/G,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,IAAI6D,kBAAkB,GAAG;EACrB,gBAAgB,EAAE,SAAAoD,CAAUrE,OAAO,EAAE;IACjC,IAAIgD,EAAE,GAAGsB,QAAQ,CAACtE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IACxD,IAAI6E,EAAE,GAAGqB,QAAQ,CAACtE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IACxD,IAAI8E,EAAE,GAAGoB,QAAQ,CAACtE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,CAAC;IACzD,IAAI+E,EAAE,GAAGmB,QAAQ,CAACtE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IACxD,IAAImG,QAAQ,GAAG,IAAI5H,cAAc,CAACqG,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IACjDqB,oBAAoB,CAACxE,OAAO,EAAEuE,QAAQ,CAAC;IACvCE,uBAAuB,CAACzE,OAAO,EAAEuE,QAAQ,CAAC;IAC1C,OAAOA,QAAQ;EACnB,CAAC;EACD,gBAAgB,EAAE,SAAAG,CAAU1E,OAAO,EAAE;IACjC,IAAI4C,EAAE,GAAG0B,QAAQ,CAACtE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IACxD,IAAIyE,EAAE,GAAGyB,QAAQ,CAACtE,OAAO,CAAC5B,YAAY,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IACxD,IAAI0E,CAAC,GAAGwB,QAAQ,CAACtE,OAAO,CAAC5B,YAAY,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IACtD,IAAImG,QAAQ,GAAG,IAAI3H,cAAc,CAACgG,EAAE,EAAEC,EAAE,EAAEC,CAAC,CAAC;IAC5C0B,oBAAoB,CAACxE,OAAO,EAAEuE,QAAQ,CAAC;IACvCE,uBAAuB,CAACzE,OAAO,EAAEuE,QAAQ,CAAC;IAC1C,OAAOA,QAAQ;EACnB;AACJ,CAAC;AACD,SAASC,oBAAoBA,CAACxE,OAAO,EAAEuE,QAAQ,EAAE;EAC7C,IAAII,aAAa,GAAG3E,OAAO,CAAC5B,YAAY,CAAC,eAAe,CAAC;EACzD,IAAIuG,aAAa,KAAK,gBAAgB,EAAE;IACpCJ,QAAQ,CAACK,MAAM,GAAG,IAAI;EAC1B;AACJ;AACA,SAASH,uBAAuBA,CAACzE,OAAO,EAAEuE,QAAQ,EAAE;EAChD,IAAIM,IAAI,GAAG7E,OAAO,CAACrB,UAAU;EAC7B,OAAOkG,IAAI,EAAE;IACT,IAAIA,IAAI,CAACxD,QAAQ,KAAK,CAAC,IAChBwD,IAAI,CAACxE,QAAQ,CAACyE,iBAAiB,CAAC,CAAC,KAAK,MAAM,EAAE;MACjD,IAAIC,SAAS,GAAGF,IAAI,CAACzG,YAAY,CAAC,QAAQ,CAAC;MAC3C,IAAI4G,MAAM,GAAG,KAAK,CAAC;MACnB,IAAID,SAAS,IAAIA,SAAS,CAACE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QACzCD,MAAM,GAAGV,QAAQ,CAACS,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG;MAC1C,CAAC,MACI,IAAIA,SAAS,EAAE;QAChBC,MAAM,GAAG1G,UAAU,CAACyG,SAAS,CAAC;MAClC,CAAC,MACI;QACDC,MAAM,GAAG,CAAC;MACd;MACA,IAAIE,SAAS,GAAG,CAAC,CAAC;MAClBC,gBAAgB,CAACN,IAAI,EAAEK,SAAS,EAAEA,SAAS,CAAC;MAC5C,IAAIE,SAAS,GAAGF,SAAS,CAACE,SAAS,IAC5BP,IAAI,CAACzG,YAAY,CAAC,YAAY,CAAC,IAC/B,SAAS;MAChBmG,QAAQ,CAACc,UAAU,CAACtE,IAAI,CAAC;QACrBiE,MAAM,EAAEA,MAAM;QACdM,KAAK,EAAEF;MACX,CAAC,CAAC;IACN;IACAP,IAAI,GAAGA,IAAI,CAAChG,WAAW;EAC3B;AACJ;AACA,SAASgD,YAAYA,CAAC0D,MAAM,EAAE7G,KAAK,EAAE;EACjC,IAAI6G,MAAM,IAAIA,MAAM,CAACC,gBAAgB,EAAE;IACnC,IAAI,CAAC9G,KAAK,CAAC8G,gBAAgB,EAAE;MACzB9G,KAAK,CAAC8G,gBAAgB,GAAG,CAAC,CAAC;IAC/B;IACAnJ,QAAQ,CAACqC,KAAK,CAAC8G,gBAAgB,EAAED,MAAM,CAACC,gBAAgB,CAAC;EAC7D;AACJ;AACA,SAAS9B,WAAWA,CAAC+B,YAAY,EAAE;EAC/B,IAAIC,IAAI,GAAGxG,mBAAmB,CAACuG,YAAY,CAAC;EAC5C,IAAI9B,MAAM,GAAG,EAAE;EACf,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACvG,MAAM,EAAEwG,CAAC,IAAI,CAAC,EAAE;IACrC,IAAIvG,CAAC,GAAGd,UAAU,CAACoH,IAAI,CAACC,CAAC,CAAC,CAAC;IAC3B,IAAItG,CAAC,GAAGf,UAAU,CAACoH,IAAI,CAACC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/BhC,MAAM,CAAC5C,IAAI,CAAC,CAAC3B,CAAC,EAAEC,CAAC,CAAC,CAAC;EACvB;EACA,OAAOsE,MAAM;AACjB;AACA,SAASlF,eAAeA,CAACuB,OAAO,EAAEO,EAAE,EAAEqF,cAAc,EAAEC,eAAe,EAAEC,WAAW,EAAE;EAChF,IAAIC,IAAI,GAAGxF,EAAE;EACb,IAAIyF,cAAc,GAAGD,IAAI,CAACP,gBAAgB,GAAGO,IAAI,CAACP,gBAAgB,IAAI,CAAC,CAAC;EACxE,IAAIS,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIjG,OAAO,CAACqB,QAAQ,KAAK,CAAC,EAAE;IACxB6E,uBAAuB,CAAClG,OAAO,EAAEO,EAAE,CAAC;IACpC4E,gBAAgB,CAACnF,OAAO,EAAEgG,cAAc,EAAEC,SAAS,CAAC;IACpD,IAAI,CAACJ,eAAe,EAAE;MAClBM,mBAAmB,CAACnG,OAAO,EAAEgG,cAAc,EAAEC,SAAS,CAAC;IAC3D;EACJ;EACAF,IAAI,CAACvE,KAAK,GAAGuE,IAAI,CAACvE,KAAK,IAAI,CAAC,CAAC;EAC7B,IAAIwE,cAAc,CAACI,IAAI,IAAI,IAAI,EAAE;IAC7BL,IAAI,CAACvE,KAAK,CAAC4E,IAAI,GAAGC,kBAAkB,CAACN,IAAI,EAAE,MAAM,EAAEC,cAAc,CAACI,IAAI,EAAER,cAAc,CAAC;EAC3F;EACA,IAAII,cAAc,CAACM,MAAM,IAAI,IAAI,EAAE;IAC/BP,IAAI,CAACvE,KAAK,CAAC8E,MAAM,GAAGD,kBAAkB,CAACN,IAAI,EAAE,QAAQ,EAAEC,cAAc,CAACM,MAAM,EAAEV,cAAc,CAAC;EACjG;EACArJ,IAAI,CAAC,CACD,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,eAAe,EAAE,YAAY,EAAE,UAAU,CACnF,EAAE,UAAUgK,QAAQ,EAAE;IACnB,IAAIP,cAAc,CAACO,QAAQ,CAAC,IAAI,IAAI,EAAE;MAClCR,IAAI,CAACvE,KAAK,CAAC+E,QAAQ,CAAC,GAAGjI,UAAU,CAAC0H,cAAc,CAACO,QAAQ,CAAC,CAAC;IAC/D;EACJ,CAAC,CAAC;EACFhK,IAAI,CAAC,CACD,gBAAgB,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAChG,EAAE,UAAUgK,QAAQ,EAAE;IACnB,IAAIP,cAAc,CAACO,QAAQ,CAAC,IAAI,IAAI,EAAE;MAClCR,IAAI,CAACvE,KAAK,CAAC+E,QAAQ,CAAC,GAAGP,cAAc,CAACO,QAAQ,CAAC;IACnD;EACJ,CAAC,CAAC;EACF,IAAIT,WAAW,EAAE;IACbC,IAAI,CAACS,WAAW,GAAGP,SAAS;EAChC;EACA,IAAID,cAAc,CAACS,QAAQ,EAAE;IACzBV,IAAI,CAACvE,KAAK,CAACiF,QAAQ,GAAGjK,GAAG,CAAC0C,mBAAmB,CAAC8G,cAAc,CAACS,QAAQ,CAAC,EAAE,UAAUC,GAAG,EAAE;MACnF,OAAOpI,UAAU,CAACoI,GAAG,CAAC;IAC1B,CAAC,CAAC;EACN;EACA,IAAIV,cAAc,CAACW,UAAU,KAAK,QAAQ,IAAIX,cAAc,CAACW,UAAU,KAAK,UAAU,EAAE;IACpFZ,IAAI,CAACa,SAAS,GAAG,IAAI;EACzB;EACA,IAAIZ,cAAc,CAACa,OAAO,KAAK,MAAM,EAAE;IACnCd,IAAI,CAACe,MAAM,GAAG,IAAI;EACtB;AACJ;AACA,SAAShF,kBAAkBA,CAACP,IAAI,EAAEtB,WAAW,EAAE;EAC3C,IAAI8G,eAAe,GAAG9G,WAAW,CAACuG,WAAW;EAC7C,IAAIO,eAAe,EAAE;IACjB,IAAIC,YAAY,GAAGD,eAAe,CAACC,YAAY;IAC/C,IAAIC,cAAc,GAAGD,YAAY;IACjC,IAAI,CAACA,YAAY,IAAIA,YAAY,KAAK,MAAM,EAAE;MAC1CC,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAID,YAAY,KAAK,UAAU,EAAE;MAClCC,cAAc,GAAG,YAAY;IACjC,CAAC,MACI,IAAID,YAAY,KAAK,aAAa,IAAIA,YAAY,KAAK,kBAAkB,EAAE;MAC5EC,cAAc,GAAG,KAAK;IAC1B,CAAC,MACI,IAAID,YAAY,KAAK,YAAY,IAAIA,YAAY,KAAK,iBAAiB,EAAE;MAC1EC,cAAc,GAAG,QAAQ;IAC7B,CAAC,MACI,IAAID,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,cAAc,EAAE;MACpEC,cAAc,GAAG,QAAQ;IAC7B;IACA1F,IAAI,CAACC,KAAK,CAACwF,YAAY,GAAGC,cAAc;EAC5C;EACA,IAAIC,oBAAoB,GAAGjH,WAAW,CAACuF,gBAAgB;EACvD,IAAI0B,oBAAoB,EAAE;IACtB,IAAIC,SAAS,GAAGD,oBAAoB,CAACC,SAAS;IAC9C,IAAIC,WAAW,GAAGD,SAAS;IAC3B,IAAIA,SAAS,EAAE;MACX,IAAIA,SAAS,KAAK,QAAQ,EAAE;QACxBC,WAAW,GAAG,QAAQ;MAC1B;MACA7F,IAAI,CAACC,KAAK,CAAC2F,SAAS,GAAGC,WAAW;IACtC;EACJ;AACJ;AACA,IAAIC,QAAQ,GAAG,mBAAmB;AAClC,SAAShB,kBAAkBA,CAAC9F,EAAE,EAAE+G,MAAM,EAAEZ,GAAG,EAAEd,cAAc,EAAE;EACzD,IAAI2B,QAAQ,GAAGb,GAAG,IAAIA,GAAG,CAACc,KAAK,CAACH,QAAQ,CAAC;EACzC,IAAIE,QAAQ,EAAE;IACV,IAAIE,GAAG,GAAGnL,IAAI,CAACiL,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3B3B,cAAc,CAAC7E,IAAI,CAAC,CAACR,EAAE,EAAE+G,MAAM,EAAEG,GAAG,CAAC,CAAC;IACtC;EACJ;EACA,IAAIf,GAAG,KAAK,MAAM,EAAE;IAChBA,GAAG,GAAG,IAAI;EACd;EACA,OAAOA,GAAG;AACd;AACA,SAAS5H,SAASA,CAAC4I,IAAI,EAAE9B,cAAc,EAAE;EACrC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,cAAc,CAACzG,MAAM,EAAEwG,CAAC,EAAE,EAAE;IAC5C,IAAIgC,IAAI,GAAG/B,cAAc,CAACD,CAAC,CAAC;IAC5BgC,IAAI,CAAC,CAAC,CAAC,CAACnG,KAAK,CAACmG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGD,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1C;AACJ;AACA,IAAIC,SAAS,GAAG,qCAAqC;AACrD,SAAS1I,mBAAmBA,CAAC2I,MAAM,EAAE;EACjC,OAAOA,MAAM,CAACL,KAAK,CAACI,SAAS,CAAC,IAAI,EAAE;AACxC;AACA,IAAIE,cAAc,GAAG,mEAAmE;AACxF,IAAIC,eAAe,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AACnC,SAAS/B,uBAAuBA,CAAClG,OAAO,EAAEkI,IAAI,EAAE;EAC5C,IAAIC,SAAS,GAAGnI,OAAO,CAAC5B,YAAY,CAAC,WAAW,CAAC;EACjD,IAAI+J,SAAS,EAAE;IACXA,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IACxC,IAAIC,cAAc,GAAG,EAAE;IACvB,IAAIC,EAAE,GAAG,IAAI;IACbH,SAAS,CAACC,OAAO,CAACN,cAAc,EAAE,UAAUpB,GAAG,EAAE6B,IAAI,EAAEC,KAAK,EAAE;MAC1DH,cAAc,CAACtH,IAAI,CAACwH,IAAI,EAAEC,KAAK,CAAC;MAChC,OAAO,EAAE;IACb,CAAC,CAAC;IACF,KAAK,IAAI7C,CAAC,GAAG0C,cAAc,CAAClJ,MAAM,GAAG,CAAC,EAAEwG,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACnD,IAAI6C,KAAK,GAAGH,cAAc,CAAC1C,CAAC,CAAC;MAC7B,IAAI4C,IAAI,GAAGF,cAAc,CAAC1C,CAAC,GAAG,CAAC,CAAC;MAChC,IAAI8C,QAAQ,GAAGvJ,mBAAmB,CAACsJ,KAAK,CAAC;MACzCF,EAAE,GAAGA,EAAE,IAAInM,MAAM,CAACuM,MAAM,CAAC,CAAC;MAC1B,QAAQH,IAAI;QACR,KAAK,WAAW;UACZpM,MAAM,CAACwM,SAAS,CAACL,EAAE,EAAEA,EAAE,EAAE,CAAChK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEnK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;UACnF;QACJ,KAAK,OAAO;UACRtM,MAAM,CAACyD,KAAK,CAAC0I,EAAE,EAAEA,EAAE,EAAE,CAAChK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEnK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,IAAIA,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvF;QACJ,KAAK,QAAQ;UACTtM,MAAM,CAACyM,MAAM,CAACN,EAAE,EAAEA,EAAE,EAAE,CAAChK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGV,eAAe,EAAE,CAC9DzJ,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAC9BnK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CACjC,CAAC;UACF;QACJ,KAAK,OAAO;UACR,IAAII,EAAE,GAAGb,IAAI,CAACc,GAAG,CAACxK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGV,eAAe,CAAC;UAC5D5L,MAAM,CAAC4M,GAAG,CAACT,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAEO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEP,EAAE,CAAC;UACvC;QACJ,KAAK,OAAO;UACR,IAAIU,EAAE,GAAGhB,IAAI,CAACc,GAAG,CAACxK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGV,eAAe,CAAC;UAC5D5L,MAAM,CAAC4M,GAAG,CAACT,EAAE,EAAE,CAAC,CAAC,EAAEU,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEV,EAAE,CAAC;UACvC;QACJ,KAAK,QAAQ;UACTA,EAAE,CAAC,CAAC,CAAC,GAAGhK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC/BH,EAAE,CAAC,CAAC,CAAC,GAAGhK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC/BH,EAAE,CAAC,CAAC,CAAC,GAAGhK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC/BH,EAAE,CAAC,CAAC,CAAC,GAAGhK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC/BH,EAAE,CAAC,CAAC,CAAC,GAAGhK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC/BH,EAAE,CAAC,CAAC,CAAC,GAAGhK,UAAU,CAACmK,QAAQ,CAAC,CAAC,CAAC,CAAC;UAC/B;MACR;IACJ;IACAP,IAAI,CAACe,iBAAiB,CAACX,EAAE,CAAC;EAC9B;AACJ;AACA,IAAIY,UAAU,GAAG,4BAA4B;AAC7C,SAAS/D,gBAAgBA,CAACnF,OAAO,EAAEmJ,sBAAsB,EAAEC,eAAe,EAAE;EACxE,IAAI5H,KAAK,GAAGxB,OAAO,CAAC5B,YAAY,CAAC,OAAO,CAAC;EACzC,IAAI,CAACoD,KAAK,EAAE;IACR;EACJ;EACA0H,UAAU,CAACG,SAAS,GAAG,CAAC;EACxB,IAAIC,cAAc;EAClB,OAAO,CAACA,cAAc,GAAGJ,UAAU,CAACK,IAAI,CAAC/H,KAAK,CAAC,KAAK,IAAI,EAAE;IACtD,IAAIgI,UAAU,GAAGF,cAAc,CAAC,CAAC,CAAC;IAClC,IAAIG,oBAAoB,GAAG/M,MAAM,CAACM,gCAAgC,EAAEwM,UAAU,CAAC,GACzExM,gCAAgC,CAACwM,UAAU,CAAC,GAC5C,IAAI;IACV,IAAIC,oBAAoB,EAAE;MACtBN,sBAAsB,CAACM,oBAAoB,CAAC,GAAGH,cAAc,CAAC,CAAC,CAAC;IACpE;IACA,IAAII,aAAa,GAAGhN,MAAM,CAACQ,yBAAyB,EAAEsM,UAAU,CAAC,GAC3DtM,yBAAyB,CAACsM,UAAU,CAAC,GACrC,IAAI;IACV,IAAIE,aAAa,EAAE;MACfN,eAAe,CAACM,aAAa,CAAC,GAAGJ,cAAc,CAAC,CAAC,CAAC;IACtD;EACJ;AACJ;AACA,SAASnD,mBAAmBA,CAACnG,OAAO,EAAEmJ,sBAAsB,EAAEC,eAAe,EAAE;EAC3E,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1I,qCAAqC,CAACkC,MAAM,EAAEwG,CAAC,EAAE,EAAE;IACnE,IAAIgE,WAAW,GAAG1M,qCAAqC,CAAC0I,CAAC,CAAC;IAC1D,IAAIiE,SAAS,GAAG5J,OAAO,CAAC5B,YAAY,CAACuL,WAAW,CAAC;IACjD,IAAIC,SAAS,IAAI,IAAI,EAAE;MACnBT,sBAAsB,CAACnM,gCAAgC,CAAC2M,WAAW,CAAC,CAAC,GAAGC,SAAS;IACrF;EACJ;EACA,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxI,8BAA8B,CAACgC,MAAM,EAAEwG,CAAC,EAAE,EAAE;IAC5D,IAAIgE,WAAW,GAAGxM,8BAA8B,CAACwI,CAAC,CAAC;IACnD,IAAIiE,SAAS,GAAG5J,OAAO,CAAC5B,YAAY,CAACuL,WAAW,CAAC;IACjD,IAAIC,SAAS,IAAI,IAAI,EAAE;MACnBR,eAAe,CAAClM,yBAAyB,CAACyM,WAAW,CAAC,CAAC,GAAGC,SAAS;IACvE;EACJ;AACJ;AACA,OAAO,SAAStK,oBAAoBA,CAACP,WAAW,EAAE8K,YAAY,EAAE;EAC5D,IAAInK,MAAM,GAAGmK,YAAY,CAACxL,KAAK,GAAGU,WAAW,CAACV,KAAK;EACnD,IAAIsB,MAAM,GAAGkK,YAAY,CAACtL,MAAM,GAAGQ,WAAW,CAACR,MAAM;EACrD,IAAIqB,KAAK,GAAGoI,IAAI,CAAC8B,GAAG,CAACpK,MAAM,EAAEC,MAAM,CAAC;EACpC,OAAO;IACHC,KAAK,EAAEA,KAAK;IACZR,CAAC,EAAE,EAAEL,WAAW,CAACK,CAAC,GAAGL,WAAW,CAACV,KAAK,GAAG,CAAC,CAAC,GAAGuB,KAAK,IAAIiK,YAAY,CAACzK,CAAC,GAAGyK,YAAY,CAACxL,KAAK,GAAG,CAAC,CAAC;IAC/FgB,CAAC,EAAE,EAAEN,WAAW,CAACM,CAAC,GAAGN,WAAW,CAACR,MAAM,GAAG,CAAC,CAAC,GAAGqB,KAAK,IAAIiK,YAAY,CAACxK,CAAC,GAAGwK,YAAY,CAACtL,MAAM,GAAG,CAAC;EACpG,CAAC;AACL;AACA,OAAO,SAASwL,QAAQA,CAACtM,GAAG,EAAEC,GAAG,EAAE;EAC/B,IAAIsD,MAAM,GAAG,IAAI5D,SAAS,CAAC,CAAC;EAC5B,OAAO4D,MAAM,CAACxD,KAAK,CAACC,GAAG,EAAEC,GAAG,CAAC;AACjC;AACA,SAASZ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}