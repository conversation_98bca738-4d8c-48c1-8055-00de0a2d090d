{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from './Path.js';\nvar CompoundPath = function (_super) {\n  __extends(CompoundPath, _super);\n  function CompoundPath() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'compound';\n    return _this;\n  }\n  CompoundPath.prototype._updatePathDirty = function () {\n    var paths = this.shape.paths;\n    var dirtyPath = this.shapeChanged();\n    for (var i = 0; i < paths.length; i++) {\n      dirtyPath = dirtyPath || paths[i].shapeChanged();\n    }\n    if (dirtyPath) {\n      this.dirtyShape();\n    }\n  };\n  CompoundPath.prototype.beforeBrush = function () {\n    this._updatePathDirty();\n    var paths = this.shape.paths || [];\n    var scale = this.getGlobalScale();\n    for (var i = 0; i < paths.length; i++) {\n      if (!paths[i].path) {\n        paths[i].createPathProxy();\n      }\n      paths[i].path.setScale(scale[0], scale[1], paths[i].segmentIgnoreThreshold);\n    }\n  };\n  CompoundPath.prototype.buildPath = function (ctx, shape) {\n    var paths = shape.paths || [];\n    for (var i = 0; i < paths.length; i++) {\n      paths[i].buildPath(ctx, paths[i].shape, true);\n    }\n  };\n  CompoundPath.prototype.afterBrush = function () {\n    var paths = this.shape.paths || [];\n    for (var i = 0; i < paths.length; i++) {\n      paths[i].pathUpdated();\n    }\n  };\n  CompoundPath.prototype.getBoundingRect = function () {\n    this._updatePathDirty.call(this);\n    return Path.prototype.getBoundingRect.call(this);\n  };\n  return CompoundPath;\n}(Path);\nexport default CompoundPath;", "map": {"version": 3, "names": ["__extends", "Path", "CompoundPath", "_super", "_this", "apply", "arguments", "type", "prototype", "_updatePathDirty", "paths", "shape", "<PERSON><PERSON><PERSON>", "shapeChanged", "i", "length", "dirtyShape", "beforeBrush", "scale", "getGlobalScale", "path", "createPathProxy", "setScale", "segmentIgnoreThreshold", "buildPath", "ctx", "afterBrush", "pathUpdated", "getBoundingRect", "call"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/zrender/lib/graphic/CompoundPath.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Path from './Path.js';\nvar CompoundPath = (function (_super) {\n    __extends(CompoundPath, _super);\n    function CompoundPath() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = 'compound';\n        return _this;\n    }\n    CompoundPath.prototype._updatePathDirty = function () {\n        var paths = this.shape.paths;\n        var dirtyPath = this.shapeChanged();\n        for (var i = 0; i < paths.length; i++) {\n            dirtyPath = dirtyPath || paths[i].shapeChanged();\n        }\n        if (dirtyPath) {\n            this.dirtyShape();\n        }\n    };\n    CompoundPath.prototype.beforeBrush = function () {\n        this._updatePathDirty();\n        var paths = this.shape.paths || [];\n        var scale = this.getGlobalScale();\n        for (var i = 0; i < paths.length; i++) {\n            if (!paths[i].path) {\n                paths[i].createPathProxy();\n            }\n            paths[i].path.setScale(scale[0], scale[1], paths[i].segmentIgnoreThreshold);\n        }\n    };\n    CompoundPath.prototype.buildPath = function (ctx, shape) {\n        var paths = shape.paths || [];\n        for (var i = 0; i < paths.length; i++) {\n            paths[i].buildPath(ctx, paths[i].shape, true);\n        }\n    };\n    CompoundPath.prototype.afterBrush = function () {\n        var paths = this.shape.paths || [];\n        for (var i = 0; i < paths.length; i++) {\n            paths[i].pathUpdated();\n        }\n    };\n    CompoundPath.prototype.getBoundingRect = function () {\n        this._updatePathDirty.call(this);\n        return Path.prototype.getBoundingRect.call(this);\n    };\n    return CompoundPath;\n}(Path));\nexport default CompoundPath;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,WAAW;AAC5B,IAAIC,YAAY,GAAI,UAAUC,MAAM,EAAE;EAClCH,SAAS,CAACE,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,UAAU;IACvB,OAAOH,KAAK;EAChB;EACAF,YAAY,CAACM,SAAS,CAACC,gBAAgB,GAAG,YAAY;IAClD,IAAIC,KAAK,GAAG,IAAI,CAACC,KAAK,CAACD,KAAK;IAC5B,IAAIE,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCF,SAAS,GAAGA,SAAS,IAAIF,KAAK,CAACI,CAAC,CAAC,CAACD,YAAY,CAAC,CAAC;IACpD;IACA,IAAID,SAAS,EAAE;MACX,IAAI,CAACI,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDd,YAAY,CAACM,SAAS,CAACS,WAAW,GAAG,YAAY;IAC7C,IAAI,CAACR,gBAAgB,CAAC,CAAC;IACvB,IAAIC,KAAK,GAAG,IAAI,CAACC,KAAK,CAACD,KAAK,IAAI,EAAE;IAClC,IAAIQ,KAAK,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACjC,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACnC,IAAI,CAACJ,KAAK,CAACI,CAAC,CAAC,CAACM,IAAI,EAAE;QAChBV,KAAK,CAACI,CAAC,CAAC,CAACO,eAAe,CAAC,CAAC;MAC9B;MACAX,KAAK,CAACI,CAAC,CAAC,CAACM,IAAI,CAACE,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAER,KAAK,CAACI,CAAC,CAAC,CAACS,sBAAsB,CAAC;IAC/E;EACJ,CAAC;EACDrB,YAAY,CAACM,SAAS,CAACgB,SAAS,GAAG,UAAUC,GAAG,EAAEd,KAAK,EAAE;IACrD,IAAID,KAAK,GAAGC,KAAK,CAACD,KAAK,IAAI,EAAE;IAC7B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCJ,KAAK,CAACI,CAAC,CAAC,CAACU,SAAS,CAACC,GAAG,EAAEf,KAAK,CAACI,CAAC,CAAC,CAACH,KAAK,EAAE,IAAI,CAAC;IACjD;EACJ,CAAC;EACDT,YAAY,CAACM,SAAS,CAACkB,UAAU,GAAG,YAAY;IAC5C,IAAIhB,KAAK,GAAG,IAAI,CAACC,KAAK,CAACD,KAAK,IAAI,EAAE;IAClC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCJ,KAAK,CAACI,CAAC,CAAC,CAACa,WAAW,CAAC,CAAC;IAC1B;EACJ,CAAC;EACDzB,YAAY,CAACM,SAAS,CAACoB,eAAe,GAAG,YAAY;IACjD,IAAI,CAACnB,gBAAgB,CAACoB,IAAI,CAAC,IAAI,CAAC;IAChC,OAAO5B,IAAI,CAACO,SAAS,CAACoB,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC;EACpD,CAAC;EACD,OAAO3B,YAAY;AACvB,CAAC,CAACD,IAAI,CAAE;AACR,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}