{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, hasOwn } from 'zrender/lib/core/util.js';\nvar deprecatedLogs = {};\n/**\r\n * Whether need to call `convertEC4CompatibleStyle`.\r\n */\nexport function isEC4CompatibleStyle(style, elType, hasOwnTextContentOption, hasOwnTextConfig) {\n  // Since echarts5, `RectText` is separated from its host element and style.text\n  // does not exist any more. The compat work brings some extra burden on performance.\n  // So we provide:\n  // `legacy: true` force make compat.\n  // `legacy: false`, force do not compat.\n  // `legacy` not set: auto detect whether legacy.\n  //     But in this case we do not compat (difficult to detect and rare case):\n  //     Becuse custom series and graphic component support \"merge\", users may firstly\n  //     only set `textStrokeWidth` style or secondly only set `text`.\n  return style && (style.legacy || style.legacy !== false && !hasOwnTextContentOption && !hasOwnTextConfig && elType !== 'tspan'\n  // Difficult to detect whether legacy for a \"text\" el.\n  && (elType === 'text' || hasOwn(style, 'text')));\n}\n/**\r\n * `EC4CompatibleStyle` is style that might be in echarts4 format or echarts5 format.\r\n * @param hostStyle The properties might be modified.\r\n * @return If be text el, `textContentStyle` and `textConfig` will not be returned.\r\n *         Otherwise a `textContentStyle` and `textConfig` will be created, whose props area\r\n *         retried from the `hostStyle`.\r\n */\nexport function convertFromEC4CompatibleStyle(hostStyle, elType, isNormal) {\n  var srcStyle = hostStyle;\n  var textConfig;\n  var textContent;\n  var textContentStyle;\n  if (elType === 'text') {\n    textContentStyle = srcStyle;\n  } else {\n    textContentStyle = {};\n    hasOwn(srcStyle, 'text') && (textContentStyle.text = srcStyle.text);\n    hasOwn(srcStyle, 'rich') && (textContentStyle.rich = srcStyle.rich);\n    hasOwn(srcStyle, 'textFill') && (textContentStyle.fill = srcStyle.textFill);\n    hasOwn(srcStyle, 'textStroke') && (textContentStyle.stroke = srcStyle.textStroke);\n    hasOwn(srcStyle, 'fontFamily') && (textContentStyle.fontFamily = srcStyle.fontFamily);\n    hasOwn(srcStyle, 'fontSize') && (textContentStyle.fontSize = srcStyle.fontSize);\n    hasOwn(srcStyle, 'fontStyle') && (textContentStyle.fontStyle = srcStyle.fontStyle);\n    hasOwn(srcStyle, 'fontWeight') && (textContentStyle.fontWeight = srcStyle.fontWeight);\n    textContent = {\n      type: 'text',\n      style: textContentStyle,\n      // ec4 does not support rectText trigger.\n      // And when text position is different in normal and emphasis\n      // => hover text trigger emphasis;\n      // => text position changed, leave mouse pointer immediately;\n      // That might cause incorrect state.\n      silent: true\n    };\n    textConfig = {};\n    var hasOwnPos = hasOwn(srcStyle, 'textPosition');\n    if (isNormal) {\n      textConfig.position = hasOwnPos ? srcStyle.textPosition : 'inside';\n    } else {\n      hasOwnPos && (textConfig.position = srcStyle.textPosition);\n    }\n    hasOwn(srcStyle, 'textPosition') && (textConfig.position = srcStyle.textPosition);\n    hasOwn(srcStyle, 'textOffset') && (textConfig.offset = srcStyle.textOffset);\n    hasOwn(srcStyle, 'textRotation') && (textConfig.rotation = srcStyle.textRotation);\n    hasOwn(srcStyle, 'textDistance') && (textConfig.distance = srcStyle.textDistance);\n  }\n  convertEC4CompatibleRichItem(textContentStyle, hostStyle);\n  each(textContentStyle.rich, function (richItem) {\n    convertEC4CompatibleRichItem(richItem, richItem);\n  });\n  return {\n    textConfig: textConfig,\n    textContent: textContent\n  };\n}\n/**\r\n * The result will be set to `out`.\r\n */\nfunction convertEC4CompatibleRichItem(out, richItem) {\n  if (!richItem) {\n    return;\n  }\n  // (1) For simplicity, make textXXX properties (deprecated since ec5) has\n  // higher priority. For example, consider in ec4 `borderColor: 5, textBorderColor: 10`\n  // on a rect means `borderColor: 4` on the rect and `borderColor: 10` on an attached\n  // richText in ec5.\n  // (2) `out === richItem` if and only if `out` is text el or rich item.\n  // So we can overwrite existing props in `out` since textXXX has higher priority.\n  richItem.font = richItem.textFont || richItem.font;\n  hasOwn(richItem, 'textStrokeWidth') && (out.lineWidth = richItem.textStrokeWidth);\n  hasOwn(richItem, 'textAlign') && (out.align = richItem.textAlign);\n  hasOwn(richItem, 'textVerticalAlign') && (out.verticalAlign = richItem.textVerticalAlign);\n  hasOwn(richItem, 'textLineHeight') && (out.lineHeight = richItem.textLineHeight);\n  hasOwn(richItem, 'textWidth') && (out.width = richItem.textWidth);\n  hasOwn(richItem, 'textHeight') && (out.height = richItem.textHeight);\n  hasOwn(richItem, 'textBackgroundColor') && (out.backgroundColor = richItem.textBackgroundColor);\n  hasOwn(richItem, 'textPadding') && (out.padding = richItem.textPadding);\n  hasOwn(richItem, 'textBorderColor') && (out.borderColor = richItem.textBorderColor);\n  hasOwn(richItem, 'textBorderWidth') && (out.borderWidth = richItem.textBorderWidth);\n  hasOwn(richItem, 'textBorderRadius') && (out.borderRadius = richItem.textBorderRadius);\n  hasOwn(richItem, 'textBoxShadowColor') && (out.shadowColor = richItem.textBoxShadowColor);\n  hasOwn(richItem, 'textBoxShadowBlur') && (out.shadowBlur = richItem.textBoxShadowBlur);\n  hasOwn(richItem, 'textBoxShadowOffsetX') && (out.shadowOffsetX = richItem.textBoxShadowOffsetX);\n  hasOwn(richItem, 'textBoxShadowOffsetY') && (out.shadowOffsetY = richItem.textBoxShadowOffsetY);\n}\n/**\r\n * Convert to pure echarts4 format style.\r\n * `itemStyle` will be modified, added with ec4 style properties from\r\n * `textStyle` and `textConfig`.\r\n *\r\n * [Caveat]: For simplicity, `insideRollback` in ec4 does not compat, where\r\n * `styleEmphasis: {textFill: 'red'}` will remove the normal auto added stroke.\r\n */\nexport function convertToEC4StyleForCustomSerise(itemStl, txStl, txCfg) {\n  var out = itemStl;\n  // See `custom.ts`, a trick to set extra `textPosition` firstly.\n  out.textPosition = out.textPosition || txCfg.position || 'inside';\n  txCfg.offset != null && (out.textOffset = txCfg.offset);\n  txCfg.rotation != null && (out.textRotation = txCfg.rotation);\n  txCfg.distance != null && (out.textDistance = txCfg.distance);\n  var isInside = out.textPosition.indexOf('inside') >= 0;\n  var hostFill = itemStl.fill || '#000';\n  convertToEC4RichItem(out, txStl);\n  var textFillNotSet = out.textFill == null;\n  if (isInside) {\n    if (textFillNotSet) {\n      out.textFill = txCfg.insideFill || '#fff';\n      !out.textStroke && txCfg.insideStroke && (out.textStroke = txCfg.insideStroke);\n      !out.textStroke && (out.textStroke = hostFill);\n      out.textStrokeWidth == null && (out.textStrokeWidth = 2);\n    }\n  } else {\n    if (textFillNotSet) {\n      out.textFill = itemStl.fill || txCfg.outsideFill || '#000';\n    }\n    !out.textStroke && txCfg.outsideStroke && (out.textStroke = txCfg.outsideStroke);\n  }\n  out.text = txStl.text;\n  out.rich = txStl.rich;\n  each(txStl.rich, function (richItem) {\n    convertToEC4RichItem(richItem, richItem);\n  });\n  return out;\n}\nfunction convertToEC4RichItem(out, richItem) {\n  if (!richItem) {\n    return;\n  }\n  hasOwn(richItem, 'fill') && (out.textFill = richItem.fill);\n  hasOwn(richItem, 'stroke') && (out.textStroke = richItem.fill);\n  hasOwn(richItem, 'lineWidth') && (out.textStrokeWidth = richItem.lineWidth);\n  hasOwn(richItem, 'font') && (out.font = richItem.font);\n  hasOwn(richItem, 'fontStyle') && (out.fontStyle = richItem.fontStyle);\n  hasOwn(richItem, 'fontWeight') && (out.fontWeight = richItem.fontWeight);\n  hasOwn(richItem, 'fontSize') && (out.fontSize = richItem.fontSize);\n  hasOwn(richItem, 'fontFamily') && (out.fontFamily = richItem.fontFamily);\n  hasOwn(richItem, 'align') && (out.textAlign = richItem.align);\n  hasOwn(richItem, 'verticalAlign') && (out.textVerticalAlign = richItem.verticalAlign);\n  hasOwn(richItem, 'lineHeight') && (out.textLineHeight = richItem.lineHeight);\n  hasOwn(richItem, 'width') && (out.textWidth = richItem.width);\n  hasOwn(richItem, 'height') && (out.textHeight = richItem.height);\n  hasOwn(richItem, 'backgroundColor') && (out.textBackgroundColor = richItem.backgroundColor);\n  hasOwn(richItem, 'padding') && (out.textPadding = richItem.padding);\n  hasOwn(richItem, 'borderColor') && (out.textBorderColor = richItem.borderColor);\n  hasOwn(richItem, 'borderWidth') && (out.textBorderWidth = richItem.borderWidth);\n  hasOwn(richItem, 'borderRadius') && (out.textBorderRadius = richItem.borderRadius);\n  hasOwn(richItem, 'shadowColor') && (out.textBoxShadowColor = richItem.shadowColor);\n  hasOwn(richItem, 'shadowBlur') && (out.textBoxShadowBlur = richItem.shadowBlur);\n  hasOwn(richItem, 'shadowOffsetX') && (out.textBoxShadowOffsetX = richItem.shadowOffsetX);\n  hasOwn(richItem, 'shadowOffsetY') && (out.textBoxShadowOffsetY = richItem.shadowOffsetY);\n  hasOwn(richItem, 'textShadowColor') && (out.textShadowColor = richItem.textShadowColor);\n  hasOwn(richItem, 'textShadowBlur') && (out.textShadowBlur = richItem.textShadowBlur);\n  hasOwn(richItem, 'textShadowOffsetX') && (out.textShadowOffsetX = richItem.textShadowOffsetX);\n  hasOwn(richItem, 'textShadowOffsetY') && (out.textShadowOffsetY = richItem.textShadowOffsetY);\n}\nexport function warnDeprecated(deprecated, insteadApproach) {\n  if (process.env.NODE_ENV !== 'production') {\n    var key = deprecated + '^_^' + insteadApproach;\n    if (!deprecatedLogs[key]) {\n      console.warn(\"[ECharts] DEPRECATED: \\\"\" + deprecated + \"\\\" has been deprecated. \" + insteadApproach);\n      deprecatedLogs[key] = true;\n    }\n  }\n}", "map": {"version": 3, "names": ["each", "hasOwn", "deprecatedLogs", "isEC4CompatibleStyle", "style", "elType", "hasOwnTextContentOption", "hasOwnTextConfig", "legacy", "convertFromEC4CompatibleStyle", "hostStyle", "isNormal", "srcStyle", "textConfig", "textContent", "textContentStyle", "text", "rich", "fill", "textFill", "stroke", "textStroke", "fontFamily", "fontSize", "fontStyle", "fontWeight", "type", "silent", "hasOwnPos", "position", "textPosition", "offset", "textOffset", "rotation", "textRotation", "distance", "textDistance", "convertEC4CompatibleRichItem", "richItem", "out", "font", "textFont", "lineWidth", "textStrokeWidth", "align", "textAlign", "verticalAlign", "textVerticalAlign", "lineHeight", "textLineHeight", "width", "textWidth", "height", "textHeight", "backgroundColor", "textBackgroundColor", "padding", "textPadding", "borderColor", "textBorderColor", "borderWidth", "textBorder<PERSON>idth", "borderRadius", "textBorderRadius", "shadowColor", "textBoxShadowColor", "<PERSON><PERSON><PERSON><PERSON>", "textBoxShadowBlur", "shadowOffsetX", "textBoxShadowOffsetX", "shadowOffsetY", "textBoxShadowOffsetY", "convertToEC4StyleForCustomSerise", "itemStl", "txStl", "txCfg", "isInside", "indexOf", "hostFill", "convertToEC4RichItem", "textFillNotSet", "insideFill", "insideStroke", "outsideFill", "outsideStroke", "textShadowColor", "textShadowBlur", "textShadowOffsetX", "textShadowOffsetY", "warnDeprecated", "deprecated", "insteadApproach", "process", "env", "NODE_ENV", "key", "console", "warn"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/util/styleCompat.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, hasOwn } from 'zrender/lib/core/util.js';\nvar deprecatedLogs = {};\n/**\r\n * Whether need to call `convertEC4CompatibleStyle`.\r\n */\nexport function isEC4CompatibleStyle(style, elType, hasOwnTextContentOption, hasOwnTextConfig) {\n  // Since echarts5, `RectText` is separated from its host element and style.text\n  // does not exist any more. The compat work brings some extra burden on performance.\n  // So we provide:\n  // `legacy: true` force make compat.\n  // `legacy: false`, force do not compat.\n  // `legacy` not set: auto detect whether legacy.\n  //     But in this case we do not compat (difficult to detect and rare case):\n  //     Becuse custom series and graphic component support \"merge\", users may firstly\n  //     only set `textStrokeWidth` style or secondly only set `text`.\n  return style && (style.legacy || style.legacy !== false && !hasOwnTextContentOption && !hasOwnTextConfig && elType !== 'tspan'\n  // Difficult to detect whether legacy for a \"text\" el.\n  && (elType === 'text' || hasOwn(style, 'text')));\n}\n/**\r\n * `EC4CompatibleStyle` is style that might be in echarts4 format or echarts5 format.\r\n * @param hostStyle The properties might be modified.\r\n * @return If be text el, `textContentStyle` and `textConfig` will not be returned.\r\n *         Otherwise a `textContentStyle` and `textConfig` will be created, whose props area\r\n *         retried from the `hostStyle`.\r\n */\nexport function convertFromEC4CompatibleStyle(hostStyle, elType, isNormal) {\n  var srcStyle = hostStyle;\n  var textConfig;\n  var textContent;\n  var textContentStyle;\n  if (elType === 'text') {\n    textContentStyle = srcStyle;\n  } else {\n    textContentStyle = {};\n    hasOwn(srcStyle, 'text') && (textContentStyle.text = srcStyle.text);\n    hasOwn(srcStyle, 'rich') && (textContentStyle.rich = srcStyle.rich);\n    hasOwn(srcStyle, 'textFill') && (textContentStyle.fill = srcStyle.textFill);\n    hasOwn(srcStyle, 'textStroke') && (textContentStyle.stroke = srcStyle.textStroke);\n    hasOwn(srcStyle, 'fontFamily') && (textContentStyle.fontFamily = srcStyle.fontFamily);\n    hasOwn(srcStyle, 'fontSize') && (textContentStyle.fontSize = srcStyle.fontSize);\n    hasOwn(srcStyle, 'fontStyle') && (textContentStyle.fontStyle = srcStyle.fontStyle);\n    hasOwn(srcStyle, 'fontWeight') && (textContentStyle.fontWeight = srcStyle.fontWeight);\n    textContent = {\n      type: 'text',\n      style: textContentStyle,\n      // ec4 does not support rectText trigger.\n      // And when text position is different in normal and emphasis\n      // => hover text trigger emphasis;\n      // => text position changed, leave mouse pointer immediately;\n      // That might cause incorrect state.\n      silent: true\n    };\n    textConfig = {};\n    var hasOwnPos = hasOwn(srcStyle, 'textPosition');\n    if (isNormal) {\n      textConfig.position = hasOwnPos ? srcStyle.textPosition : 'inside';\n    } else {\n      hasOwnPos && (textConfig.position = srcStyle.textPosition);\n    }\n    hasOwn(srcStyle, 'textPosition') && (textConfig.position = srcStyle.textPosition);\n    hasOwn(srcStyle, 'textOffset') && (textConfig.offset = srcStyle.textOffset);\n    hasOwn(srcStyle, 'textRotation') && (textConfig.rotation = srcStyle.textRotation);\n    hasOwn(srcStyle, 'textDistance') && (textConfig.distance = srcStyle.textDistance);\n  }\n  convertEC4CompatibleRichItem(textContentStyle, hostStyle);\n  each(textContentStyle.rich, function (richItem) {\n    convertEC4CompatibleRichItem(richItem, richItem);\n  });\n  return {\n    textConfig: textConfig,\n    textContent: textContent\n  };\n}\n/**\r\n * The result will be set to `out`.\r\n */\nfunction convertEC4CompatibleRichItem(out, richItem) {\n  if (!richItem) {\n    return;\n  }\n  // (1) For simplicity, make textXXX properties (deprecated since ec5) has\n  // higher priority. For example, consider in ec4 `borderColor: 5, textBorderColor: 10`\n  // on a rect means `borderColor: 4` on the rect and `borderColor: 10` on an attached\n  // richText in ec5.\n  // (2) `out === richItem` if and only if `out` is text el or rich item.\n  // So we can overwrite existing props in `out` since textXXX has higher priority.\n  richItem.font = richItem.textFont || richItem.font;\n  hasOwn(richItem, 'textStrokeWidth') && (out.lineWidth = richItem.textStrokeWidth);\n  hasOwn(richItem, 'textAlign') && (out.align = richItem.textAlign);\n  hasOwn(richItem, 'textVerticalAlign') && (out.verticalAlign = richItem.textVerticalAlign);\n  hasOwn(richItem, 'textLineHeight') && (out.lineHeight = richItem.textLineHeight);\n  hasOwn(richItem, 'textWidth') && (out.width = richItem.textWidth);\n  hasOwn(richItem, 'textHeight') && (out.height = richItem.textHeight);\n  hasOwn(richItem, 'textBackgroundColor') && (out.backgroundColor = richItem.textBackgroundColor);\n  hasOwn(richItem, 'textPadding') && (out.padding = richItem.textPadding);\n  hasOwn(richItem, 'textBorderColor') && (out.borderColor = richItem.textBorderColor);\n  hasOwn(richItem, 'textBorderWidth') && (out.borderWidth = richItem.textBorderWidth);\n  hasOwn(richItem, 'textBorderRadius') && (out.borderRadius = richItem.textBorderRadius);\n  hasOwn(richItem, 'textBoxShadowColor') && (out.shadowColor = richItem.textBoxShadowColor);\n  hasOwn(richItem, 'textBoxShadowBlur') && (out.shadowBlur = richItem.textBoxShadowBlur);\n  hasOwn(richItem, 'textBoxShadowOffsetX') && (out.shadowOffsetX = richItem.textBoxShadowOffsetX);\n  hasOwn(richItem, 'textBoxShadowOffsetY') && (out.shadowOffsetY = richItem.textBoxShadowOffsetY);\n}\n/**\r\n * Convert to pure echarts4 format style.\r\n * `itemStyle` will be modified, added with ec4 style properties from\r\n * `textStyle` and `textConfig`.\r\n *\r\n * [Caveat]: For simplicity, `insideRollback` in ec4 does not compat, where\r\n * `styleEmphasis: {textFill: 'red'}` will remove the normal auto added stroke.\r\n */\nexport function convertToEC4StyleForCustomSerise(itemStl, txStl, txCfg) {\n  var out = itemStl;\n  // See `custom.ts`, a trick to set extra `textPosition` firstly.\n  out.textPosition = out.textPosition || txCfg.position || 'inside';\n  txCfg.offset != null && (out.textOffset = txCfg.offset);\n  txCfg.rotation != null && (out.textRotation = txCfg.rotation);\n  txCfg.distance != null && (out.textDistance = txCfg.distance);\n  var isInside = out.textPosition.indexOf('inside') >= 0;\n  var hostFill = itemStl.fill || '#000';\n  convertToEC4RichItem(out, txStl);\n  var textFillNotSet = out.textFill == null;\n  if (isInside) {\n    if (textFillNotSet) {\n      out.textFill = txCfg.insideFill || '#fff';\n      !out.textStroke && txCfg.insideStroke && (out.textStroke = txCfg.insideStroke);\n      !out.textStroke && (out.textStroke = hostFill);\n      out.textStrokeWidth == null && (out.textStrokeWidth = 2);\n    }\n  } else {\n    if (textFillNotSet) {\n      out.textFill = itemStl.fill || txCfg.outsideFill || '#000';\n    }\n    !out.textStroke && txCfg.outsideStroke && (out.textStroke = txCfg.outsideStroke);\n  }\n  out.text = txStl.text;\n  out.rich = txStl.rich;\n  each(txStl.rich, function (richItem) {\n    convertToEC4RichItem(richItem, richItem);\n  });\n  return out;\n}\nfunction convertToEC4RichItem(out, richItem) {\n  if (!richItem) {\n    return;\n  }\n  hasOwn(richItem, 'fill') && (out.textFill = richItem.fill);\n  hasOwn(richItem, 'stroke') && (out.textStroke = richItem.fill);\n  hasOwn(richItem, 'lineWidth') && (out.textStrokeWidth = richItem.lineWidth);\n  hasOwn(richItem, 'font') && (out.font = richItem.font);\n  hasOwn(richItem, 'fontStyle') && (out.fontStyle = richItem.fontStyle);\n  hasOwn(richItem, 'fontWeight') && (out.fontWeight = richItem.fontWeight);\n  hasOwn(richItem, 'fontSize') && (out.fontSize = richItem.fontSize);\n  hasOwn(richItem, 'fontFamily') && (out.fontFamily = richItem.fontFamily);\n  hasOwn(richItem, 'align') && (out.textAlign = richItem.align);\n  hasOwn(richItem, 'verticalAlign') && (out.textVerticalAlign = richItem.verticalAlign);\n  hasOwn(richItem, 'lineHeight') && (out.textLineHeight = richItem.lineHeight);\n  hasOwn(richItem, 'width') && (out.textWidth = richItem.width);\n  hasOwn(richItem, 'height') && (out.textHeight = richItem.height);\n  hasOwn(richItem, 'backgroundColor') && (out.textBackgroundColor = richItem.backgroundColor);\n  hasOwn(richItem, 'padding') && (out.textPadding = richItem.padding);\n  hasOwn(richItem, 'borderColor') && (out.textBorderColor = richItem.borderColor);\n  hasOwn(richItem, 'borderWidth') && (out.textBorderWidth = richItem.borderWidth);\n  hasOwn(richItem, 'borderRadius') && (out.textBorderRadius = richItem.borderRadius);\n  hasOwn(richItem, 'shadowColor') && (out.textBoxShadowColor = richItem.shadowColor);\n  hasOwn(richItem, 'shadowBlur') && (out.textBoxShadowBlur = richItem.shadowBlur);\n  hasOwn(richItem, 'shadowOffsetX') && (out.textBoxShadowOffsetX = richItem.shadowOffsetX);\n  hasOwn(richItem, 'shadowOffsetY') && (out.textBoxShadowOffsetY = richItem.shadowOffsetY);\n  hasOwn(richItem, 'textShadowColor') && (out.textShadowColor = richItem.textShadowColor);\n  hasOwn(richItem, 'textShadowBlur') && (out.textShadowBlur = richItem.textShadowBlur);\n  hasOwn(richItem, 'textShadowOffsetX') && (out.textShadowOffsetX = richItem.textShadowOffsetX);\n  hasOwn(richItem, 'textShadowOffsetY') && (out.textShadowOffsetY = richItem.textShadowOffsetY);\n}\nexport function warnDeprecated(deprecated, insteadApproach) {\n  if (process.env.NODE_ENV !== 'production') {\n    var key = deprecated + '^_^' + insteadApproach;\n    if (!deprecatedLogs[key]) {\n      console.warn(\"[ECharts] DEPRECATED: \\\"\" + deprecated + \"\\\" has been deprecated. \" + insteadApproach);\n      deprecatedLogs[key] = true;\n    }\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,MAAM,QAAQ,0BAA0B;AACvD,IAAIC,cAAc,GAAG,CAAC,CAAC;AACvB;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,MAAM,EAAEC,uBAAuB,EAAEC,gBAAgB,EAAE;EAC7F;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOH,KAAK,KAAKA,KAAK,CAACI,MAAM,IAAIJ,KAAK,CAACI,MAAM,KAAK,KAAK,IAAI,CAACF,uBAAuB,IAAI,CAACC,gBAAgB,IAAIF,MAAM,KAAK;EACvH;EAAA,IACIA,MAAM,KAAK,MAAM,IAAIJ,MAAM,CAACG,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,6BAA6BA,CAACC,SAAS,EAAEL,MAAM,EAAEM,QAAQ,EAAE;EACzE,IAAIC,QAAQ,GAAGF,SAAS;EACxB,IAAIG,UAAU;EACd,IAAIC,WAAW;EACf,IAAIC,gBAAgB;EACpB,IAAIV,MAAM,KAAK,MAAM,EAAE;IACrBU,gBAAgB,GAAGH,QAAQ;EAC7B,CAAC,MAAM;IACLG,gBAAgB,GAAG,CAAC,CAAC;IACrBd,MAAM,CAACW,QAAQ,EAAE,MAAM,CAAC,KAAKG,gBAAgB,CAACC,IAAI,GAAGJ,QAAQ,CAACI,IAAI,CAAC;IACnEf,MAAM,CAACW,QAAQ,EAAE,MAAM,CAAC,KAAKG,gBAAgB,CAACE,IAAI,GAAGL,QAAQ,CAACK,IAAI,CAAC;IACnEhB,MAAM,CAACW,QAAQ,EAAE,UAAU,CAAC,KAAKG,gBAAgB,CAACG,IAAI,GAAGN,QAAQ,CAACO,QAAQ,CAAC;IAC3ElB,MAAM,CAACW,QAAQ,EAAE,YAAY,CAAC,KAAKG,gBAAgB,CAACK,MAAM,GAAGR,QAAQ,CAACS,UAAU,CAAC;IACjFpB,MAAM,CAACW,QAAQ,EAAE,YAAY,CAAC,KAAKG,gBAAgB,CAACO,UAAU,GAAGV,QAAQ,CAACU,UAAU,CAAC;IACrFrB,MAAM,CAACW,QAAQ,EAAE,UAAU,CAAC,KAAKG,gBAAgB,CAACQ,QAAQ,GAAGX,QAAQ,CAACW,QAAQ,CAAC;IAC/EtB,MAAM,CAACW,QAAQ,EAAE,WAAW,CAAC,KAAKG,gBAAgB,CAACS,SAAS,GAAGZ,QAAQ,CAACY,SAAS,CAAC;IAClFvB,MAAM,CAACW,QAAQ,EAAE,YAAY,CAAC,KAAKG,gBAAgB,CAACU,UAAU,GAAGb,QAAQ,CAACa,UAAU,CAAC;IACrFX,WAAW,GAAG;MACZY,IAAI,EAAE,MAAM;MACZtB,KAAK,EAAEW,gBAAgB;MACvB;MACA;MACA;MACA;MACA;MACAY,MAAM,EAAE;IACV,CAAC;IACDd,UAAU,GAAG,CAAC,CAAC;IACf,IAAIe,SAAS,GAAG3B,MAAM,CAACW,QAAQ,EAAE,cAAc,CAAC;IAChD,IAAID,QAAQ,EAAE;MACZE,UAAU,CAACgB,QAAQ,GAAGD,SAAS,GAAGhB,QAAQ,CAACkB,YAAY,GAAG,QAAQ;IACpE,CAAC,MAAM;MACLF,SAAS,KAAKf,UAAU,CAACgB,QAAQ,GAAGjB,QAAQ,CAACkB,YAAY,CAAC;IAC5D;IACA7B,MAAM,CAACW,QAAQ,EAAE,cAAc,CAAC,KAAKC,UAAU,CAACgB,QAAQ,GAAGjB,QAAQ,CAACkB,YAAY,CAAC;IACjF7B,MAAM,CAACW,QAAQ,EAAE,YAAY,CAAC,KAAKC,UAAU,CAACkB,MAAM,GAAGnB,QAAQ,CAACoB,UAAU,CAAC;IAC3E/B,MAAM,CAACW,QAAQ,EAAE,cAAc,CAAC,KAAKC,UAAU,CAACoB,QAAQ,GAAGrB,QAAQ,CAACsB,YAAY,CAAC;IACjFjC,MAAM,CAACW,QAAQ,EAAE,cAAc,CAAC,KAAKC,UAAU,CAACsB,QAAQ,GAAGvB,QAAQ,CAACwB,YAAY,CAAC;EACnF;EACAC,4BAA4B,CAACtB,gBAAgB,EAAEL,SAAS,CAAC;EACzDV,IAAI,CAACe,gBAAgB,CAACE,IAAI,EAAE,UAAUqB,QAAQ,EAAE;IAC9CD,4BAA4B,CAACC,QAAQ,EAAEA,QAAQ,CAAC;EAClD,CAAC,CAAC;EACF,OAAO;IACLzB,UAAU,EAAEA,UAAU;IACtBC,WAAW,EAAEA;EACf,CAAC;AACH;AACA;AACA;AACA;AACA,SAASuB,4BAA4BA,CAACE,GAAG,EAAED,QAAQ,EAAE;EACnD,IAAI,CAACA,QAAQ,EAAE;IACb;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACAA,QAAQ,CAACE,IAAI,GAAGF,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACE,IAAI;EAClDvC,MAAM,CAACqC,QAAQ,EAAE,iBAAiB,CAAC,KAAKC,GAAG,CAACG,SAAS,GAAGJ,QAAQ,CAACK,eAAe,CAAC;EACjF1C,MAAM,CAACqC,QAAQ,EAAE,WAAW,CAAC,KAAKC,GAAG,CAACK,KAAK,GAAGN,QAAQ,CAACO,SAAS,CAAC;EACjE5C,MAAM,CAACqC,QAAQ,EAAE,mBAAmB,CAAC,KAAKC,GAAG,CAACO,aAAa,GAAGR,QAAQ,CAACS,iBAAiB,CAAC;EACzF9C,MAAM,CAACqC,QAAQ,EAAE,gBAAgB,CAAC,KAAKC,GAAG,CAACS,UAAU,GAAGV,QAAQ,CAACW,cAAc,CAAC;EAChFhD,MAAM,CAACqC,QAAQ,EAAE,WAAW,CAAC,KAAKC,GAAG,CAACW,KAAK,GAAGZ,QAAQ,CAACa,SAAS,CAAC;EACjElD,MAAM,CAACqC,QAAQ,EAAE,YAAY,CAAC,KAAKC,GAAG,CAACa,MAAM,GAAGd,QAAQ,CAACe,UAAU,CAAC;EACpEpD,MAAM,CAACqC,QAAQ,EAAE,qBAAqB,CAAC,KAAKC,GAAG,CAACe,eAAe,GAAGhB,QAAQ,CAACiB,mBAAmB,CAAC;EAC/FtD,MAAM,CAACqC,QAAQ,EAAE,aAAa,CAAC,KAAKC,GAAG,CAACiB,OAAO,GAAGlB,QAAQ,CAACmB,WAAW,CAAC;EACvExD,MAAM,CAACqC,QAAQ,EAAE,iBAAiB,CAAC,KAAKC,GAAG,CAACmB,WAAW,GAAGpB,QAAQ,CAACqB,eAAe,CAAC;EACnF1D,MAAM,CAACqC,QAAQ,EAAE,iBAAiB,CAAC,KAAKC,GAAG,CAACqB,WAAW,GAAGtB,QAAQ,CAACuB,eAAe,CAAC;EACnF5D,MAAM,CAACqC,QAAQ,EAAE,kBAAkB,CAAC,KAAKC,GAAG,CAACuB,YAAY,GAAGxB,QAAQ,CAACyB,gBAAgB,CAAC;EACtF9D,MAAM,CAACqC,QAAQ,EAAE,oBAAoB,CAAC,KAAKC,GAAG,CAACyB,WAAW,GAAG1B,QAAQ,CAAC2B,kBAAkB,CAAC;EACzFhE,MAAM,CAACqC,QAAQ,EAAE,mBAAmB,CAAC,KAAKC,GAAG,CAAC2B,UAAU,GAAG5B,QAAQ,CAAC6B,iBAAiB,CAAC;EACtFlE,MAAM,CAACqC,QAAQ,EAAE,sBAAsB,CAAC,KAAKC,GAAG,CAAC6B,aAAa,GAAG9B,QAAQ,CAAC+B,oBAAoB,CAAC;EAC/FpE,MAAM,CAACqC,QAAQ,EAAE,sBAAsB,CAAC,KAAKC,GAAG,CAAC+B,aAAa,GAAGhC,QAAQ,CAACiC,oBAAoB,CAAC;AACjG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gCAAgCA,CAACC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACtE,IAAIpC,GAAG,GAAGkC,OAAO;EACjB;EACAlC,GAAG,CAACT,YAAY,GAAGS,GAAG,CAACT,YAAY,IAAI6C,KAAK,CAAC9C,QAAQ,IAAI,QAAQ;EACjE8C,KAAK,CAAC5C,MAAM,IAAI,IAAI,KAAKQ,GAAG,CAACP,UAAU,GAAG2C,KAAK,CAAC5C,MAAM,CAAC;EACvD4C,KAAK,CAAC1C,QAAQ,IAAI,IAAI,KAAKM,GAAG,CAACL,YAAY,GAAGyC,KAAK,CAAC1C,QAAQ,CAAC;EAC7D0C,KAAK,CAACxC,QAAQ,IAAI,IAAI,KAAKI,GAAG,CAACH,YAAY,GAAGuC,KAAK,CAACxC,QAAQ,CAAC;EAC7D,IAAIyC,QAAQ,GAAGrC,GAAG,CAACT,YAAY,CAAC+C,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;EACtD,IAAIC,QAAQ,GAAGL,OAAO,CAACvD,IAAI,IAAI,MAAM;EACrC6D,oBAAoB,CAACxC,GAAG,EAAEmC,KAAK,CAAC;EAChC,IAAIM,cAAc,GAAGzC,GAAG,CAACpB,QAAQ,IAAI,IAAI;EACzC,IAAIyD,QAAQ,EAAE;IACZ,IAAII,cAAc,EAAE;MAClBzC,GAAG,CAACpB,QAAQ,GAAGwD,KAAK,CAACM,UAAU,IAAI,MAAM;MACzC,CAAC1C,GAAG,CAAClB,UAAU,IAAIsD,KAAK,CAACO,YAAY,KAAK3C,GAAG,CAAClB,UAAU,GAAGsD,KAAK,CAACO,YAAY,CAAC;MAC9E,CAAC3C,GAAG,CAAClB,UAAU,KAAKkB,GAAG,CAAClB,UAAU,GAAGyD,QAAQ,CAAC;MAC9CvC,GAAG,CAACI,eAAe,IAAI,IAAI,KAAKJ,GAAG,CAACI,eAAe,GAAG,CAAC,CAAC;IAC1D;EACF,CAAC,MAAM;IACL,IAAIqC,cAAc,EAAE;MAClBzC,GAAG,CAACpB,QAAQ,GAAGsD,OAAO,CAACvD,IAAI,IAAIyD,KAAK,CAACQ,WAAW,IAAI,MAAM;IAC5D;IACA,CAAC5C,GAAG,CAAClB,UAAU,IAAIsD,KAAK,CAACS,aAAa,KAAK7C,GAAG,CAAClB,UAAU,GAAGsD,KAAK,CAACS,aAAa,CAAC;EAClF;EACA7C,GAAG,CAACvB,IAAI,GAAG0D,KAAK,CAAC1D,IAAI;EACrBuB,GAAG,CAACtB,IAAI,GAAGyD,KAAK,CAACzD,IAAI;EACrBjB,IAAI,CAAC0E,KAAK,CAACzD,IAAI,EAAE,UAAUqB,QAAQ,EAAE;IACnCyC,oBAAoB,CAACzC,QAAQ,EAAEA,QAAQ,CAAC;EAC1C,CAAC,CAAC;EACF,OAAOC,GAAG;AACZ;AACA,SAASwC,oBAAoBA,CAACxC,GAAG,EAAED,QAAQ,EAAE;EAC3C,IAAI,CAACA,QAAQ,EAAE;IACb;EACF;EACArC,MAAM,CAACqC,QAAQ,EAAE,MAAM,CAAC,KAAKC,GAAG,CAACpB,QAAQ,GAAGmB,QAAQ,CAACpB,IAAI,CAAC;EAC1DjB,MAAM,CAACqC,QAAQ,EAAE,QAAQ,CAAC,KAAKC,GAAG,CAAClB,UAAU,GAAGiB,QAAQ,CAACpB,IAAI,CAAC;EAC9DjB,MAAM,CAACqC,QAAQ,EAAE,WAAW,CAAC,KAAKC,GAAG,CAACI,eAAe,GAAGL,QAAQ,CAACI,SAAS,CAAC;EAC3EzC,MAAM,CAACqC,QAAQ,EAAE,MAAM,CAAC,KAAKC,GAAG,CAACC,IAAI,GAAGF,QAAQ,CAACE,IAAI,CAAC;EACtDvC,MAAM,CAACqC,QAAQ,EAAE,WAAW,CAAC,KAAKC,GAAG,CAACf,SAAS,GAAGc,QAAQ,CAACd,SAAS,CAAC;EACrEvB,MAAM,CAACqC,QAAQ,EAAE,YAAY,CAAC,KAAKC,GAAG,CAACd,UAAU,GAAGa,QAAQ,CAACb,UAAU,CAAC;EACxExB,MAAM,CAACqC,QAAQ,EAAE,UAAU,CAAC,KAAKC,GAAG,CAAChB,QAAQ,GAAGe,QAAQ,CAACf,QAAQ,CAAC;EAClEtB,MAAM,CAACqC,QAAQ,EAAE,YAAY,CAAC,KAAKC,GAAG,CAACjB,UAAU,GAAGgB,QAAQ,CAAChB,UAAU,CAAC;EACxErB,MAAM,CAACqC,QAAQ,EAAE,OAAO,CAAC,KAAKC,GAAG,CAACM,SAAS,GAAGP,QAAQ,CAACM,KAAK,CAAC;EAC7D3C,MAAM,CAACqC,QAAQ,EAAE,eAAe,CAAC,KAAKC,GAAG,CAACQ,iBAAiB,GAAGT,QAAQ,CAACQ,aAAa,CAAC;EACrF7C,MAAM,CAACqC,QAAQ,EAAE,YAAY,CAAC,KAAKC,GAAG,CAACU,cAAc,GAAGX,QAAQ,CAACU,UAAU,CAAC;EAC5E/C,MAAM,CAACqC,QAAQ,EAAE,OAAO,CAAC,KAAKC,GAAG,CAACY,SAAS,GAAGb,QAAQ,CAACY,KAAK,CAAC;EAC7DjD,MAAM,CAACqC,QAAQ,EAAE,QAAQ,CAAC,KAAKC,GAAG,CAACc,UAAU,GAAGf,QAAQ,CAACc,MAAM,CAAC;EAChEnD,MAAM,CAACqC,QAAQ,EAAE,iBAAiB,CAAC,KAAKC,GAAG,CAACgB,mBAAmB,GAAGjB,QAAQ,CAACgB,eAAe,CAAC;EAC3FrD,MAAM,CAACqC,QAAQ,EAAE,SAAS,CAAC,KAAKC,GAAG,CAACkB,WAAW,GAAGnB,QAAQ,CAACkB,OAAO,CAAC;EACnEvD,MAAM,CAACqC,QAAQ,EAAE,aAAa,CAAC,KAAKC,GAAG,CAACoB,eAAe,GAAGrB,QAAQ,CAACoB,WAAW,CAAC;EAC/EzD,MAAM,CAACqC,QAAQ,EAAE,aAAa,CAAC,KAAKC,GAAG,CAACsB,eAAe,GAAGvB,QAAQ,CAACsB,WAAW,CAAC;EAC/E3D,MAAM,CAACqC,QAAQ,EAAE,cAAc,CAAC,KAAKC,GAAG,CAACwB,gBAAgB,GAAGzB,QAAQ,CAACwB,YAAY,CAAC;EAClF7D,MAAM,CAACqC,QAAQ,EAAE,aAAa,CAAC,KAAKC,GAAG,CAAC0B,kBAAkB,GAAG3B,QAAQ,CAAC0B,WAAW,CAAC;EAClF/D,MAAM,CAACqC,QAAQ,EAAE,YAAY,CAAC,KAAKC,GAAG,CAAC4B,iBAAiB,GAAG7B,QAAQ,CAAC4B,UAAU,CAAC;EAC/EjE,MAAM,CAACqC,QAAQ,EAAE,eAAe,CAAC,KAAKC,GAAG,CAAC8B,oBAAoB,GAAG/B,QAAQ,CAAC8B,aAAa,CAAC;EACxFnE,MAAM,CAACqC,QAAQ,EAAE,eAAe,CAAC,KAAKC,GAAG,CAACgC,oBAAoB,GAAGjC,QAAQ,CAACgC,aAAa,CAAC;EACxFrE,MAAM,CAACqC,QAAQ,EAAE,iBAAiB,CAAC,KAAKC,GAAG,CAAC8C,eAAe,GAAG/C,QAAQ,CAAC+C,eAAe,CAAC;EACvFpF,MAAM,CAACqC,QAAQ,EAAE,gBAAgB,CAAC,KAAKC,GAAG,CAAC+C,cAAc,GAAGhD,QAAQ,CAACgD,cAAc,CAAC;EACpFrF,MAAM,CAACqC,QAAQ,EAAE,mBAAmB,CAAC,KAAKC,GAAG,CAACgD,iBAAiB,GAAGjD,QAAQ,CAACiD,iBAAiB,CAAC;EAC7FtF,MAAM,CAACqC,QAAQ,EAAE,mBAAmB,CAAC,KAAKC,GAAG,CAACiD,iBAAiB,GAAGlD,QAAQ,CAACkD,iBAAiB,CAAC;AAC/F;AACA,OAAO,SAASC,cAAcA,CAACC,UAAU,EAAEC,eAAe,EAAE;EAC1D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIC,GAAG,GAAGL,UAAU,GAAG,KAAK,GAAGC,eAAe;IAC9C,IAAI,CAACzF,cAAc,CAAC6F,GAAG,CAAC,EAAE;MACxBC,OAAO,CAACC,IAAI,CAAC,0BAA0B,GAAGP,UAAU,GAAG,0BAA0B,GAAGC,eAAe,CAAC;MACpGzF,cAAc,CAAC6F,GAAG,CAAC,GAAG,IAAI;IAC5B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}