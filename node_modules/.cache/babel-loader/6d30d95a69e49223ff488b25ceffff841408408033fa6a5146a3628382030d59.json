{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, isString } from 'zrender/lib/core/util.js';\nexport default function categoryVisual(ecModel) {\n  var paletteScope = {};\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var categoriesData = seriesModel.getCategoriesData();\n    var data = seriesModel.getData();\n    var categoryNameIdxMap = {};\n    categoriesData.each(function (idx) {\n      var name = categoriesData.getName(idx);\n      // Add prefix to avoid conflict with Object.prototype.\n      categoryNameIdxMap['ec-' + name] = idx;\n      var itemModel = categoriesData.getItemModel(idx);\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      if (!style.fill) {\n        // Get color from palette.\n        style.fill = seriesModel.getColorFromPalette(name, paletteScope);\n      }\n      categoriesData.setItemVisual(idx, 'style', style);\n      var symbolVisualList = ['symbol', 'symbolSize', 'symbolKeepAspect'];\n      for (var i = 0; i < symbolVisualList.length; i++) {\n        var symbolVisual = itemModel.getShallow(symbolVisualList[i], true);\n        if (symbolVisual != null) {\n          categoriesData.setItemVisual(idx, symbolVisualList[i], symbolVisual);\n        }\n      }\n    });\n    // Assign category color to visual\n    if (categoriesData.count()) {\n      data.each(function (idx) {\n        var model = data.getItemModel(idx);\n        var categoryIdx = model.getShallow('category');\n        if (categoryIdx != null) {\n          if (isString(categoryIdx)) {\n            categoryIdx = categoryNameIdxMap['ec-' + categoryIdx];\n          }\n          var categoryStyle = categoriesData.getItemVisual(categoryIdx, 'style');\n          var style = data.ensureUniqueItemVisual(idx, 'style');\n          extend(style, categoryStyle);\n          var visualList = ['symbol', 'symbolSize', 'symbolKeepAspect'];\n          for (var i = 0; i < visualList.length; i++) {\n            data.setItemVisual(idx, visualList[i], categoriesData.getItemVisual(categoryIdx, visualList[i]));\n          }\n        }\n      });\n    }\n  });\n}", "map": {"version": 3, "names": ["extend", "isString", "categoryVisual", "ecModel", "paletteScope", "eachSeriesByType", "seriesModel", "categoriesData", "getCategoriesData", "data", "getData", "categoryNameIdxMap", "each", "idx", "name", "getName", "itemModel", "getItemModel", "style", "getModel", "getItemStyle", "fill", "getColorFromPalette", "setItemVisual", "symbolVisualList", "i", "length", "symbolVisual", "getShallow", "count", "model", "categoryIdx", "categoryStyle", "getItemVisual", "ensureUniqueItemVisual", "visualList"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/chart/graph/categoryVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { extend, isString } from 'zrender/lib/core/util.js';\nexport default function categoryVisual(ecModel) {\n  var paletteScope = {};\n  ecModel.eachSeriesByType('graph', function (seriesModel) {\n    var categoriesData = seriesModel.getCategoriesData();\n    var data = seriesModel.getData();\n    var categoryNameIdxMap = {};\n    categoriesData.each(function (idx) {\n      var name = categoriesData.getName(idx);\n      // Add prefix to avoid conflict with Object.prototype.\n      categoryNameIdxMap['ec-' + name] = idx;\n      var itemModel = categoriesData.getItemModel(idx);\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      if (!style.fill) {\n        // Get color from palette.\n        style.fill = seriesModel.getColorFromPalette(name, paletteScope);\n      }\n      categoriesData.setItemVisual(idx, 'style', style);\n      var symbolVisualList = ['symbol', 'symbolSize', 'symbolKeepAspect'];\n      for (var i = 0; i < symbolVisualList.length; i++) {\n        var symbolVisual = itemModel.getShallow(symbolVisualList[i], true);\n        if (symbolVisual != null) {\n          categoriesData.setItemVisual(idx, symbolVisualList[i], symbolVisual);\n        }\n      }\n    });\n    // Assign category color to visual\n    if (categoriesData.count()) {\n      data.each(function (idx) {\n        var model = data.getItemModel(idx);\n        var categoryIdx = model.getShallow('category');\n        if (categoryIdx != null) {\n          if (isString(categoryIdx)) {\n            categoryIdx = categoryNameIdxMap['ec-' + categoryIdx];\n          }\n          var categoryStyle = categoriesData.getItemVisual(categoryIdx, 'style');\n          var style = data.ensureUniqueItemVisual(idx, 'style');\n          extend(style, categoryStyle);\n          var visualList = ['symbol', 'symbolSize', 'symbolKeepAspect'];\n          for (var i = 0; i < visualList.length; i++) {\n            data.setItemVisual(idx, visualList[i], categoriesData.getItemVisual(categoryIdx, visualList[i]));\n          }\n        }\n      });\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,0BAA0B;AAC3D,eAAe,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC9C,IAAIC,YAAY,GAAG,CAAC,CAAC;EACrBD,OAAO,CAACE,gBAAgB,CAAC,OAAO,EAAE,UAAUC,WAAW,EAAE;IACvD,IAAIC,cAAc,GAAGD,WAAW,CAACE,iBAAiB,CAAC,CAAC;IACpD,IAAIC,IAAI,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC;IAChC,IAAIC,kBAAkB,GAAG,CAAC,CAAC;IAC3BJ,cAAc,CAACK,IAAI,CAAC,UAAUC,GAAG,EAAE;MACjC,IAAIC,IAAI,GAAGP,cAAc,CAACQ,OAAO,CAACF,GAAG,CAAC;MACtC;MACAF,kBAAkB,CAAC,KAAK,GAAGG,IAAI,CAAC,GAAGD,GAAG;MACtC,IAAIG,SAAS,GAAGT,cAAc,CAACU,YAAY,CAACJ,GAAG,CAAC;MAChD,IAAIK,KAAK,GAAGF,SAAS,CAACG,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;MAC1D,IAAI,CAACF,KAAK,CAACG,IAAI,EAAE;QACf;QACAH,KAAK,CAACG,IAAI,GAAGf,WAAW,CAACgB,mBAAmB,CAACR,IAAI,EAAEV,YAAY,CAAC;MAClE;MACAG,cAAc,CAACgB,aAAa,CAACV,GAAG,EAAE,OAAO,EAAEK,KAAK,CAAC;MACjD,IAAIM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,kBAAkB,CAAC;MACnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAChD,IAAIE,YAAY,GAAGX,SAAS,CAACY,UAAU,CAACJ,gBAAgB,CAACC,CAAC,CAAC,EAAE,IAAI,CAAC;QAClE,IAAIE,YAAY,IAAI,IAAI,EAAE;UACxBpB,cAAc,CAACgB,aAAa,CAACV,GAAG,EAAEW,gBAAgB,CAACC,CAAC,CAAC,EAAEE,YAAY,CAAC;QACtE;MACF;IACF,CAAC,CAAC;IACF;IACA,IAAIpB,cAAc,CAACsB,KAAK,CAAC,CAAC,EAAE;MAC1BpB,IAAI,CAACG,IAAI,CAAC,UAAUC,GAAG,EAAE;QACvB,IAAIiB,KAAK,GAAGrB,IAAI,CAACQ,YAAY,CAACJ,GAAG,CAAC;QAClC,IAAIkB,WAAW,GAAGD,KAAK,CAACF,UAAU,CAAC,UAAU,CAAC;QAC9C,IAAIG,WAAW,IAAI,IAAI,EAAE;UACvB,IAAI9B,QAAQ,CAAC8B,WAAW,CAAC,EAAE;YACzBA,WAAW,GAAGpB,kBAAkB,CAAC,KAAK,GAAGoB,WAAW,CAAC;UACvD;UACA,IAAIC,aAAa,GAAGzB,cAAc,CAAC0B,aAAa,CAACF,WAAW,EAAE,OAAO,CAAC;UACtE,IAAIb,KAAK,GAAGT,IAAI,CAACyB,sBAAsB,CAACrB,GAAG,EAAE,OAAO,CAAC;UACrDb,MAAM,CAACkB,KAAK,EAAEc,aAAa,CAAC;UAC5B,IAAIG,UAAU,GAAG,CAAC,QAAQ,EAAE,YAAY,EAAE,kBAAkB,CAAC;UAC7D,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,UAAU,CAACT,MAAM,EAAED,CAAC,EAAE,EAAE;YAC1ChB,IAAI,CAACc,aAAa,CAACV,GAAG,EAAEsB,UAAU,CAACV,CAAC,CAAC,EAAElB,cAAc,CAAC0B,aAAa,CAACF,WAAW,EAAEI,UAAU,CAACV,CAAC,CAAC,CAAC,CAAC;UAClG;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}