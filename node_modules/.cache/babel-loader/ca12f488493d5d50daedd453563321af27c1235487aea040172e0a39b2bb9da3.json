{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function ariaPreprocessor(option) {\n  if (!option || !option.aria) {\n    return;\n  }\n  var aria = option.aria;\n  // aria.show is deprecated and should use aria.enabled instead\n  if (aria.show != null) {\n    aria.enabled = aria.show;\n  }\n  aria.label = aria.label || {};\n  // move description, general, series, data to be under aria.label\n  zrUtil.each(['description', 'general', 'series', 'data'], function (name) {\n    if (aria[name] != null) {\n      aria.label[name] = aria[name];\n    }\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "ariaPreprocessor", "option", "aria", "show", "enabled", "label", "each", "name"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/aria/preprocessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function ariaPreprocessor(option) {\n  if (!option || !option.aria) {\n    return;\n  }\n  var aria = option.aria;\n  // aria.show is deprecated and should use aria.enabled instead\n  if (aria.show != null) {\n    aria.enabled = aria.show;\n  }\n  aria.label = aria.label || {};\n  // move description, general, series, data to be under aria.label\n  zrUtil.each(['description', 'general', 'series', 'data'], function (name) {\n    if (aria[name] != null) {\n      aria.label[name] = aria[name];\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,eAAe,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC/C,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;IAC3B;EACF;EACA,IAAIA,IAAI,GAAGD,MAAM,CAACC,IAAI;EACtB;EACA,IAAIA,IAAI,CAACC,IAAI,IAAI,IAAI,EAAE;IACrBD,IAAI,CAACE,OAAO,GAAGF,IAAI,CAACC,IAAI;EAC1B;EACAD,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;EAC7B;EACAN,MAAM,CAACO,IAAI,CAAC,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,UAAUC,IAAI,EAAE;IACxE,IAAIL,IAAI,CAACK,IAAI,CAAC,IAAI,IAAI,EAAE;MACtBL,IAAI,CAACG,KAAK,CAACE,IAAI,CAAC,GAAGL,IAAI,CAACK,IAAI,CAAC;IAC/B;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}