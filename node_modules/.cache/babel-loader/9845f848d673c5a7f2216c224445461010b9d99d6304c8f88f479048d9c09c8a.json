{"ast": null, "code": "import easingFuncs from './easing.js';\nimport { isFunction, noop } from '../core/util.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nvar Clip = function () {\n  function Clip(opts) {\n    this._inited = false;\n    this._startTime = 0;\n    this._pausedTime = 0;\n    this._paused = false;\n    this._life = opts.life || 1000;\n    this._delay = opts.delay || 0;\n    this.loop = opts.loop || false;\n    this.onframe = opts.onframe || noop;\n    this.ondestroy = opts.ondestroy || noop;\n    this.onrestart = opts.onrestart || noop;\n    opts.easing && this.setEasing(opts.easing);\n  }\n  Clip.prototype.step = function (globalTime, deltaTime) {\n    if (!this._inited) {\n      this._startTime = globalTime + this._delay;\n      this._inited = true;\n    }\n    if (this._paused) {\n      this._pausedTime += deltaTime;\n      return;\n    }\n    var life = this._life;\n    var elapsedTime = globalTime - this._startTime - this._pausedTime;\n    var percent = elapsedTime / life;\n    if (percent < 0) {\n      percent = 0;\n    }\n    percent = Math.min(percent, 1);\n    var easingFunc = this.easingFunc;\n    var schedule = easingFunc ? easingFunc(percent) : percent;\n    this.onframe(schedule);\n    if (percent === 1) {\n      if (this.loop) {\n        var remainder = elapsedTime % life;\n        this._startTime = globalTime - remainder;\n        this._pausedTime = 0;\n        this.onrestart();\n      } else {\n        return true;\n      }\n    }\n    return false;\n  };\n  Clip.prototype.pause = function () {\n    this._paused = true;\n  };\n  Clip.prototype.resume = function () {\n    this._paused = false;\n  };\n  Clip.prototype.setEasing = function (easing) {\n    this.easing = easing;\n    this.easingFunc = isFunction(easing) ? easing : easingFuncs[easing] || createCubicEasingFunc(easing);\n  };\n  return Clip;\n}();\nexport default Clip;", "map": {"version": 3, "names": ["easingFuncs", "isFunction", "noop", "createCubicEasingFunc", "Clip", "opts", "_inited", "_startTime", "_pausedTime", "_paused", "_life", "life", "_delay", "delay", "loop", "onframe", "ondestroy", "<PERSON><PERSON><PERSON>", "easing", "setEasing", "prototype", "step", "globalTime", "deltaTime", "elapsedTime", "percent", "Math", "min", "easingFunc", "schedule", "remainder", "pause", "resume"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/zrender/lib/animation/Clip.js"], "sourcesContent": ["import easingFuncs from './easing.js';\nimport { isFunction, noop } from '../core/util.js';\nimport { createCubicEasingFunc } from './cubicEasing.js';\nvar Clip = (function () {\n    function Clip(opts) {\n        this._inited = false;\n        this._startTime = 0;\n        this._pausedTime = 0;\n        this._paused = false;\n        this._life = opts.life || 1000;\n        this._delay = opts.delay || 0;\n        this.loop = opts.loop || false;\n        this.onframe = opts.onframe || noop;\n        this.ondestroy = opts.ondestroy || noop;\n        this.onrestart = opts.onrestart || noop;\n        opts.easing && this.setEasing(opts.easing);\n    }\n    Clip.prototype.step = function (globalTime, deltaTime) {\n        if (!this._inited) {\n            this._startTime = globalTime + this._delay;\n            this._inited = true;\n        }\n        if (this._paused) {\n            this._pausedTime += deltaTime;\n            return;\n        }\n        var life = this._life;\n        var elapsedTime = globalTime - this._startTime - this._pausedTime;\n        var percent = elapsedTime / life;\n        if (percent < 0) {\n            percent = 0;\n        }\n        percent = Math.min(percent, 1);\n        var easingFunc = this.easingFunc;\n        var schedule = easingFunc ? easingFunc(percent) : percent;\n        this.onframe(schedule);\n        if (percent === 1) {\n            if (this.loop) {\n                var remainder = elapsedTime % life;\n                this._startTime = globalTime - remainder;\n                this._pausedTime = 0;\n                this.onrestart();\n            }\n            else {\n                return true;\n            }\n        }\n        return false;\n    };\n    Clip.prototype.pause = function () {\n        this._paused = true;\n    };\n    Clip.prototype.resume = function () {\n        this._paused = false;\n    };\n    Clip.prototype.setEasing = function (easing) {\n        this.easing = easing;\n        this.easingFunc = isFunction(easing)\n            ? easing\n            : easingFuncs[easing] || createCubicEasingFunc(easing);\n    };\n    return Clip;\n}());\nexport default Clip;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,aAAa;AACrC,SAASC,UAAU,EAAEC,IAAI,QAAQ,iBAAiB;AAClD,SAASC,qBAAqB,QAAQ,kBAAkB;AACxD,IAAIC,IAAI,GAAI,YAAY;EACpB,SAASA,IAAIA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,KAAK,GAAGL,IAAI,CAACM,IAAI,IAAI,IAAI;IAC9B,IAAI,CAACC,MAAM,GAAGP,IAAI,CAACQ,KAAK,IAAI,CAAC;IAC7B,IAAI,CAACC,IAAI,GAAGT,IAAI,CAACS,IAAI,IAAI,KAAK;IAC9B,IAAI,CAACC,OAAO,GAAGV,IAAI,CAACU,OAAO,IAAIb,IAAI;IACnC,IAAI,CAACc,SAAS,GAAGX,IAAI,CAACW,SAAS,IAAId,IAAI;IACvC,IAAI,CAACe,SAAS,GAAGZ,IAAI,CAACY,SAAS,IAAIf,IAAI;IACvCG,IAAI,CAACa,MAAM,IAAI,IAAI,CAACC,SAAS,CAACd,IAAI,CAACa,MAAM,CAAC;EAC9C;EACAd,IAAI,CAACgB,SAAS,CAACC,IAAI,GAAG,UAAUC,UAAU,EAAEC,SAAS,EAAE;IACnD,IAAI,CAAC,IAAI,CAACjB,OAAO,EAAE;MACf,IAAI,CAACC,UAAU,GAAGe,UAAU,GAAG,IAAI,CAACV,MAAM;MAC1C,IAAI,CAACN,OAAO,GAAG,IAAI;IACvB;IACA,IAAI,IAAI,CAACG,OAAO,EAAE;MACd,IAAI,CAACD,WAAW,IAAIe,SAAS;MAC7B;IACJ;IACA,IAAIZ,IAAI,GAAG,IAAI,CAACD,KAAK;IACrB,IAAIc,WAAW,GAAGF,UAAU,GAAG,IAAI,CAACf,UAAU,GAAG,IAAI,CAACC,WAAW;IACjE,IAAIiB,OAAO,GAAGD,WAAW,GAAGb,IAAI;IAChC,IAAIc,OAAO,GAAG,CAAC,EAAE;MACbA,OAAO,GAAG,CAAC;IACf;IACAA,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACF,OAAO,EAAE,CAAC,CAAC;IAC9B,IAAIG,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIC,QAAQ,GAAGD,UAAU,GAAGA,UAAU,CAACH,OAAO,CAAC,GAAGA,OAAO;IACzD,IAAI,CAACV,OAAO,CAACc,QAAQ,CAAC;IACtB,IAAIJ,OAAO,KAAK,CAAC,EAAE;MACf,IAAI,IAAI,CAACX,IAAI,EAAE;QACX,IAAIgB,SAAS,GAAGN,WAAW,GAAGb,IAAI;QAClC,IAAI,CAACJ,UAAU,GAAGe,UAAU,GAAGQ,SAAS;QACxC,IAAI,CAACtB,WAAW,GAAG,CAAC;QACpB,IAAI,CAACS,SAAS,CAAC,CAAC;MACpB,CAAC,MACI;QACD,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACDb,IAAI,CAACgB,SAAS,CAACW,KAAK,GAAG,YAAY;IAC/B,IAAI,CAACtB,OAAO,GAAG,IAAI;EACvB,CAAC;EACDL,IAAI,CAACgB,SAAS,CAACY,MAAM,GAAG,YAAY;IAChC,IAAI,CAACvB,OAAO,GAAG,KAAK;EACxB,CAAC;EACDL,IAAI,CAACgB,SAAS,CAACD,SAAS,GAAG,UAAUD,MAAM,EAAE;IACzC,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACU,UAAU,GAAG3B,UAAU,CAACiB,MAAM,CAAC,GAC9BA,MAAM,GACNlB,WAAW,CAACkB,MAAM,CAAC,IAAIf,qBAAqB,CAACe,MAAM,CAAC;EAC9D,CAAC;EACD,OAAOd,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,eAAeA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}