{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as globalListener from './globalListener.js';\nimport ComponentView from '../../view/Component.js';\nvar AxisPointerView = /** @class */function (_super) {\n  __extends(AxisPointerView, _super);\n  function AxisPointerView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisPointerView.type;\n    return _this;\n  }\n  AxisPointerView.prototype.render = function (globalAxisPointerModel, ecModel, api) {\n    var globalTooltipModel = ecModel.getComponent('tooltip');\n    var triggerOn = globalAxisPointerModel.get('triggerOn') || globalTooltipModel && globalTooltipModel.get('triggerOn') || 'mousemove|click';\n    // Register global listener in AxisPointerView to enable\n    // AxisPointerView to be independent to Tooltip.\n    globalListener.register('axisPointer', api, function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none' && (currTrigger === 'leave' || triggerOn.indexOf(currTrigger) >= 0)) {\n        dispatchAction({\n          type: 'updateAxisPointer',\n          currTrigger: currTrigger,\n          x: e && e.offsetX,\n          y: e && e.offsetY\n        });\n      }\n    });\n  };\n  AxisPointerView.prototype.remove = function (ecModel, api) {\n    globalListener.unregister('axisPointer', api);\n  };\n  AxisPointerView.prototype.dispose = function (ecModel, api) {\n    globalListener.unregister('axisPointer', api);\n  };\n  AxisPointerView.type = 'axisPointer';\n  return AxisPointerView;\n}(ComponentView);\nexport default AxisPointerView;", "map": {"version": 3, "names": ["__extends", "globalListener", "ComponentView", "AxisPointerView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "globalAxisPointerModel", "ecModel", "api", "globalTooltipModel", "getComponent", "triggerOn", "get", "register", "currTrigger", "e", "dispatchAction", "indexOf", "x", "offsetX", "y", "offsetY", "remove", "unregister", "dispose"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/component/axisPointer/AxisPointerView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as globalListener from './globalListener.js';\nimport ComponentView from '../../view/Component.js';\nvar AxisPointerView = /** @class */function (_super) {\n  __extends(AxisPointerView, _super);\n  function AxisPointerView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisPointerView.type;\n    return _this;\n  }\n  AxisPointerView.prototype.render = function (globalAxisPointerModel, ecModel, api) {\n    var globalTooltipModel = ecModel.getComponent('tooltip');\n    var triggerOn = globalAxisPointerModel.get('triggerOn') || globalTooltipModel && globalTooltipModel.get('triggerOn') || 'mousemove|click';\n    // Register global listener in AxisPointerView to enable\n    // AxisPointerView to be independent to Tooltip.\n    globalListener.register('axisPointer', api, function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none' && (currTrigger === 'leave' || triggerOn.indexOf(currTrigger) >= 0)) {\n        dispatchAction({\n          type: 'updateAxisPointer',\n          currTrigger: currTrigger,\n          x: e && e.offsetX,\n          y: e && e.offsetY\n        });\n      }\n    });\n  };\n  AxisPointerView.prototype.remove = function (ecModel, api) {\n    globalListener.unregister('axisPointer', api);\n  };\n  AxisPointerView.prototype.dispose = function (ecModel, api) {\n    globalListener.unregister('axisPointer', api);\n  };\n  AxisPointerView.type = 'axisPointer';\n  return AxisPointerView;\n}(ComponentView);\nexport default AxisPointerView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,cAAc,MAAM,qBAAqB;AACrD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDJ,SAAS,CAACG,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjC,OAAOH,KAAK;EACd;EACAF,eAAe,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,sBAAsB,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACjF,IAAIC,kBAAkB,GAAGF,OAAO,CAACG,YAAY,CAAC,SAAS,CAAC;IACxD,IAAIC,SAAS,GAAGL,sBAAsB,CAACM,GAAG,CAAC,WAAW,CAAC,IAAIH,kBAAkB,IAAIA,kBAAkB,CAACG,GAAG,CAAC,WAAW,CAAC,IAAI,iBAAiB;IACzI;IACA;IACAhB,cAAc,CAACiB,QAAQ,CAAC,aAAa,EAAEL,GAAG,EAAE,UAAUM,WAAW,EAAEC,CAAC,EAAEC,cAAc,EAAE;MACpF;MACA,IAAIL,SAAS,KAAK,MAAM,KAAKG,WAAW,KAAK,OAAO,IAAIH,SAAS,CAACM,OAAO,CAACH,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE;QAC5FE,cAAc,CAAC;UACbb,IAAI,EAAE,mBAAmB;UACzBW,WAAW,EAAEA,WAAW;UACxBI,CAAC,EAAEH,CAAC,IAAIA,CAAC,CAACI,OAAO;UACjBC,CAAC,EAAEL,CAAC,IAAIA,CAAC,CAACM;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EACDvB,eAAe,CAACM,SAAS,CAACkB,MAAM,GAAG,UAAUf,OAAO,EAAEC,GAAG,EAAE;IACzDZ,cAAc,CAAC2B,UAAU,CAAC,aAAa,EAAEf,GAAG,CAAC;EAC/C,CAAC;EACDV,eAAe,CAACM,SAAS,CAACoB,OAAO,GAAG,UAAUjB,OAAO,EAAEC,GAAG,EAAE;IAC1DZ,cAAc,CAAC2B,UAAU,CAAC,aAAa,EAAEf,GAAG,CAAC;EAC/C,CAAC;EACDV,eAAe,CAACK,IAAI,GAAG,aAAa;EACpC,OAAOL,eAAe;AACxB,CAAC,CAACD,aAAa,CAAC;AAChB,eAAeC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}