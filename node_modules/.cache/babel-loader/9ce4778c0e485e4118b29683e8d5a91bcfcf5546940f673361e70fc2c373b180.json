{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport { parsePercent, isNumeric } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nvar BAR_BORDER_WIDTH_QUERY = ['itemStyle', 'borderWidth'];\n// index: +isHorizontal\nvar LAYOUT_ATTRS = [{\n  xy: 'x',\n  wh: 'width',\n  index: 0,\n  posDesc: ['left', 'right']\n}, {\n  xy: 'y',\n  wh: 'height',\n  index: 1,\n  posDesc: ['top', 'bottom']\n}];\nvar pathForLineWidth = new graphic.Circle();\nvar PictorialBarView = /** @class */function (_super) {\n  __extends(PictorialBarView, _super);\n  function PictorialBarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PictorialBarView.type;\n    return _this;\n  }\n  PictorialBarView.prototype.render = function (seriesModel, ecModel, api) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var coordSysRect = cartesian.master.getRect();\n    var opt = {\n      ecSize: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      seriesModel: seriesModel,\n      coordSys: cartesian,\n      coordSysExtent: [[coordSysRect.x, coordSysRect.x + coordSysRect.width], [coordSysRect.y, coordSysRect.y + coordSysRect.height]],\n      isHorizontal: isHorizontal,\n      valueDim: LAYOUT_ATTRS[+isHorizontal],\n      categoryDim: LAYOUT_ATTRS[1 - +isHorizontal]\n    };\n    data.diff(oldData).add(function (dataIndex) {\n      if (!data.hasValue(dataIndex)) {\n        return;\n      }\n      var itemModel = getItemModel(data, dataIndex);\n      var symbolMeta = getSymbolMeta(data, dataIndex, itemModel, opt);\n      var bar = createBar(data, opt, symbolMeta);\n      data.setItemGraphicEl(dataIndex, bar);\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).update(function (newIndex, oldIndex) {\n      var bar = oldData.getItemGraphicEl(oldIndex);\n      if (!data.hasValue(newIndex)) {\n        group.remove(bar);\n        return;\n      }\n      var itemModel = getItemModel(data, newIndex);\n      var symbolMeta = getSymbolMeta(data, newIndex, itemModel, opt);\n      var pictorialShapeStr = getShapeStr(data, symbolMeta);\n      if (bar && pictorialShapeStr !== bar.__pictorialShapeStr) {\n        group.remove(bar);\n        data.setItemGraphicEl(newIndex, null);\n        bar = null;\n      }\n      if (bar) {\n        updateBar(bar, opt, symbolMeta);\n      } else {\n        bar = createBar(data, opt, symbolMeta, true);\n      }\n      data.setItemGraphicEl(newIndex, bar);\n      bar.__pictorialSymbolMeta = symbolMeta;\n      // Add back\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).remove(function (dataIndex) {\n      var bar = oldData.getItemGraphicEl(dataIndex);\n      bar && removeBar(oldData, dataIndex, bar.__pictorialSymbolMeta.animationModel, bar);\n    }).execute();\n    // Do clipping\n    var clipPath = seriesModel.get('clip', true) ? createClipPath(seriesModel.coordinateSystem, false, seriesModel) : null;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n    this._data = data;\n    return this.group;\n  };\n  PictorialBarView.prototype.remove = function (ecModel, api) {\n    var group = this.group;\n    var data = this._data;\n    if (ecModel.get('animation')) {\n      if (data) {\n        data.eachItemGraphicEl(function (bar) {\n          removeBar(data, getECData(bar).dataIndex, ecModel, bar);\n        });\n      }\n    } else {\n      group.removeAll();\n    }\n  };\n  PictorialBarView.type = 'pictorialBar';\n  return PictorialBarView;\n}(ChartView);\n// Set or calculate default value about symbol, and calculate layout info.\nfunction getSymbolMeta(data, dataIndex, itemModel, opt) {\n  var layout = data.getItemLayout(dataIndex);\n  var symbolRepeat = itemModel.get('symbolRepeat');\n  var symbolClip = itemModel.get('symbolClip');\n  var symbolPosition = itemModel.get('symbolPosition') || 'start';\n  var symbolRotate = itemModel.get('symbolRotate');\n  var rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n  var symbolPatternSize = itemModel.get('symbolPatternSize') || 2;\n  var isAnimationEnabled = itemModel.isAnimationEnabled();\n  var symbolMeta = {\n    dataIndex: dataIndex,\n    layout: layout,\n    itemModel: itemModel,\n    symbolType: data.getItemVisual(dataIndex, 'symbol') || 'circle',\n    style: data.getItemVisual(dataIndex, 'style'),\n    symbolClip: symbolClip,\n    symbolRepeat: symbolRepeat,\n    symbolRepeatDirection: itemModel.get('symbolRepeatDirection'),\n    symbolPatternSize: symbolPatternSize,\n    rotation: rotation,\n    animationModel: isAnimationEnabled ? itemModel : null,\n    hoverScale: isAnimationEnabled && itemModel.get(['emphasis', 'scale']),\n    z2: itemModel.getShallow('z', true) || 0\n  };\n  prepareBarLength(itemModel, symbolRepeat, layout, opt, symbolMeta);\n  prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, symbolMeta.boundingLength, symbolMeta.pxSign, symbolPatternSize, opt, symbolMeta);\n  prepareLineWidth(itemModel, symbolMeta.symbolScale, rotation, opt, symbolMeta);\n  var symbolSize = symbolMeta.symbolSize;\n  var symbolOffset = normalizeSymbolOffset(itemModel.get('symbolOffset'), symbolSize);\n  prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, symbolMeta.valueLineWidth, symbolMeta.boundingLength, symbolMeta.repeatCutLength, opt, symbolMeta);\n  return symbolMeta;\n}\n// bar length can be negative.\nfunction prepareBarLength(itemModel, symbolRepeat, layout, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var symbolBoundingData = itemModel.get('symbolBoundingData');\n  var valueAxis = opt.coordSys.getOtherAxis(opt.coordSys.getBaseAxis());\n  var zeroPx = valueAxis.toGlobalCoord(valueAxis.dataToCoord(0));\n  var pxSignIdx = 1 - +(layout[valueDim.wh] <= 0);\n  var boundingLength;\n  if (zrUtil.isArray(symbolBoundingData)) {\n    var symbolBoundingExtent = [convertToCoordOnAxis(valueAxis, symbolBoundingData[0]) - zeroPx, convertToCoordOnAxis(valueAxis, symbolBoundingData[1]) - zeroPx];\n    symbolBoundingExtent[1] < symbolBoundingExtent[0] && symbolBoundingExtent.reverse();\n    boundingLength = symbolBoundingExtent[pxSignIdx];\n  } else if (symbolBoundingData != null) {\n    boundingLength = convertToCoordOnAxis(valueAxis, symbolBoundingData) - zeroPx;\n  } else if (symbolRepeat) {\n    boundingLength = opt.coordSysExtent[valueDim.index][pxSignIdx] - zeroPx;\n  } else {\n    boundingLength = layout[valueDim.wh];\n  }\n  outputSymbolMeta.boundingLength = boundingLength;\n  if (symbolRepeat) {\n    outputSymbolMeta.repeatCutLength = layout[valueDim.wh];\n  }\n  // if 'pxSign' means sign of pixel,  it can't be zero, or symbolScale will be zero\n  // and when borderWidth be settled, the actual linewidth will be NaN\n  var isXAxis = valueDim.xy === 'x';\n  var isInverse = valueAxis.inverse;\n  outputSymbolMeta.pxSign = isXAxis && !isInverse || !isXAxis && isInverse ? boundingLength >= 0 ? 1 : -1 : boundingLength > 0 ? 1 : -1;\n}\nfunction convertToCoordOnAxis(axis, value) {\n  return axis.toGlobalCoord(axis.dataToCoord(axis.scale.parse(value)));\n}\n// Support ['100%', '100%']\nfunction prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, boundingLength, pxSign, symbolPatternSize, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var categoryDim = opt.categoryDim;\n  var categorySize = Math.abs(layout[categoryDim.wh]);\n  var symbolSize = data.getItemVisual(dataIndex, 'symbolSize');\n  var parsedSymbolSize;\n  if (zrUtil.isArray(symbolSize)) {\n    parsedSymbolSize = symbolSize.slice();\n  } else {\n    if (symbolSize == null) {\n      // will parse to number below\n      parsedSymbolSize = ['100%', '100%'];\n    } else {\n      parsedSymbolSize = [symbolSize, symbolSize];\n    }\n  }\n  // Note: percentage symbolSize (like '100%') do not consider lineWidth, because it is\n  // to complicated to calculate real percent value if considering scaled lineWidth.\n  // So the actual size will bigger than layout size if lineWidth is bigger than zero,\n  // which can be tolerated in pictorial chart.\n  parsedSymbolSize[categoryDim.index] = parsePercent(parsedSymbolSize[categoryDim.index], categorySize);\n  parsedSymbolSize[valueDim.index] = parsePercent(parsedSymbolSize[valueDim.index], symbolRepeat ? categorySize : Math.abs(boundingLength));\n  outputSymbolMeta.symbolSize = parsedSymbolSize;\n  // If x or y is less than zero, show reversed shape.\n  var symbolScale = outputSymbolMeta.symbolScale = [parsedSymbolSize[0] / symbolPatternSize, parsedSymbolSize[1] / symbolPatternSize];\n  // Follow convention, 'right' and 'top' is the normal scale.\n  symbolScale[valueDim.index] *= (opt.isHorizontal ? -1 : 1) * pxSign;\n}\nfunction prepareLineWidth(itemModel, symbolScale, rotation, opt, outputSymbolMeta) {\n  // In symbols are drawn with scale, so do not need to care about the case that width\n  // or height are too small. But symbol use strokeNoScale, where acture lineWidth should\n  // be calculated.\n  var valueLineWidth = itemModel.get(BAR_BORDER_WIDTH_QUERY) || 0;\n  if (valueLineWidth) {\n    pathForLineWidth.attr({\n      scaleX: symbolScale[0],\n      scaleY: symbolScale[1],\n      rotation: rotation\n    });\n    pathForLineWidth.updateTransform();\n    valueLineWidth /= pathForLineWidth.getLineScale();\n    valueLineWidth *= symbolScale[opt.valueDim.index];\n  }\n  outputSymbolMeta.valueLineWidth = valueLineWidth || 0;\n}\nfunction prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, valueLineWidth, boundingLength, repeatCutLength, opt, outputSymbolMeta) {\n  var categoryDim = opt.categoryDim;\n  var valueDim = opt.valueDim;\n  var pxSign = outputSymbolMeta.pxSign;\n  var unitLength = Math.max(symbolSize[valueDim.index] + valueLineWidth, 0);\n  var pathLen = unitLength;\n  // Note: rotation will not effect the layout of symbols, because user may\n  // want symbols to rotate on its center, which should not be translated\n  // when rotating.\n  if (symbolRepeat) {\n    var absBoundingLength = Math.abs(boundingLength);\n    var symbolMargin = zrUtil.retrieve(itemModel.get('symbolMargin'), '15%') + '';\n    var hasEndGap = false;\n    if (symbolMargin.lastIndexOf('!') === symbolMargin.length - 1) {\n      hasEndGap = true;\n      symbolMargin = symbolMargin.slice(0, symbolMargin.length - 1);\n    }\n    var symbolMarginNumeric = parsePercent(symbolMargin, symbolSize[valueDim.index]);\n    var uLenWithMargin = Math.max(unitLength + symbolMarginNumeric * 2, 0);\n    // When symbol margin is less than 0, margin at both ends will be subtracted\n    // to ensure that all of the symbols will not be overflow the given area.\n    var endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;\n    // Both final repeatTimes and final symbolMarginNumeric area calculated based on\n    // boundingLength.\n    var repeatSpecified = isNumeric(symbolRepeat);\n    var repeatTimes = repeatSpecified ? symbolRepeat : toIntTimes((absBoundingLength + endFix) / uLenWithMargin);\n    // Adjust calculate margin, to ensure each symbol is displayed\n    // entirely in the given layout area.\n    var mDiff = absBoundingLength - repeatTimes * unitLength;\n    symbolMarginNumeric = mDiff / 2 / (hasEndGap ? repeatTimes : Math.max(repeatTimes - 1, 1));\n    uLenWithMargin = unitLength + symbolMarginNumeric * 2;\n    endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;\n    // Update repeatTimes when not all symbol will be shown.\n    if (!repeatSpecified && symbolRepeat !== 'fixed') {\n      repeatTimes = repeatCutLength ? toIntTimes((Math.abs(repeatCutLength) + endFix) / uLenWithMargin) : 0;\n    }\n    pathLen = repeatTimes * uLenWithMargin - endFix;\n    outputSymbolMeta.repeatTimes = repeatTimes;\n    outputSymbolMeta.symbolMargin = symbolMarginNumeric;\n  }\n  var sizeFix = pxSign * (pathLen / 2);\n  var pathPosition = outputSymbolMeta.pathPosition = [];\n  pathPosition[categoryDim.index] = layout[categoryDim.wh] / 2;\n  pathPosition[valueDim.index] = symbolPosition === 'start' ? sizeFix : symbolPosition === 'end' ? boundingLength - sizeFix : boundingLength / 2; // 'center'\n  if (symbolOffset) {\n    pathPosition[0] += symbolOffset[0];\n    pathPosition[1] += symbolOffset[1];\n  }\n  var bundlePosition = outputSymbolMeta.bundlePosition = [];\n  bundlePosition[categoryDim.index] = layout[categoryDim.xy];\n  bundlePosition[valueDim.index] = layout[valueDim.xy];\n  var barRectShape = outputSymbolMeta.barRectShape = zrUtil.extend({}, layout);\n  barRectShape[valueDim.wh] = pxSign * Math.max(Math.abs(layout[valueDim.wh]), Math.abs(pathPosition[valueDim.index] + sizeFix));\n  barRectShape[categoryDim.wh] = layout[categoryDim.wh];\n  var clipShape = outputSymbolMeta.clipShape = {};\n  // Consider that symbol may be overflow layout rect.\n  clipShape[categoryDim.xy] = -layout[categoryDim.xy];\n  clipShape[categoryDim.wh] = opt.ecSize[categoryDim.wh];\n  clipShape[valueDim.xy] = 0;\n  clipShape[valueDim.wh] = layout[valueDim.wh];\n}\nfunction createPath(symbolMeta) {\n  var symbolPatternSize = symbolMeta.symbolPatternSize;\n  var path = createSymbol(\n  // Consider texture img, make a big size.\n  symbolMeta.symbolType, -symbolPatternSize / 2, -symbolPatternSize / 2, symbolPatternSize, symbolPatternSize);\n  path.attr({\n    culling: true\n  });\n  path.type !== 'image' && path.setStyle({\n    strokeNoScale: true\n  });\n  return path;\n}\nfunction createOrUpdateRepeatSymbols(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var symbolSize = symbolMeta.symbolSize;\n  var valueLineWidth = symbolMeta.valueLineWidth;\n  var pathPosition = symbolMeta.pathPosition;\n  var valueDim = opt.valueDim;\n  var repeatTimes = symbolMeta.repeatTimes || 0;\n  var index = 0;\n  var unit = symbolSize[opt.valueDim.index] + valueLineWidth + symbolMeta.symbolMargin * 2;\n  eachPath(bar, function (path) {\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    if (index < repeatTimes) {\n      updateAttr(path, null, makeTarget(index), symbolMeta, isUpdate);\n    } else {\n      updateAttr(path, null, {\n        scaleX: 0,\n        scaleY: 0\n      }, symbolMeta, isUpdate, function () {\n        bundle.remove(path);\n      });\n    }\n    // updateHoverAnimation(path, symbolMeta);\n    index++;\n  });\n  for (; index < repeatTimes; index++) {\n    var path = createPath(symbolMeta);\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    bundle.add(path);\n    var target = makeTarget(index);\n    updateAttr(path, {\n      x: target.x,\n      y: target.y,\n      scaleX: 0,\n      scaleY: 0\n    }, {\n      scaleX: target.scaleX,\n      scaleY: target.scaleY,\n      rotation: target.rotation\n    }, symbolMeta, isUpdate);\n  }\n  function makeTarget(index) {\n    var position = pathPosition.slice();\n    // (start && pxSign > 0) || (end && pxSign < 0): i = repeatTimes - index\n    // Otherwise: i = index;\n    var pxSign = symbolMeta.pxSign;\n    var i = index;\n    if (symbolMeta.symbolRepeatDirection === 'start' ? pxSign > 0 : pxSign < 0) {\n      i = repeatTimes - 1 - index;\n    }\n    position[valueDim.index] = unit * (i - repeatTimes / 2 + 0.5) + pathPosition[valueDim.index];\n    return {\n      x: position[0],\n      y: position[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    };\n  }\n}\nfunction createOrUpdateSingleSymbol(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var mainPath = bar.__pictorialMainPath;\n  if (!mainPath) {\n    mainPath = bar.__pictorialMainPath = createPath(symbolMeta);\n    bundle.add(mainPath);\n    updateAttr(mainPath, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: 0,\n      scaleY: 0,\n      rotation: symbolMeta.rotation\n    }, {\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1]\n    }, symbolMeta, isUpdate);\n  } else {\n    updateAttr(mainPath, null, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    }, symbolMeta, isUpdate);\n  }\n}\n// bar rect is used for label.\nfunction createOrUpdateBarRect(bar, symbolMeta, isUpdate) {\n  var rectShape = zrUtil.extend({}, symbolMeta.barRectShape);\n  var barRect = bar.__pictorialBarRect;\n  if (!barRect) {\n    barRect = bar.__pictorialBarRect = new graphic.Rect({\n      z2: 2,\n      shape: rectShape,\n      silent: true,\n      style: {\n        stroke: 'transparent',\n        fill: 'transparent',\n        lineWidth: 0\n      }\n    });\n    barRect.disableMorphing = true;\n    bar.add(barRect);\n  } else {\n    updateAttr(barRect, null, {\n      shape: rectShape\n    }, symbolMeta, isUpdate);\n  }\n}\nfunction createOrUpdateClip(bar, opt, symbolMeta, isUpdate) {\n  // If not clip, symbol will be remove and rebuilt.\n  if (symbolMeta.symbolClip) {\n    var clipPath = bar.__pictorialClipPath;\n    var clipShape = zrUtil.extend({}, symbolMeta.clipShape);\n    var valueDim = opt.valueDim;\n    var animationModel = symbolMeta.animationModel;\n    var dataIndex = symbolMeta.dataIndex;\n    if (clipPath) {\n      graphic.updateProps(clipPath, {\n        shape: clipShape\n      }, animationModel, dataIndex);\n    } else {\n      clipShape[valueDim.wh] = 0;\n      clipPath = new graphic.Rect({\n        shape: clipShape\n      });\n      bar.__pictorialBundle.setClipPath(clipPath);\n      bar.__pictorialClipPath = clipPath;\n      var target = {};\n      target[valueDim.wh] = symbolMeta.clipShape[valueDim.wh];\n      graphic[isUpdate ? 'updateProps' : 'initProps'](clipPath, {\n        shape: target\n      }, animationModel, dataIndex);\n    }\n  }\n}\nfunction getItemModel(data, dataIndex) {\n  var itemModel = data.getItemModel(dataIndex);\n  itemModel.getAnimationDelayParams = getAnimationDelayParams;\n  itemModel.isAnimationEnabled = isAnimationEnabled;\n  return itemModel;\n}\nfunction getAnimationDelayParams(path) {\n  // The order is the same as the z-order, see `symbolRepeatDiretion`.\n  return {\n    index: path.__pictorialAnimationIndex,\n    count: path.__pictorialRepeatTimes\n  };\n}\nfunction isAnimationEnabled() {\n  // `animation` prop can be set on itemModel in pictorial bar chart.\n  return this.parentModel.isAnimationEnabled() && !!this.getShallow('animation');\n}\nfunction createBar(data, opt, symbolMeta, isUpdate) {\n  // bar is the main element for each data.\n  var bar = new graphic.Group();\n  // bundle is used for location and clip.\n  var bundle = new graphic.Group();\n  bar.add(bundle);\n  bar.__pictorialBundle = bundle;\n  bundle.x = symbolMeta.bundlePosition[0];\n  bundle.y = symbolMeta.bundlePosition[1];\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, isUpdate);\n  createOrUpdateClip(bar, opt, symbolMeta, isUpdate);\n  bar.__pictorialShapeStr = getShapeStr(data, symbolMeta);\n  bar.__pictorialSymbolMeta = symbolMeta;\n  return bar;\n}\nfunction updateBar(bar, opt, symbolMeta) {\n  var animationModel = symbolMeta.animationModel;\n  var dataIndex = symbolMeta.dataIndex;\n  var bundle = bar.__pictorialBundle;\n  graphic.updateProps(bundle, {\n    x: symbolMeta.bundlePosition[0],\n    y: symbolMeta.bundlePosition[1]\n  }, animationModel, dataIndex);\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta, true);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta, true);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, true);\n  createOrUpdateClip(bar, opt, symbolMeta, true);\n}\nfunction removeBar(data, dataIndex, animationModel, bar) {\n  // Not show text when animating\n  var labelRect = bar.__pictorialBarRect;\n  labelRect && labelRect.removeTextContent();\n  var paths = [];\n  eachPath(bar, function (path) {\n    paths.push(path);\n  });\n  bar.__pictorialMainPath && paths.push(bar.__pictorialMainPath);\n  // I do not find proper remove animation for clip yet.\n  bar.__pictorialClipPath && (animationModel = null);\n  zrUtil.each(paths, function (path) {\n    graphic.removeElement(path, {\n      scaleX: 0,\n      scaleY: 0\n    }, animationModel, dataIndex, function () {\n      bar.parent && bar.parent.remove(bar);\n    });\n  });\n  data.setItemGraphicEl(dataIndex, null);\n}\nfunction getShapeStr(data, symbolMeta) {\n  return [data.getItemVisual(symbolMeta.dataIndex, 'symbol') || 'none', !!symbolMeta.symbolRepeat, !!symbolMeta.symbolClip].join(':');\n}\nfunction eachPath(bar, cb, context) {\n  // Do not use Group#eachChild, because it do not support remove.\n  zrUtil.each(bar.__pictorialBundle.children(), function (el) {\n    el !== bar.__pictorialBarRect && cb.call(context, el);\n  });\n}\nfunction updateAttr(el, immediateAttrs, animationAttrs, symbolMeta, isUpdate, cb) {\n  immediateAttrs && el.attr(immediateAttrs);\n  // when symbolCip used, only clip path has init animation, otherwise it would be weird effect.\n  if (symbolMeta.symbolClip && !isUpdate) {\n    animationAttrs && el.attr(animationAttrs);\n  } else {\n    animationAttrs && graphic[isUpdate ? 'updateProps' : 'initProps'](el, animationAttrs, symbolMeta.animationModel, symbolMeta.dataIndex, cb);\n  }\n}\nfunction updateCommon(bar, opt, symbolMeta) {\n  var dataIndex = symbolMeta.dataIndex;\n  var itemModel = symbolMeta.itemModel;\n  // Color must be excluded.\n  // Because symbol provide setColor individually to set fill and stroke\n  var emphasisModel = itemModel.getModel('emphasis');\n  var emphasisStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n  var blurStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n  var selectStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n  var cursorStyle = itemModel.getShallow('cursor');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var hoverScale = emphasisModel.get('scale');\n  eachPath(bar, function (path) {\n    if (path instanceof ZRImage) {\n      var pathStyle = path.style;\n      path.useStyle(zrUtil.extend({\n        // TODO other properties like dx, dy ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolMeta.style));\n    } else {\n      path.useStyle(symbolMeta.style);\n    }\n    var emphasisState = path.ensureState('emphasis');\n    emphasisState.style = emphasisStyle;\n    if (hoverScale) {\n      // NOTE: Must after scale is set after updateAttr\n      emphasisState.scaleX = path.scaleX * 1.1;\n      emphasisState.scaleY = path.scaleY * 1.1;\n    }\n    path.ensureState('blur').style = blurStyle;\n    path.ensureState('select').style = selectStyle;\n    cursorStyle && (path.cursor = cursorStyle);\n    path.z2 = symbolMeta.z2;\n  });\n  var barPositionOutside = opt.valueDim.posDesc[+(symbolMeta.boundingLength > 0)];\n  var barRect = bar.__pictorialBarRect;\n  barRect.ignoreClip = true;\n  setLabelStyle(barRect, getLabelStatesModels(itemModel), {\n    labelFetcher: opt.seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(opt.seriesModel.getData(), dataIndex),\n    inheritColor: symbolMeta.style.fill,\n    defaultOpacity: symbolMeta.style.opacity,\n    defaultOutsidePosition: barPositionOutside\n  });\n  toggleHoverEmphasis(bar, focus, blurScope, emphasisModel.get('disabled'));\n}\nfunction toIntTimes(times) {\n  var roundedTimes = Math.round(times);\n  // Escapse accurate error\n  return Math.abs(times - roundedTimes) < 1e-4 ? roundedTimes : Math.ceil(times);\n}\nexport default PictorialBarView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "toggleHoverEmphasis", "createSymbol", "normalizeSymbolOffset", "parsePercent", "isNumeric", "ChartView", "getDefaultLabel", "setLabelStyle", "getLabelStatesModels", "ZRImage", "getECData", "createClipPath", "BAR_BORDER_WIDTH_QUERY", "LAYOUT_ATTRS", "xy", "wh", "index", "posDesc", "pathForLineWidth", "Circle", "PictorialBarView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "seriesModel", "ecModel", "api", "group", "data", "getData", "oldData", "_data", "cartesian", "coordinateSystem", "baseAxis", "getBaseAxis", "isHorizontal", "coordSysRect", "master", "getRect", "opt", "ecSize", "width", "getWidth", "height", "getHeight", "coordSys", "coordSysExtent", "x", "y", "valueDim", "categoryDim", "diff", "add", "dataIndex", "hasValue", "itemModel", "getItemModel", "symbolMeta", "getSymbolMeta", "bar", "createBar", "setItemGraphicEl", "updateCommon", "update", "newIndex", "oldIndex", "getItemGraphicEl", "remove", "pictorialShapeStr", "getShapeStr", "__pictorialShapeStr", "updateBar", "__pictorialSymbolMeta", "removeBar", "animationModel", "execute", "clipPath", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "removeClip<PERSON>ath", "eachItemGraphicEl", "removeAll", "layout", "getItemLayout", "symbolRepeat", "symbolClip", "symbolPosition", "symbolRotate", "rotation", "Math", "PI", "symbolPatternSize", "isAnimationEnabled", "symbolType", "getItemVisual", "style", "symbolRepeatDirection", "hoverScale", "z2", "getShallow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prepareSymbolSize", "bounding<PERSON>ength", "pxSign", "prepareLineWidth", "symbolScale", "symbolSize", "symbolOffset", "prepareLayoutInfo", "valueLineWidth", "repeatCutLength", "outputSymbolMeta", "symbolBoundingData", "valueAxis", "getOtherAxis", "zeroPx", "toGlobalCoord", "dataToCoord", "pxSignIdx", "isArray", "symbolBoundingExtent", "convertToCoordOnAxis", "reverse", "isXAxis", "isInverse", "inverse", "axis", "value", "scale", "parse", "categorySize", "abs", "parsedSymbolSize", "slice", "attr", "scaleX", "scaleY", "updateTransform", "getLineScale", "unitLength", "max", "pathLen", "absBoundingLength", "symbol<PERSON><PERSON><PERSON>", "retrieve", "hasEndGap", "lastIndexOf", "length", "symbolMarginNumeric", "uLenWithMargin", "endFix", "repeatSpecified", "repeatTimes", "toIntTimes", "mDiff", "sizeFix", "pathPosition", "bundlePosition", "barRectShape", "extend", "clipShape", "createPath", "path", "culling", "setStyle", "strokeNoScale", "createOrUpdateRepeatSymbols", "isUpdate", "bundle", "__pictorialBundle", "unit", "eachPath", "__pictorialAnimationIndex", "__pictorialRepeatTimes", "updateAttr", "makeTarget", "target", "position", "i", "createOrUpdateSingleSymbol", "mainP<PERSON>", "__pictorial<PERSON><PERSON><PERSON><PERSON>", "createOrUpdateBarRect", "rectShape", "barRect", "__pictorialBarRect", "Rect", "shape", "silent", "stroke", "fill", "lineWidth", "disableMorphing", "createOrUpdateClip", "__pictorialClipPath", "updateProps", "getAnimationDelayParams", "count", "parentModel", "Group", "labelRect", "removeTextContent", "paths", "push", "each", "removeElement", "parent", "join", "cb", "context", "children", "el", "call", "immediateAttrs", "animationAttrs", "emphasisModel", "getModel", "emphasisStyle", "getItemStyle", "blurStyle", "selectStyle", "cursorStyle", "focus", "blurScope", "pathStyle", "useStyle", "image", "emphasisState", "ensureState", "cursor", "barPositionOutside", "ignoreClip", "labelFetcher", "labelDataIndex", "defaultText", "inheritColor", "defaultOpacity", "opacity", "defaultOutsidePosition", "times", "roundedTimes", "round", "ceil"], "sources": ["/Users/<USER>/Downloads/invenrtory-1/نسخة 7 من inventory/node_modules/echarts/lib/chart/bar/PictorialBarView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport { parsePercent, isNumeric } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nvar BAR_BORDER_WIDTH_QUERY = ['itemStyle', 'borderWidth'];\n// index: +isHorizontal\nvar LAYOUT_ATTRS = [{\n  xy: 'x',\n  wh: 'width',\n  index: 0,\n  posDesc: ['left', 'right']\n}, {\n  xy: 'y',\n  wh: 'height',\n  index: 1,\n  posDesc: ['top', 'bottom']\n}];\nvar pathForLineWidth = new graphic.Circle();\nvar PictorialBarView = /** @class */function (_super) {\n  __extends(PictorialBarView, _super);\n  function PictorialBarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PictorialBarView.type;\n    return _this;\n  }\n  PictorialBarView.prototype.render = function (seriesModel, ecModel, api) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var coordSysRect = cartesian.master.getRect();\n    var opt = {\n      ecSize: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      seriesModel: seriesModel,\n      coordSys: cartesian,\n      coordSysExtent: [[coordSysRect.x, coordSysRect.x + coordSysRect.width], [coordSysRect.y, coordSysRect.y + coordSysRect.height]],\n      isHorizontal: isHorizontal,\n      valueDim: LAYOUT_ATTRS[+isHorizontal],\n      categoryDim: LAYOUT_ATTRS[1 - +isHorizontal]\n    };\n    data.diff(oldData).add(function (dataIndex) {\n      if (!data.hasValue(dataIndex)) {\n        return;\n      }\n      var itemModel = getItemModel(data, dataIndex);\n      var symbolMeta = getSymbolMeta(data, dataIndex, itemModel, opt);\n      var bar = createBar(data, opt, symbolMeta);\n      data.setItemGraphicEl(dataIndex, bar);\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).update(function (newIndex, oldIndex) {\n      var bar = oldData.getItemGraphicEl(oldIndex);\n      if (!data.hasValue(newIndex)) {\n        group.remove(bar);\n        return;\n      }\n      var itemModel = getItemModel(data, newIndex);\n      var symbolMeta = getSymbolMeta(data, newIndex, itemModel, opt);\n      var pictorialShapeStr = getShapeStr(data, symbolMeta);\n      if (bar && pictorialShapeStr !== bar.__pictorialShapeStr) {\n        group.remove(bar);\n        data.setItemGraphicEl(newIndex, null);\n        bar = null;\n      }\n      if (bar) {\n        updateBar(bar, opt, symbolMeta);\n      } else {\n        bar = createBar(data, opt, symbolMeta, true);\n      }\n      data.setItemGraphicEl(newIndex, bar);\n      bar.__pictorialSymbolMeta = symbolMeta;\n      // Add back\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).remove(function (dataIndex) {\n      var bar = oldData.getItemGraphicEl(dataIndex);\n      bar && removeBar(oldData, dataIndex, bar.__pictorialSymbolMeta.animationModel, bar);\n    }).execute();\n    // Do clipping\n    var clipPath = seriesModel.get('clip', true) ? createClipPath(seriesModel.coordinateSystem, false, seriesModel) : null;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n    this._data = data;\n    return this.group;\n  };\n  PictorialBarView.prototype.remove = function (ecModel, api) {\n    var group = this.group;\n    var data = this._data;\n    if (ecModel.get('animation')) {\n      if (data) {\n        data.eachItemGraphicEl(function (bar) {\n          removeBar(data, getECData(bar).dataIndex, ecModel, bar);\n        });\n      }\n    } else {\n      group.removeAll();\n    }\n  };\n  PictorialBarView.type = 'pictorialBar';\n  return PictorialBarView;\n}(ChartView);\n// Set or calculate default value about symbol, and calculate layout info.\nfunction getSymbolMeta(data, dataIndex, itemModel, opt) {\n  var layout = data.getItemLayout(dataIndex);\n  var symbolRepeat = itemModel.get('symbolRepeat');\n  var symbolClip = itemModel.get('symbolClip');\n  var symbolPosition = itemModel.get('symbolPosition') || 'start';\n  var symbolRotate = itemModel.get('symbolRotate');\n  var rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n  var symbolPatternSize = itemModel.get('symbolPatternSize') || 2;\n  var isAnimationEnabled = itemModel.isAnimationEnabled();\n  var symbolMeta = {\n    dataIndex: dataIndex,\n    layout: layout,\n    itemModel: itemModel,\n    symbolType: data.getItemVisual(dataIndex, 'symbol') || 'circle',\n    style: data.getItemVisual(dataIndex, 'style'),\n    symbolClip: symbolClip,\n    symbolRepeat: symbolRepeat,\n    symbolRepeatDirection: itemModel.get('symbolRepeatDirection'),\n    symbolPatternSize: symbolPatternSize,\n    rotation: rotation,\n    animationModel: isAnimationEnabled ? itemModel : null,\n    hoverScale: isAnimationEnabled && itemModel.get(['emphasis', 'scale']),\n    z2: itemModel.getShallow('z', true) || 0\n  };\n  prepareBarLength(itemModel, symbolRepeat, layout, opt, symbolMeta);\n  prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, symbolMeta.boundingLength, symbolMeta.pxSign, symbolPatternSize, opt, symbolMeta);\n  prepareLineWidth(itemModel, symbolMeta.symbolScale, rotation, opt, symbolMeta);\n  var symbolSize = symbolMeta.symbolSize;\n  var symbolOffset = normalizeSymbolOffset(itemModel.get('symbolOffset'), symbolSize);\n  prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, symbolMeta.valueLineWidth, symbolMeta.boundingLength, symbolMeta.repeatCutLength, opt, symbolMeta);\n  return symbolMeta;\n}\n// bar length can be negative.\nfunction prepareBarLength(itemModel, symbolRepeat, layout, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var symbolBoundingData = itemModel.get('symbolBoundingData');\n  var valueAxis = opt.coordSys.getOtherAxis(opt.coordSys.getBaseAxis());\n  var zeroPx = valueAxis.toGlobalCoord(valueAxis.dataToCoord(0));\n  var pxSignIdx = 1 - +(layout[valueDim.wh] <= 0);\n  var boundingLength;\n  if (zrUtil.isArray(symbolBoundingData)) {\n    var symbolBoundingExtent = [convertToCoordOnAxis(valueAxis, symbolBoundingData[0]) - zeroPx, convertToCoordOnAxis(valueAxis, symbolBoundingData[1]) - zeroPx];\n    symbolBoundingExtent[1] < symbolBoundingExtent[0] && symbolBoundingExtent.reverse();\n    boundingLength = symbolBoundingExtent[pxSignIdx];\n  } else if (symbolBoundingData != null) {\n    boundingLength = convertToCoordOnAxis(valueAxis, symbolBoundingData) - zeroPx;\n  } else if (symbolRepeat) {\n    boundingLength = opt.coordSysExtent[valueDim.index][pxSignIdx] - zeroPx;\n  } else {\n    boundingLength = layout[valueDim.wh];\n  }\n  outputSymbolMeta.boundingLength = boundingLength;\n  if (symbolRepeat) {\n    outputSymbolMeta.repeatCutLength = layout[valueDim.wh];\n  }\n  // if 'pxSign' means sign of pixel,  it can't be zero, or symbolScale will be zero\n  // and when borderWidth be settled, the actual linewidth will be NaN\n  var isXAxis = valueDim.xy === 'x';\n  var isInverse = valueAxis.inverse;\n  outputSymbolMeta.pxSign = isXAxis && !isInverse || !isXAxis && isInverse ? boundingLength >= 0 ? 1 : -1 : boundingLength > 0 ? 1 : -1;\n}\nfunction convertToCoordOnAxis(axis, value) {\n  return axis.toGlobalCoord(axis.dataToCoord(axis.scale.parse(value)));\n}\n// Support ['100%', '100%']\nfunction prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, boundingLength, pxSign, symbolPatternSize, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var categoryDim = opt.categoryDim;\n  var categorySize = Math.abs(layout[categoryDim.wh]);\n  var symbolSize = data.getItemVisual(dataIndex, 'symbolSize');\n  var parsedSymbolSize;\n  if (zrUtil.isArray(symbolSize)) {\n    parsedSymbolSize = symbolSize.slice();\n  } else {\n    if (symbolSize == null) {\n      // will parse to number below\n      parsedSymbolSize = ['100%', '100%'];\n    } else {\n      parsedSymbolSize = [symbolSize, symbolSize];\n    }\n  }\n  // Note: percentage symbolSize (like '100%') do not consider lineWidth, because it is\n  // to complicated to calculate real percent value if considering scaled lineWidth.\n  // So the actual size will bigger than layout size if lineWidth is bigger than zero,\n  // which can be tolerated in pictorial chart.\n  parsedSymbolSize[categoryDim.index] = parsePercent(parsedSymbolSize[categoryDim.index], categorySize);\n  parsedSymbolSize[valueDim.index] = parsePercent(parsedSymbolSize[valueDim.index], symbolRepeat ? categorySize : Math.abs(boundingLength));\n  outputSymbolMeta.symbolSize = parsedSymbolSize;\n  // If x or y is less than zero, show reversed shape.\n  var symbolScale = outputSymbolMeta.symbolScale = [parsedSymbolSize[0] / symbolPatternSize, parsedSymbolSize[1] / symbolPatternSize];\n  // Follow convention, 'right' and 'top' is the normal scale.\n  symbolScale[valueDim.index] *= (opt.isHorizontal ? -1 : 1) * pxSign;\n}\nfunction prepareLineWidth(itemModel, symbolScale, rotation, opt, outputSymbolMeta) {\n  // In symbols are drawn with scale, so do not need to care about the case that width\n  // or height are too small. But symbol use strokeNoScale, where acture lineWidth should\n  // be calculated.\n  var valueLineWidth = itemModel.get(BAR_BORDER_WIDTH_QUERY) || 0;\n  if (valueLineWidth) {\n    pathForLineWidth.attr({\n      scaleX: symbolScale[0],\n      scaleY: symbolScale[1],\n      rotation: rotation\n    });\n    pathForLineWidth.updateTransform();\n    valueLineWidth /= pathForLineWidth.getLineScale();\n    valueLineWidth *= symbolScale[opt.valueDim.index];\n  }\n  outputSymbolMeta.valueLineWidth = valueLineWidth || 0;\n}\nfunction prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, valueLineWidth, boundingLength, repeatCutLength, opt, outputSymbolMeta) {\n  var categoryDim = opt.categoryDim;\n  var valueDim = opt.valueDim;\n  var pxSign = outputSymbolMeta.pxSign;\n  var unitLength = Math.max(symbolSize[valueDim.index] + valueLineWidth, 0);\n  var pathLen = unitLength;\n  // Note: rotation will not effect the layout of symbols, because user may\n  // want symbols to rotate on its center, which should not be translated\n  // when rotating.\n  if (symbolRepeat) {\n    var absBoundingLength = Math.abs(boundingLength);\n    var symbolMargin = zrUtil.retrieve(itemModel.get('symbolMargin'), '15%') + '';\n    var hasEndGap = false;\n    if (symbolMargin.lastIndexOf('!') === symbolMargin.length - 1) {\n      hasEndGap = true;\n      symbolMargin = symbolMargin.slice(0, symbolMargin.length - 1);\n    }\n    var symbolMarginNumeric = parsePercent(symbolMargin, symbolSize[valueDim.index]);\n    var uLenWithMargin = Math.max(unitLength + symbolMarginNumeric * 2, 0);\n    // When symbol margin is less than 0, margin at both ends will be subtracted\n    // to ensure that all of the symbols will not be overflow the given area.\n    var endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;\n    // Both final repeatTimes and final symbolMarginNumeric area calculated based on\n    // boundingLength.\n    var repeatSpecified = isNumeric(symbolRepeat);\n    var repeatTimes = repeatSpecified ? symbolRepeat : toIntTimes((absBoundingLength + endFix) / uLenWithMargin);\n    // Adjust calculate margin, to ensure each symbol is displayed\n    // entirely in the given layout area.\n    var mDiff = absBoundingLength - repeatTimes * unitLength;\n    symbolMarginNumeric = mDiff / 2 / (hasEndGap ? repeatTimes : Math.max(repeatTimes - 1, 1));\n    uLenWithMargin = unitLength + symbolMarginNumeric * 2;\n    endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;\n    // Update repeatTimes when not all symbol will be shown.\n    if (!repeatSpecified && symbolRepeat !== 'fixed') {\n      repeatTimes = repeatCutLength ? toIntTimes((Math.abs(repeatCutLength) + endFix) / uLenWithMargin) : 0;\n    }\n    pathLen = repeatTimes * uLenWithMargin - endFix;\n    outputSymbolMeta.repeatTimes = repeatTimes;\n    outputSymbolMeta.symbolMargin = symbolMarginNumeric;\n  }\n  var sizeFix = pxSign * (pathLen / 2);\n  var pathPosition = outputSymbolMeta.pathPosition = [];\n  pathPosition[categoryDim.index] = layout[categoryDim.wh] / 2;\n  pathPosition[valueDim.index] = symbolPosition === 'start' ? sizeFix : symbolPosition === 'end' ? boundingLength - sizeFix : boundingLength / 2; // 'center'\n  if (symbolOffset) {\n    pathPosition[0] += symbolOffset[0];\n    pathPosition[1] += symbolOffset[1];\n  }\n  var bundlePosition = outputSymbolMeta.bundlePosition = [];\n  bundlePosition[categoryDim.index] = layout[categoryDim.xy];\n  bundlePosition[valueDim.index] = layout[valueDim.xy];\n  var barRectShape = outputSymbolMeta.barRectShape = zrUtil.extend({}, layout);\n  barRectShape[valueDim.wh] = pxSign * Math.max(Math.abs(layout[valueDim.wh]), Math.abs(pathPosition[valueDim.index] + sizeFix));\n  barRectShape[categoryDim.wh] = layout[categoryDim.wh];\n  var clipShape = outputSymbolMeta.clipShape = {};\n  // Consider that symbol may be overflow layout rect.\n  clipShape[categoryDim.xy] = -layout[categoryDim.xy];\n  clipShape[categoryDim.wh] = opt.ecSize[categoryDim.wh];\n  clipShape[valueDim.xy] = 0;\n  clipShape[valueDim.wh] = layout[valueDim.wh];\n}\nfunction createPath(symbolMeta) {\n  var symbolPatternSize = symbolMeta.symbolPatternSize;\n  var path = createSymbol(\n  // Consider texture img, make a big size.\n  symbolMeta.symbolType, -symbolPatternSize / 2, -symbolPatternSize / 2, symbolPatternSize, symbolPatternSize);\n  path.attr({\n    culling: true\n  });\n  path.type !== 'image' && path.setStyle({\n    strokeNoScale: true\n  });\n  return path;\n}\nfunction createOrUpdateRepeatSymbols(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var symbolSize = symbolMeta.symbolSize;\n  var valueLineWidth = symbolMeta.valueLineWidth;\n  var pathPosition = symbolMeta.pathPosition;\n  var valueDim = opt.valueDim;\n  var repeatTimes = symbolMeta.repeatTimes || 0;\n  var index = 0;\n  var unit = symbolSize[opt.valueDim.index] + valueLineWidth + symbolMeta.symbolMargin * 2;\n  eachPath(bar, function (path) {\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    if (index < repeatTimes) {\n      updateAttr(path, null, makeTarget(index), symbolMeta, isUpdate);\n    } else {\n      updateAttr(path, null, {\n        scaleX: 0,\n        scaleY: 0\n      }, symbolMeta, isUpdate, function () {\n        bundle.remove(path);\n      });\n    }\n    // updateHoverAnimation(path, symbolMeta);\n    index++;\n  });\n  for (; index < repeatTimes; index++) {\n    var path = createPath(symbolMeta);\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    bundle.add(path);\n    var target = makeTarget(index);\n    updateAttr(path, {\n      x: target.x,\n      y: target.y,\n      scaleX: 0,\n      scaleY: 0\n    }, {\n      scaleX: target.scaleX,\n      scaleY: target.scaleY,\n      rotation: target.rotation\n    }, symbolMeta, isUpdate);\n  }\n  function makeTarget(index) {\n    var position = pathPosition.slice();\n    // (start && pxSign > 0) || (end && pxSign < 0): i = repeatTimes - index\n    // Otherwise: i = index;\n    var pxSign = symbolMeta.pxSign;\n    var i = index;\n    if (symbolMeta.symbolRepeatDirection === 'start' ? pxSign > 0 : pxSign < 0) {\n      i = repeatTimes - 1 - index;\n    }\n    position[valueDim.index] = unit * (i - repeatTimes / 2 + 0.5) + pathPosition[valueDim.index];\n    return {\n      x: position[0],\n      y: position[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    };\n  }\n}\nfunction createOrUpdateSingleSymbol(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var mainPath = bar.__pictorialMainPath;\n  if (!mainPath) {\n    mainPath = bar.__pictorialMainPath = createPath(symbolMeta);\n    bundle.add(mainPath);\n    updateAttr(mainPath, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: 0,\n      scaleY: 0,\n      rotation: symbolMeta.rotation\n    }, {\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1]\n    }, symbolMeta, isUpdate);\n  } else {\n    updateAttr(mainPath, null, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    }, symbolMeta, isUpdate);\n  }\n}\n// bar rect is used for label.\nfunction createOrUpdateBarRect(bar, symbolMeta, isUpdate) {\n  var rectShape = zrUtil.extend({}, symbolMeta.barRectShape);\n  var barRect = bar.__pictorialBarRect;\n  if (!barRect) {\n    barRect = bar.__pictorialBarRect = new graphic.Rect({\n      z2: 2,\n      shape: rectShape,\n      silent: true,\n      style: {\n        stroke: 'transparent',\n        fill: 'transparent',\n        lineWidth: 0\n      }\n    });\n    barRect.disableMorphing = true;\n    bar.add(barRect);\n  } else {\n    updateAttr(barRect, null, {\n      shape: rectShape\n    }, symbolMeta, isUpdate);\n  }\n}\nfunction createOrUpdateClip(bar, opt, symbolMeta, isUpdate) {\n  // If not clip, symbol will be remove and rebuilt.\n  if (symbolMeta.symbolClip) {\n    var clipPath = bar.__pictorialClipPath;\n    var clipShape = zrUtil.extend({}, symbolMeta.clipShape);\n    var valueDim = opt.valueDim;\n    var animationModel = symbolMeta.animationModel;\n    var dataIndex = symbolMeta.dataIndex;\n    if (clipPath) {\n      graphic.updateProps(clipPath, {\n        shape: clipShape\n      }, animationModel, dataIndex);\n    } else {\n      clipShape[valueDim.wh] = 0;\n      clipPath = new graphic.Rect({\n        shape: clipShape\n      });\n      bar.__pictorialBundle.setClipPath(clipPath);\n      bar.__pictorialClipPath = clipPath;\n      var target = {};\n      target[valueDim.wh] = symbolMeta.clipShape[valueDim.wh];\n      graphic[isUpdate ? 'updateProps' : 'initProps'](clipPath, {\n        shape: target\n      }, animationModel, dataIndex);\n    }\n  }\n}\nfunction getItemModel(data, dataIndex) {\n  var itemModel = data.getItemModel(dataIndex);\n  itemModel.getAnimationDelayParams = getAnimationDelayParams;\n  itemModel.isAnimationEnabled = isAnimationEnabled;\n  return itemModel;\n}\nfunction getAnimationDelayParams(path) {\n  // The order is the same as the z-order, see `symbolRepeatDiretion`.\n  return {\n    index: path.__pictorialAnimationIndex,\n    count: path.__pictorialRepeatTimes\n  };\n}\nfunction isAnimationEnabled() {\n  // `animation` prop can be set on itemModel in pictorial bar chart.\n  return this.parentModel.isAnimationEnabled() && !!this.getShallow('animation');\n}\nfunction createBar(data, opt, symbolMeta, isUpdate) {\n  // bar is the main element for each data.\n  var bar = new graphic.Group();\n  // bundle is used for location and clip.\n  var bundle = new graphic.Group();\n  bar.add(bundle);\n  bar.__pictorialBundle = bundle;\n  bundle.x = symbolMeta.bundlePosition[0];\n  bundle.y = symbolMeta.bundlePosition[1];\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, isUpdate);\n  createOrUpdateClip(bar, opt, symbolMeta, isUpdate);\n  bar.__pictorialShapeStr = getShapeStr(data, symbolMeta);\n  bar.__pictorialSymbolMeta = symbolMeta;\n  return bar;\n}\nfunction updateBar(bar, opt, symbolMeta) {\n  var animationModel = symbolMeta.animationModel;\n  var dataIndex = symbolMeta.dataIndex;\n  var bundle = bar.__pictorialBundle;\n  graphic.updateProps(bundle, {\n    x: symbolMeta.bundlePosition[0],\n    y: symbolMeta.bundlePosition[1]\n  }, animationModel, dataIndex);\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta, true);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta, true);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, true);\n  createOrUpdateClip(bar, opt, symbolMeta, true);\n}\nfunction removeBar(data, dataIndex, animationModel, bar) {\n  // Not show text when animating\n  var labelRect = bar.__pictorialBarRect;\n  labelRect && labelRect.removeTextContent();\n  var paths = [];\n  eachPath(bar, function (path) {\n    paths.push(path);\n  });\n  bar.__pictorialMainPath && paths.push(bar.__pictorialMainPath);\n  // I do not find proper remove animation for clip yet.\n  bar.__pictorialClipPath && (animationModel = null);\n  zrUtil.each(paths, function (path) {\n    graphic.removeElement(path, {\n      scaleX: 0,\n      scaleY: 0\n    }, animationModel, dataIndex, function () {\n      bar.parent && bar.parent.remove(bar);\n    });\n  });\n  data.setItemGraphicEl(dataIndex, null);\n}\nfunction getShapeStr(data, symbolMeta) {\n  return [data.getItemVisual(symbolMeta.dataIndex, 'symbol') || 'none', !!symbolMeta.symbolRepeat, !!symbolMeta.symbolClip].join(':');\n}\nfunction eachPath(bar, cb, context) {\n  // Do not use Group#eachChild, because it do not support remove.\n  zrUtil.each(bar.__pictorialBundle.children(), function (el) {\n    el !== bar.__pictorialBarRect && cb.call(context, el);\n  });\n}\nfunction updateAttr(el, immediateAttrs, animationAttrs, symbolMeta, isUpdate, cb) {\n  immediateAttrs && el.attr(immediateAttrs);\n  // when symbolCip used, only clip path has init animation, otherwise it would be weird effect.\n  if (symbolMeta.symbolClip && !isUpdate) {\n    animationAttrs && el.attr(animationAttrs);\n  } else {\n    animationAttrs && graphic[isUpdate ? 'updateProps' : 'initProps'](el, animationAttrs, symbolMeta.animationModel, symbolMeta.dataIndex, cb);\n  }\n}\nfunction updateCommon(bar, opt, symbolMeta) {\n  var dataIndex = symbolMeta.dataIndex;\n  var itemModel = symbolMeta.itemModel;\n  // Color must be excluded.\n  // Because symbol provide setColor individually to set fill and stroke\n  var emphasisModel = itemModel.getModel('emphasis');\n  var emphasisStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n  var blurStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n  var selectStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n  var cursorStyle = itemModel.getShallow('cursor');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var hoverScale = emphasisModel.get('scale');\n  eachPath(bar, function (path) {\n    if (path instanceof ZRImage) {\n      var pathStyle = path.style;\n      path.useStyle(zrUtil.extend({\n        // TODO other properties like dx, dy ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolMeta.style));\n    } else {\n      path.useStyle(symbolMeta.style);\n    }\n    var emphasisState = path.ensureState('emphasis');\n    emphasisState.style = emphasisStyle;\n    if (hoverScale) {\n      // NOTE: Must after scale is set after updateAttr\n      emphasisState.scaleX = path.scaleX * 1.1;\n      emphasisState.scaleY = path.scaleY * 1.1;\n    }\n    path.ensureState('blur').style = blurStyle;\n    path.ensureState('select').style = selectStyle;\n    cursorStyle && (path.cursor = cursorStyle);\n    path.z2 = symbolMeta.z2;\n  });\n  var barPositionOutside = opt.valueDim.posDesc[+(symbolMeta.boundingLength > 0)];\n  var barRect = bar.__pictorialBarRect;\n  barRect.ignoreClip = true;\n  setLabelStyle(barRect, getLabelStatesModels(itemModel), {\n    labelFetcher: opt.seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(opt.seriesModel.getData(), dataIndex),\n    inheritColor: symbolMeta.style.fill,\n    defaultOpacity: symbolMeta.style.opacity,\n    defaultOutsidePosition: barPositionOutside\n  });\n  toggleHoverEmphasis(bar, focus, blurScope, emphasisModel.get('disabled'));\n}\nfunction toIntTimes(times) {\n  var roundedTimes = Math.round(times);\n  // Escapse accurate error\n  return Math.abs(times - roundedTimes) < 1e-4 ? roundedTimes : Math.ceil(times);\n}\nexport default PictorialBarView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,sBAAsB;AAC1E,SAASC,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,OAAOC,OAAO,MAAM,8BAA8B;AAClD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,cAAc,QAAQ,yCAAyC;AACxE,IAAIC,sBAAsB,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC;AACzD;AACA,IAAIC,YAAY,GAAG,CAAC;EAClBC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,OAAO;EACXC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;AAC3B,CAAC,EAAE;EACDH,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ;AAC3B,CAAC,CAAC;AACF,IAAIC,gBAAgB,GAAG,IAAInB,OAAO,CAACoB,MAAM,CAAC,CAAC;AAC3C,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACpDxB,SAAS,CAACuB,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClC,OAAOH,KAAK;EACd;EACAF,gBAAgB,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACvE,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,IAAI,GAAGJ,WAAW,CAACK,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAIC,SAAS,GAAGR,WAAW,CAACS,gBAAgB;IAC5C,IAAIC,QAAQ,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC;IACtC,IAAIC,YAAY,GAAGF,QAAQ,CAACE,YAAY,CAAC,CAAC;IAC1C,IAAIC,YAAY,GAAGL,SAAS,CAACM,MAAM,CAACC,OAAO,CAAC,CAAC;IAC7C,IAAIC,GAAG,GAAG;MACRC,MAAM,EAAE;QACNC,KAAK,EAAEhB,GAAG,CAACiB,QAAQ,CAAC,CAAC;QACrBC,MAAM,EAAElB,GAAG,CAACmB,SAAS,CAAC;MACxB,CAAC;MACDrB,WAAW,EAAEA,WAAW;MACxBsB,QAAQ,EAAEd,SAAS;MACnBe,cAAc,EAAE,CAAC,CAACV,YAAY,CAACW,CAAC,EAAEX,YAAY,CAACW,CAAC,GAAGX,YAAY,CAACK,KAAK,CAAC,EAAE,CAACL,YAAY,CAACY,CAAC,EAAEZ,YAAY,CAACY,CAAC,GAAGZ,YAAY,CAACO,MAAM,CAAC,CAAC;MAC/HR,YAAY,EAAEA,YAAY;MAC1Bc,QAAQ,EAAEzC,YAAY,CAAC,CAAC2B,YAAY,CAAC;MACrCe,WAAW,EAAE1C,YAAY,CAAC,CAAC,GAAG,CAAC2B,YAAY;IAC7C,CAAC;IACDR,IAAI,CAACwB,IAAI,CAACtB,OAAO,CAAC,CAACuB,GAAG,CAAC,UAAUC,SAAS,EAAE;MAC1C,IAAI,CAAC1B,IAAI,CAAC2B,QAAQ,CAACD,SAAS,CAAC,EAAE;QAC7B;MACF;MACA,IAAIE,SAAS,GAAGC,YAAY,CAAC7B,IAAI,EAAE0B,SAAS,CAAC;MAC7C,IAAII,UAAU,GAAGC,aAAa,CAAC/B,IAAI,EAAE0B,SAAS,EAAEE,SAAS,EAAEhB,GAAG,CAAC;MAC/D,IAAIoB,GAAG,GAAGC,SAAS,CAACjC,IAAI,EAAEY,GAAG,EAAEkB,UAAU,CAAC;MAC1C9B,IAAI,CAACkC,gBAAgB,CAACR,SAAS,EAAEM,GAAG,CAAC;MACrCjC,KAAK,CAAC0B,GAAG,CAACO,GAAG,CAAC;MACdG,YAAY,CAACH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;IACpC,CAAC,CAAC,CAACM,MAAM,CAAC,UAAUC,QAAQ,EAAEC,QAAQ,EAAE;MACtC,IAAIN,GAAG,GAAG9B,OAAO,CAACqC,gBAAgB,CAACD,QAAQ,CAAC;MAC5C,IAAI,CAACtC,IAAI,CAAC2B,QAAQ,CAACU,QAAQ,CAAC,EAAE;QAC5BtC,KAAK,CAACyC,MAAM,CAACR,GAAG,CAAC;QACjB;MACF;MACA,IAAIJ,SAAS,GAAGC,YAAY,CAAC7B,IAAI,EAAEqC,QAAQ,CAAC;MAC5C,IAAIP,UAAU,GAAGC,aAAa,CAAC/B,IAAI,EAAEqC,QAAQ,EAAET,SAAS,EAAEhB,GAAG,CAAC;MAC9D,IAAI6B,iBAAiB,GAAGC,WAAW,CAAC1C,IAAI,EAAE8B,UAAU,CAAC;MACrD,IAAIE,GAAG,IAAIS,iBAAiB,KAAKT,GAAG,CAACW,mBAAmB,EAAE;QACxD5C,KAAK,CAACyC,MAAM,CAACR,GAAG,CAAC;QACjBhC,IAAI,CAACkC,gBAAgB,CAACG,QAAQ,EAAE,IAAI,CAAC;QACrCL,GAAG,GAAG,IAAI;MACZ;MACA,IAAIA,GAAG,EAAE;QACPY,SAAS,CAACZ,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;MACjC,CAAC,MAAM;QACLE,GAAG,GAAGC,SAAS,CAACjC,IAAI,EAAEY,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;MAC9C;MACA9B,IAAI,CAACkC,gBAAgB,CAACG,QAAQ,EAAEL,GAAG,CAAC;MACpCA,GAAG,CAACa,qBAAqB,GAAGf,UAAU;MACtC;MACA/B,KAAK,CAAC0B,GAAG,CAACO,GAAG,CAAC;MACdG,YAAY,CAACH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;IACpC,CAAC,CAAC,CAACU,MAAM,CAAC,UAAUd,SAAS,EAAE;MAC7B,IAAIM,GAAG,GAAG9B,OAAO,CAACqC,gBAAgB,CAACb,SAAS,CAAC;MAC7CM,GAAG,IAAIc,SAAS,CAAC5C,OAAO,EAAEwB,SAAS,EAAEM,GAAG,CAACa,qBAAqB,CAACE,cAAc,EAAEf,GAAG,CAAC;IACrF,CAAC,CAAC,CAACgB,OAAO,CAAC,CAAC;IACZ;IACA,IAAIC,QAAQ,GAAGrD,WAAW,CAACsD,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAGvE,cAAc,CAACiB,WAAW,CAACS,gBAAgB,EAAE,KAAK,EAAET,WAAW,CAAC,GAAG,IAAI;IACtH,IAAIqD,QAAQ,EAAE;MACZlD,KAAK,CAACoD,WAAW,CAACF,QAAQ,CAAC;IAC7B,CAAC,MAAM;MACLlD,KAAK,CAACqD,cAAc,CAAC,CAAC;IACxB;IACA,IAAI,CAACjD,KAAK,GAAGH,IAAI;IACjB,OAAO,IAAI,CAACD,KAAK;EACnB,CAAC;EACDX,gBAAgB,CAACM,SAAS,CAAC8C,MAAM,GAAG,UAAU3C,OAAO,EAAEC,GAAG,EAAE;IAC1D,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,IAAI,GAAG,IAAI,CAACG,KAAK;IACrB,IAAIN,OAAO,CAACqD,GAAG,CAAC,WAAW,CAAC,EAAE;MAC5B,IAAIlD,IAAI,EAAE;QACRA,IAAI,CAACqD,iBAAiB,CAAC,UAAUrB,GAAG,EAAE;UACpCc,SAAS,CAAC9C,IAAI,EAAEtB,SAAS,CAACsD,GAAG,CAAC,CAACN,SAAS,EAAE7B,OAAO,EAAEmC,GAAG,CAAC;QACzD,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACLjC,KAAK,CAACuD,SAAS,CAAC,CAAC;IACnB;EACF,CAAC;EACDlE,gBAAgB,CAACK,IAAI,GAAG,cAAc;EACtC,OAAOL,gBAAgB;AACzB,CAAC,CAACf,SAAS,CAAC;AACZ;AACA,SAAS0D,aAAaA,CAAC/B,IAAI,EAAE0B,SAAS,EAAEE,SAAS,EAAEhB,GAAG,EAAE;EACtD,IAAI2C,MAAM,GAAGvD,IAAI,CAACwD,aAAa,CAAC9B,SAAS,CAAC;EAC1C,IAAI+B,YAAY,GAAG7B,SAAS,CAACsB,GAAG,CAAC,cAAc,CAAC;EAChD,IAAIQ,UAAU,GAAG9B,SAAS,CAACsB,GAAG,CAAC,YAAY,CAAC;EAC5C,IAAIS,cAAc,GAAG/B,SAAS,CAACsB,GAAG,CAAC,gBAAgB,CAAC,IAAI,OAAO;EAC/D,IAAIU,YAAY,GAAGhC,SAAS,CAACsB,GAAG,CAAC,cAAc,CAAC;EAChD,IAAIW,QAAQ,GAAG,CAACD,YAAY,IAAI,CAAC,IAAIE,IAAI,CAACC,EAAE,GAAG,GAAG,IAAI,CAAC;EACvD,IAAIC,iBAAiB,GAAGpC,SAAS,CAACsB,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC;EAC/D,IAAIe,kBAAkB,GAAGrC,SAAS,CAACqC,kBAAkB,CAAC,CAAC;EACvD,IAAInC,UAAU,GAAG;IACfJ,SAAS,EAAEA,SAAS;IACpB6B,MAAM,EAAEA,MAAM;IACd3B,SAAS,EAAEA,SAAS;IACpBsC,UAAU,EAAElE,IAAI,CAACmE,aAAa,CAACzC,SAAS,EAAE,QAAQ,CAAC,IAAI,QAAQ;IAC/D0C,KAAK,EAAEpE,IAAI,CAACmE,aAAa,CAACzC,SAAS,EAAE,OAAO,CAAC;IAC7CgC,UAAU,EAAEA,UAAU;IACtBD,YAAY,EAAEA,YAAY;IAC1BY,qBAAqB,EAAEzC,SAAS,CAACsB,GAAG,CAAC,uBAAuB,CAAC;IAC7Dc,iBAAiB,EAAEA,iBAAiB;IACpCH,QAAQ,EAAEA,QAAQ;IAClBd,cAAc,EAAEkB,kBAAkB,GAAGrC,SAAS,GAAG,IAAI;IACrD0C,UAAU,EAAEL,kBAAkB,IAAIrC,SAAS,CAACsB,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACtEqB,EAAE,EAAE3C,SAAS,CAAC4C,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI;EACzC,CAAC;EACDC,gBAAgB,CAAC7C,SAAS,EAAE6B,YAAY,EAAEF,MAAM,EAAE3C,GAAG,EAAEkB,UAAU,CAAC;EAClE4C,iBAAiB,CAAC1E,IAAI,EAAE0B,SAAS,EAAE6B,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAE5B,UAAU,CAAC6C,cAAc,EAAE7C,UAAU,CAAC8C,MAAM,EAAEZ,iBAAiB,EAAEpD,GAAG,EAAEkB,UAAU,CAAC;EACtJ+C,gBAAgB,CAACjD,SAAS,EAAEE,UAAU,CAACgD,WAAW,EAAEjB,QAAQ,EAAEjD,GAAG,EAAEkB,UAAU,CAAC;EAC9E,IAAIiD,UAAU,GAAGjD,UAAU,CAACiD,UAAU;EACtC,IAAIC,YAAY,GAAG9G,qBAAqB,CAAC0D,SAAS,CAACsB,GAAG,CAAC,cAAc,CAAC,EAAE6B,UAAU,CAAC;EACnFE,iBAAiB,CAACrD,SAAS,EAAEmD,UAAU,EAAExB,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAEsB,YAAY,EAAErB,cAAc,EAAE7B,UAAU,CAACoD,cAAc,EAAEpD,UAAU,CAAC6C,cAAc,EAAE7C,UAAU,CAACqD,eAAe,EAAEvE,GAAG,EAAEkB,UAAU,CAAC;EAC3M,OAAOA,UAAU;AACnB;AACA;AACA,SAAS2C,gBAAgBA,CAAC7C,SAAS,EAAE6B,YAAY,EAAEF,MAAM,EAAE3C,GAAG,EAAEwE,gBAAgB,EAAE;EAChF,IAAI9D,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAI+D,kBAAkB,GAAGzD,SAAS,CAACsB,GAAG,CAAC,oBAAoB,CAAC;EAC5D,IAAIoC,SAAS,GAAG1E,GAAG,CAACM,QAAQ,CAACqE,YAAY,CAAC3E,GAAG,CAACM,QAAQ,CAACX,WAAW,CAAC,CAAC,CAAC;EACrE,IAAIiF,MAAM,GAAGF,SAAS,CAACG,aAAa,CAACH,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC,CAAC;EAC9D,IAAIC,SAAS,GAAG,CAAC,GAAG,EAAEpC,MAAM,CAACjC,QAAQ,CAACvC,EAAE,CAAC,IAAI,CAAC,CAAC;EAC/C,IAAI4F,cAAc;EAClB,IAAI7G,MAAM,CAAC8H,OAAO,CAACP,kBAAkB,CAAC,EAAE;IACtC,IAAIQ,oBAAoB,GAAG,CAACC,oBAAoB,CAACR,SAAS,EAAED,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAGG,MAAM,EAAEM,oBAAoB,CAACR,SAAS,EAAED,kBAAkB,CAAC,CAAC,CAAC,CAAC,GAAGG,MAAM,CAAC;IAC7JK,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC,IAAIA,oBAAoB,CAACE,OAAO,CAAC,CAAC;IACnFpB,cAAc,GAAGkB,oBAAoB,CAACF,SAAS,CAAC;EAClD,CAAC,MAAM,IAAIN,kBAAkB,IAAI,IAAI,EAAE;IACrCV,cAAc,GAAGmB,oBAAoB,CAACR,SAAS,EAAED,kBAAkB,CAAC,GAAGG,MAAM;EAC/E,CAAC,MAAM,IAAI/B,YAAY,EAAE;IACvBkB,cAAc,GAAG/D,GAAG,CAACO,cAAc,CAACG,QAAQ,CAACtC,KAAK,CAAC,CAAC2G,SAAS,CAAC,GAAGH,MAAM;EACzE,CAAC,MAAM;IACLb,cAAc,GAAGpB,MAAM,CAACjC,QAAQ,CAACvC,EAAE,CAAC;EACtC;EACAqG,gBAAgB,CAACT,cAAc,GAAGA,cAAc;EAChD,IAAIlB,YAAY,EAAE;IAChB2B,gBAAgB,CAACD,eAAe,GAAG5B,MAAM,CAACjC,QAAQ,CAACvC,EAAE,CAAC;EACxD;EACA;EACA;EACA,IAAIiH,OAAO,GAAG1E,QAAQ,CAACxC,EAAE,KAAK,GAAG;EACjC,IAAImH,SAAS,GAAGX,SAAS,CAACY,OAAO;EACjCd,gBAAgB,CAACR,MAAM,GAAGoB,OAAO,IAAI,CAACC,SAAS,IAAI,CAACD,OAAO,IAAIC,SAAS,GAAGtB,cAAc,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACvI;AACA,SAASmB,oBAAoBA,CAACK,IAAI,EAAEC,KAAK,EAAE;EACzC,OAAOD,IAAI,CAACV,aAAa,CAACU,IAAI,CAACT,WAAW,CAACS,IAAI,CAACE,KAAK,CAACC,KAAK,CAACF,KAAK,CAAC,CAAC,CAAC;AACtE;AACA;AACA,SAAS1B,iBAAiBA,CAAC1E,IAAI,EAAE0B,SAAS,EAAE6B,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAEiB,cAAc,EAAEC,MAAM,EAAEZ,iBAAiB,EAAEpD,GAAG,EAAEwE,gBAAgB,EAAE;EAC9I,IAAI9D,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAIC,WAAW,GAAGX,GAAG,CAACW,WAAW;EACjC,IAAIgF,YAAY,GAAGzC,IAAI,CAAC0C,GAAG,CAACjD,MAAM,CAAChC,WAAW,CAACxC,EAAE,CAAC,CAAC;EACnD,IAAIgG,UAAU,GAAG/E,IAAI,CAACmE,aAAa,CAACzC,SAAS,EAAE,YAAY,CAAC;EAC5D,IAAI+E,gBAAgB;EACpB,IAAI3I,MAAM,CAAC8H,OAAO,CAACb,UAAU,CAAC,EAAE;IAC9B0B,gBAAgB,GAAG1B,UAAU,CAAC2B,KAAK,CAAC,CAAC;EACvC,CAAC,MAAM;IACL,IAAI3B,UAAU,IAAI,IAAI,EAAE;MACtB;MACA0B,gBAAgB,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IACrC,CAAC,MAAM;MACLA,gBAAgB,GAAG,CAAC1B,UAAU,EAAEA,UAAU,CAAC;IAC7C;EACF;EACA;EACA;EACA;EACA;EACA0B,gBAAgB,CAAClF,WAAW,CAACvC,KAAK,CAAC,GAAGb,YAAY,CAACsI,gBAAgB,CAAClF,WAAW,CAACvC,KAAK,CAAC,EAAEuH,YAAY,CAAC;EACrGE,gBAAgB,CAACnF,QAAQ,CAACtC,KAAK,CAAC,GAAGb,YAAY,CAACsI,gBAAgB,CAACnF,QAAQ,CAACtC,KAAK,CAAC,EAAEyE,YAAY,GAAG8C,YAAY,GAAGzC,IAAI,CAAC0C,GAAG,CAAC7B,cAAc,CAAC,CAAC;EACzIS,gBAAgB,CAACL,UAAU,GAAG0B,gBAAgB;EAC9C;EACA,IAAI3B,WAAW,GAAGM,gBAAgB,CAACN,WAAW,GAAG,CAAC2B,gBAAgB,CAAC,CAAC,CAAC,GAAGzC,iBAAiB,EAAEyC,gBAAgB,CAAC,CAAC,CAAC,GAAGzC,iBAAiB,CAAC;EACnI;EACAc,WAAW,CAACxD,QAAQ,CAACtC,KAAK,CAAC,IAAI,CAAC4B,GAAG,CAACJ,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIoE,MAAM;AACrE;AACA,SAASC,gBAAgBA,CAACjD,SAAS,EAAEkD,WAAW,EAAEjB,QAAQ,EAAEjD,GAAG,EAAEwE,gBAAgB,EAAE;EACjF;EACA;EACA;EACA,IAAIF,cAAc,GAAGtD,SAAS,CAACsB,GAAG,CAACtE,sBAAsB,CAAC,IAAI,CAAC;EAC/D,IAAIsG,cAAc,EAAE;IAClBhG,gBAAgB,CAACyH,IAAI,CAAC;MACpBC,MAAM,EAAE9B,WAAW,CAAC,CAAC,CAAC;MACtB+B,MAAM,EAAE/B,WAAW,CAAC,CAAC,CAAC;MACtBjB,QAAQ,EAAEA;IACZ,CAAC,CAAC;IACF3E,gBAAgB,CAAC4H,eAAe,CAAC,CAAC;IAClC5B,cAAc,IAAIhG,gBAAgB,CAAC6H,YAAY,CAAC,CAAC;IACjD7B,cAAc,IAAIJ,WAAW,CAAClE,GAAG,CAACU,QAAQ,CAACtC,KAAK,CAAC;EACnD;EACAoG,gBAAgB,CAACF,cAAc,GAAGA,cAAc,IAAI,CAAC;AACvD;AACA,SAASD,iBAAiBA,CAACrD,SAAS,EAAEmD,UAAU,EAAExB,MAAM,EAAEE,YAAY,EAAEC,UAAU,EAAEsB,YAAY,EAAErB,cAAc,EAAEuB,cAAc,EAAEP,cAAc,EAAEQ,eAAe,EAAEvE,GAAG,EAAEwE,gBAAgB,EAAE;EACxL,IAAI7D,WAAW,GAAGX,GAAG,CAACW,WAAW;EACjC,IAAID,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAIsD,MAAM,GAAGQ,gBAAgB,CAACR,MAAM;EACpC,IAAIoC,UAAU,GAAGlD,IAAI,CAACmD,GAAG,CAAClC,UAAU,CAACzD,QAAQ,CAACtC,KAAK,CAAC,GAAGkG,cAAc,EAAE,CAAC,CAAC;EACzE,IAAIgC,OAAO,GAAGF,UAAU;EACxB;EACA;EACA;EACA,IAAIvD,YAAY,EAAE;IAChB,IAAI0D,iBAAiB,GAAGrD,IAAI,CAAC0C,GAAG,CAAC7B,cAAc,CAAC;IAChD,IAAIyC,YAAY,GAAGtJ,MAAM,CAACuJ,QAAQ,CAACzF,SAAS,CAACsB,GAAG,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC,GAAG,EAAE;IAC7E,IAAIoE,SAAS,GAAG,KAAK;IACrB,IAAIF,YAAY,CAACG,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,CAACI,MAAM,GAAG,CAAC,EAAE;MAC7DF,SAAS,GAAG,IAAI;MAChBF,YAAY,GAAGA,YAAY,CAACV,KAAK,CAAC,CAAC,EAAEU,YAAY,CAACI,MAAM,GAAG,CAAC,CAAC;IAC/D;IACA,IAAIC,mBAAmB,GAAGtJ,YAAY,CAACiJ,YAAY,EAAErC,UAAU,CAACzD,QAAQ,CAACtC,KAAK,CAAC,CAAC;IAChF,IAAI0I,cAAc,GAAG5D,IAAI,CAACmD,GAAG,CAACD,UAAU,GAAGS,mBAAmB,GAAG,CAAC,EAAE,CAAC,CAAC;IACtE;IACA;IACA,IAAIE,MAAM,GAAGL,SAAS,GAAG,CAAC,GAAGG,mBAAmB,GAAG,CAAC;IACpD;IACA;IACA,IAAIG,eAAe,GAAGxJ,SAAS,CAACqF,YAAY,CAAC;IAC7C,IAAIoE,WAAW,GAAGD,eAAe,GAAGnE,YAAY,GAAGqE,UAAU,CAAC,CAACX,iBAAiB,GAAGQ,MAAM,IAAID,cAAc,CAAC;IAC5G;IACA;IACA,IAAIK,KAAK,GAAGZ,iBAAiB,GAAGU,WAAW,GAAGb,UAAU;IACxDS,mBAAmB,GAAGM,KAAK,GAAG,CAAC,IAAIT,SAAS,GAAGO,WAAW,GAAG/D,IAAI,CAACmD,GAAG,CAACY,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1FH,cAAc,GAAGV,UAAU,GAAGS,mBAAmB,GAAG,CAAC;IACrDE,MAAM,GAAGL,SAAS,GAAG,CAAC,GAAGG,mBAAmB,GAAG,CAAC;IAChD;IACA,IAAI,CAACG,eAAe,IAAInE,YAAY,KAAK,OAAO,EAAE;MAChDoE,WAAW,GAAG1C,eAAe,GAAG2C,UAAU,CAAC,CAAChE,IAAI,CAAC0C,GAAG,CAACrB,eAAe,CAAC,GAAGwC,MAAM,IAAID,cAAc,CAAC,GAAG,CAAC;IACvG;IACAR,OAAO,GAAGW,WAAW,GAAGH,cAAc,GAAGC,MAAM;IAC/CvC,gBAAgB,CAACyC,WAAW,GAAGA,WAAW;IAC1CzC,gBAAgB,CAACgC,YAAY,GAAGK,mBAAmB;EACrD;EACA,IAAIO,OAAO,GAAGpD,MAAM,IAAIsC,OAAO,GAAG,CAAC,CAAC;EACpC,IAAIe,YAAY,GAAG7C,gBAAgB,CAAC6C,YAAY,GAAG,EAAE;EACrDA,YAAY,CAAC1G,WAAW,CAACvC,KAAK,CAAC,GAAGuE,MAAM,CAAChC,WAAW,CAACxC,EAAE,CAAC,GAAG,CAAC;EAC5DkJ,YAAY,CAAC3G,QAAQ,CAACtC,KAAK,CAAC,GAAG2E,cAAc,KAAK,OAAO,GAAGqE,OAAO,GAAGrE,cAAc,KAAK,KAAK,GAAGgB,cAAc,GAAGqD,OAAO,GAAGrD,cAAc,GAAG,CAAC,CAAC,CAAC;EAChJ,IAAIK,YAAY,EAAE;IAChBiD,YAAY,CAAC,CAAC,CAAC,IAAIjD,YAAY,CAAC,CAAC,CAAC;IAClCiD,YAAY,CAAC,CAAC,CAAC,IAAIjD,YAAY,CAAC,CAAC,CAAC;EACpC;EACA,IAAIkD,cAAc,GAAG9C,gBAAgB,CAAC8C,cAAc,GAAG,EAAE;EACzDA,cAAc,CAAC3G,WAAW,CAACvC,KAAK,CAAC,GAAGuE,MAAM,CAAChC,WAAW,CAACzC,EAAE,CAAC;EAC1DoJ,cAAc,CAAC5G,QAAQ,CAACtC,KAAK,CAAC,GAAGuE,MAAM,CAACjC,QAAQ,CAACxC,EAAE,CAAC;EACpD,IAAIqJ,YAAY,GAAG/C,gBAAgB,CAAC+C,YAAY,GAAGrK,MAAM,CAACsK,MAAM,CAAC,CAAC,CAAC,EAAE7E,MAAM,CAAC;EAC5E4E,YAAY,CAAC7G,QAAQ,CAACvC,EAAE,CAAC,GAAG6F,MAAM,GAAGd,IAAI,CAACmD,GAAG,CAACnD,IAAI,CAAC0C,GAAG,CAACjD,MAAM,CAACjC,QAAQ,CAACvC,EAAE,CAAC,CAAC,EAAE+E,IAAI,CAAC0C,GAAG,CAACyB,YAAY,CAAC3G,QAAQ,CAACtC,KAAK,CAAC,GAAGgJ,OAAO,CAAC,CAAC;EAC9HG,YAAY,CAAC5G,WAAW,CAACxC,EAAE,CAAC,GAAGwE,MAAM,CAAChC,WAAW,CAACxC,EAAE,CAAC;EACrD,IAAIsJ,SAAS,GAAGjD,gBAAgB,CAACiD,SAAS,GAAG,CAAC,CAAC;EAC/C;EACAA,SAAS,CAAC9G,WAAW,CAACzC,EAAE,CAAC,GAAG,CAACyE,MAAM,CAAChC,WAAW,CAACzC,EAAE,CAAC;EACnDuJ,SAAS,CAAC9G,WAAW,CAACxC,EAAE,CAAC,GAAG6B,GAAG,CAACC,MAAM,CAACU,WAAW,CAACxC,EAAE,CAAC;EACtDsJ,SAAS,CAAC/G,QAAQ,CAACxC,EAAE,CAAC,GAAG,CAAC;EAC1BuJ,SAAS,CAAC/G,QAAQ,CAACvC,EAAE,CAAC,GAAGwE,MAAM,CAACjC,QAAQ,CAACvC,EAAE,CAAC;AAC9C;AACA,SAASuJ,UAAUA,CAACxG,UAAU,EAAE;EAC9B,IAAIkC,iBAAiB,GAAGlC,UAAU,CAACkC,iBAAiB;EACpD,IAAIuE,IAAI,GAAGtK,YAAY;EACvB;EACA6D,UAAU,CAACoC,UAAU,EAAE,CAACF,iBAAiB,GAAG,CAAC,EAAE,CAACA,iBAAiB,GAAG,CAAC,EAAEA,iBAAiB,EAAEA,iBAAiB,CAAC;EAC5GuE,IAAI,CAAC5B,IAAI,CAAC;IACR6B,OAAO,EAAE;EACX,CAAC,CAAC;EACFD,IAAI,CAAC9I,IAAI,KAAK,OAAO,IAAI8I,IAAI,CAACE,QAAQ,CAAC;IACrCC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,OAAOH,IAAI;AACb;AACA,SAASI,2BAA2BA,CAAC3G,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE8G,QAAQ,EAAE;EACnE,IAAIC,MAAM,GAAG7G,GAAG,CAAC8G,iBAAiB;EAClC,IAAI/D,UAAU,GAAGjD,UAAU,CAACiD,UAAU;EACtC,IAAIG,cAAc,GAAGpD,UAAU,CAACoD,cAAc;EAC9C,IAAI+C,YAAY,GAAGnG,UAAU,CAACmG,YAAY;EAC1C,IAAI3G,QAAQ,GAAGV,GAAG,CAACU,QAAQ;EAC3B,IAAIuG,WAAW,GAAG/F,UAAU,CAAC+F,WAAW,IAAI,CAAC;EAC7C,IAAI7I,KAAK,GAAG,CAAC;EACb,IAAI+J,IAAI,GAAGhE,UAAU,CAACnE,GAAG,CAACU,QAAQ,CAACtC,KAAK,CAAC,GAAGkG,cAAc,GAAGpD,UAAU,CAACsF,YAAY,GAAG,CAAC;EACxF4B,QAAQ,CAAChH,GAAG,EAAE,UAAUuG,IAAI,EAAE;IAC5BA,IAAI,CAACU,yBAAyB,GAAGjK,KAAK;IACtCuJ,IAAI,CAACW,sBAAsB,GAAGrB,WAAW;IACzC,IAAI7I,KAAK,GAAG6I,WAAW,EAAE;MACvBsB,UAAU,CAACZ,IAAI,EAAE,IAAI,EAAEa,UAAU,CAACpK,KAAK,CAAC,EAAE8C,UAAU,EAAE8G,QAAQ,CAAC;IACjE,CAAC,MAAM;MACLO,UAAU,CAACZ,IAAI,EAAE,IAAI,EAAE;QACrB3B,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE;MACV,CAAC,EAAE/E,UAAU,EAAE8G,QAAQ,EAAE,YAAY;QACnCC,MAAM,CAACrG,MAAM,CAAC+F,IAAI,CAAC;MACrB,CAAC,CAAC;IACJ;IACA;IACAvJ,KAAK,EAAE;EACT,CAAC,CAAC;EACF,OAAOA,KAAK,GAAG6I,WAAW,EAAE7I,KAAK,EAAE,EAAE;IACnC,IAAIuJ,IAAI,GAAGD,UAAU,CAACxG,UAAU,CAAC;IACjCyG,IAAI,CAACU,yBAAyB,GAAGjK,KAAK;IACtCuJ,IAAI,CAACW,sBAAsB,GAAGrB,WAAW;IACzCgB,MAAM,CAACpH,GAAG,CAAC8G,IAAI,CAAC;IAChB,IAAIc,MAAM,GAAGD,UAAU,CAACpK,KAAK,CAAC;IAC9BmK,UAAU,CAACZ,IAAI,EAAE;MACfnH,CAAC,EAAEiI,MAAM,CAACjI,CAAC;MACXC,CAAC,EAAEgI,MAAM,CAAChI,CAAC;MACXuF,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACV,CAAC,EAAE;MACDD,MAAM,EAAEyC,MAAM,CAACzC,MAAM;MACrBC,MAAM,EAAEwC,MAAM,CAACxC,MAAM;MACrBhD,QAAQ,EAAEwF,MAAM,CAACxF;IACnB,CAAC,EAAE/B,UAAU,EAAE8G,QAAQ,CAAC;EAC1B;EACA,SAASQ,UAAUA,CAACpK,KAAK,EAAE;IACzB,IAAIsK,QAAQ,GAAGrB,YAAY,CAACvB,KAAK,CAAC,CAAC;IACnC;IACA;IACA,IAAI9B,MAAM,GAAG9C,UAAU,CAAC8C,MAAM;IAC9B,IAAI2E,CAAC,GAAGvK,KAAK;IACb,IAAI8C,UAAU,CAACuC,qBAAqB,KAAK,OAAO,GAAGO,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC,EAAE;MAC1E2E,CAAC,GAAG1B,WAAW,GAAG,CAAC,GAAG7I,KAAK;IAC7B;IACAsK,QAAQ,CAAChI,QAAQ,CAACtC,KAAK,CAAC,GAAG+J,IAAI,IAAIQ,CAAC,GAAG1B,WAAW,GAAG,CAAC,GAAG,GAAG,CAAC,GAAGI,YAAY,CAAC3G,QAAQ,CAACtC,KAAK,CAAC;IAC5F,OAAO;MACLoC,CAAC,EAAEkI,QAAQ,CAAC,CAAC,CAAC;MACdjI,CAAC,EAAEiI,QAAQ,CAAC,CAAC,CAAC;MACd1C,MAAM,EAAE9E,UAAU,CAACgD,WAAW,CAAC,CAAC,CAAC;MACjC+B,MAAM,EAAE/E,UAAU,CAACgD,WAAW,CAAC,CAAC,CAAC;MACjCjB,QAAQ,EAAE/B,UAAU,CAAC+B;IACvB,CAAC;EACH;AACF;AACA,SAAS2F,0BAA0BA,CAACxH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE8G,QAAQ,EAAE;EAClE,IAAIC,MAAM,GAAG7G,GAAG,CAAC8G,iBAAiB;EAClC,IAAIW,QAAQ,GAAGzH,GAAG,CAAC0H,mBAAmB;EACtC,IAAI,CAACD,QAAQ,EAAE;IACbA,QAAQ,GAAGzH,GAAG,CAAC0H,mBAAmB,GAAGpB,UAAU,CAACxG,UAAU,CAAC;IAC3D+G,MAAM,CAACpH,GAAG,CAACgI,QAAQ,CAAC;IACpBN,UAAU,CAACM,QAAQ,EAAE;MACnBrI,CAAC,EAAEU,UAAU,CAACmG,YAAY,CAAC,CAAC,CAAC;MAC7B5G,CAAC,EAAES,UAAU,CAACmG,YAAY,CAAC,CAAC,CAAC;MAC7BrB,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACThD,QAAQ,EAAE/B,UAAU,CAAC+B;IACvB,CAAC,EAAE;MACD+C,MAAM,EAAE9E,UAAU,CAACgD,WAAW,CAAC,CAAC,CAAC;MACjC+B,MAAM,EAAE/E,UAAU,CAACgD,WAAW,CAAC,CAAC;IAClC,CAAC,EAAEhD,UAAU,EAAE8G,QAAQ,CAAC;EAC1B,CAAC,MAAM;IACLO,UAAU,CAACM,QAAQ,EAAE,IAAI,EAAE;MACzBrI,CAAC,EAAEU,UAAU,CAACmG,YAAY,CAAC,CAAC,CAAC;MAC7B5G,CAAC,EAAES,UAAU,CAACmG,YAAY,CAAC,CAAC,CAAC;MAC7BrB,MAAM,EAAE9E,UAAU,CAACgD,WAAW,CAAC,CAAC,CAAC;MACjC+B,MAAM,EAAE/E,UAAU,CAACgD,WAAW,CAAC,CAAC,CAAC;MACjCjB,QAAQ,EAAE/B,UAAU,CAAC+B;IACvB,CAAC,EAAE/B,UAAU,EAAE8G,QAAQ,CAAC;EAC1B;AACF;AACA;AACA,SAASe,qBAAqBA,CAAC3H,GAAG,EAAEF,UAAU,EAAE8G,QAAQ,EAAE;EACxD,IAAIgB,SAAS,GAAG9L,MAAM,CAACsK,MAAM,CAAC,CAAC,CAAC,EAAEtG,UAAU,CAACqG,YAAY,CAAC;EAC1D,IAAI0B,OAAO,GAAG7H,GAAG,CAAC8H,kBAAkB;EACpC,IAAI,CAACD,OAAO,EAAE;IACZA,OAAO,GAAG7H,GAAG,CAAC8H,kBAAkB,GAAG,IAAI/L,OAAO,CAACgM,IAAI,CAAC;MAClDxF,EAAE,EAAE,CAAC;MACLyF,KAAK,EAAEJ,SAAS;MAChBK,MAAM,EAAE,IAAI;MACZ7F,KAAK,EAAE;QACL8F,MAAM,EAAE,aAAa;QACrBC,IAAI,EAAE,aAAa;QACnBC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IACFP,OAAO,CAACQ,eAAe,GAAG,IAAI;IAC9BrI,GAAG,CAACP,GAAG,CAACoI,OAAO,CAAC;EAClB,CAAC,MAAM;IACLV,UAAU,CAACU,OAAO,EAAE,IAAI,EAAE;MACxBG,KAAK,EAAEJ;IACT,CAAC,EAAE9H,UAAU,EAAE8G,QAAQ,CAAC;EAC1B;AACF;AACA,SAAS0B,kBAAkBA,CAACtI,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE8G,QAAQ,EAAE;EAC1D;EACA,IAAI9G,UAAU,CAAC4B,UAAU,EAAE;IACzB,IAAIT,QAAQ,GAAGjB,GAAG,CAACuI,mBAAmB;IACtC,IAAIlC,SAAS,GAAGvK,MAAM,CAACsK,MAAM,CAAC,CAAC,CAAC,EAAEtG,UAAU,CAACuG,SAAS,CAAC;IACvD,IAAI/G,QAAQ,GAAGV,GAAG,CAACU,QAAQ;IAC3B,IAAIyB,cAAc,GAAGjB,UAAU,CAACiB,cAAc;IAC9C,IAAIrB,SAAS,GAAGI,UAAU,CAACJ,SAAS;IACpC,IAAIuB,QAAQ,EAAE;MACZlF,OAAO,CAACyM,WAAW,CAACvH,QAAQ,EAAE;QAC5B+G,KAAK,EAAE3B;MACT,CAAC,EAAEtF,cAAc,EAAErB,SAAS,CAAC;IAC/B,CAAC,MAAM;MACL2G,SAAS,CAAC/G,QAAQ,CAACvC,EAAE,CAAC,GAAG,CAAC;MAC1BkE,QAAQ,GAAG,IAAIlF,OAAO,CAACgM,IAAI,CAAC;QAC1BC,KAAK,EAAE3B;MACT,CAAC,CAAC;MACFrG,GAAG,CAAC8G,iBAAiB,CAAC3F,WAAW,CAACF,QAAQ,CAAC;MAC3CjB,GAAG,CAACuI,mBAAmB,GAAGtH,QAAQ;MAClC,IAAIoG,MAAM,GAAG,CAAC,CAAC;MACfA,MAAM,CAAC/H,QAAQ,CAACvC,EAAE,CAAC,GAAG+C,UAAU,CAACuG,SAAS,CAAC/G,QAAQ,CAACvC,EAAE,CAAC;MACvDhB,OAAO,CAAC6K,QAAQ,GAAG,aAAa,GAAG,WAAW,CAAC,CAAC3F,QAAQ,EAAE;QACxD+G,KAAK,EAAEX;MACT,CAAC,EAAEtG,cAAc,EAAErB,SAAS,CAAC;IAC/B;EACF;AACF;AACA,SAASG,YAAYA,CAAC7B,IAAI,EAAE0B,SAAS,EAAE;EACrC,IAAIE,SAAS,GAAG5B,IAAI,CAAC6B,YAAY,CAACH,SAAS,CAAC;EAC5CE,SAAS,CAAC6I,uBAAuB,GAAGA,uBAAuB;EAC3D7I,SAAS,CAACqC,kBAAkB,GAAGA,kBAAkB;EACjD,OAAOrC,SAAS;AAClB;AACA,SAAS6I,uBAAuBA,CAAClC,IAAI,EAAE;EACrC;EACA,OAAO;IACLvJ,KAAK,EAAEuJ,IAAI,CAACU,yBAAyB;IACrCyB,KAAK,EAAEnC,IAAI,CAACW;EACd,CAAC;AACH;AACA,SAASjF,kBAAkBA,CAAA,EAAG;EAC5B;EACA,OAAO,IAAI,CAAC0G,WAAW,CAAC1G,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAACO,UAAU,CAAC,WAAW,CAAC;AAChF;AACA,SAASvC,SAASA,CAACjC,IAAI,EAAEY,GAAG,EAAEkB,UAAU,EAAE8G,QAAQ,EAAE;EAClD;EACA,IAAI5G,GAAG,GAAG,IAAIjE,OAAO,CAAC6M,KAAK,CAAC,CAAC;EAC7B;EACA,IAAI/B,MAAM,GAAG,IAAI9K,OAAO,CAAC6M,KAAK,CAAC,CAAC;EAChC5I,GAAG,CAACP,GAAG,CAACoH,MAAM,CAAC;EACf7G,GAAG,CAAC8G,iBAAiB,GAAGD,MAAM;EAC9BA,MAAM,CAACzH,CAAC,GAAGU,UAAU,CAACoG,cAAc,CAAC,CAAC,CAAC;EACvCW,MAAM,CAACxH,CAAC,GAAGS,UAAU,CAACoG,cAAc,CAAC,CAAC,CAAC;EACvC,IAAIpG,UAAU,CAAC2B,YAAY,EAAE;IAC3BkF,2BAA2B,CAAC3G,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;EACnD,CAAC,MAAM;IACL0H,0BAA0B,CAACxH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,CAAC;EAClD;EACA6H,qBAAqB,CAAC3H,GAAG,EAAEF,UAAU,EAAE8G,QAAQ,CAAC;EAChD0B,kBAAkB,CAACtI,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE8G,QAAQ,CAAC;EAClD5G,GAAG,CAACW,mBAAmB,GAAGD,WAAW,CAAC1C,IAAI,EAAE8B,UAAU,CAAC;EACvDE,GAAG,CAACa,qBAAqB,GAAGf,UAAU;EACtC,OAAOE,GAAG;AACZ;AACA,SAASY,SAASA,CAACZ,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE;EACvC,IAAIiB,cAAc,GAAGjB,UAAU,CAACiB,cAAc;EAC9C,IAAIrB,SAAS,GAAGI,UAAU,CAACJ,SAAS;EACpC,IAAImH,MAAM,GAAG7G,GAAG,CAAC8G,iBAAiB;EAClC/K,OAAO,CAACyM,WAAW,CAAC3B,MAAM,EAAE;IAC1BzH,CAAC,EAAEU,UAAU,CAACoG,cAAc,CAAC,CAAC,CAAC;IAC/B7G,CAAC,EAAES,UAAU,CAACoG,cAAc,CAAC,CAAC;EAChC,CAAC,EAAEnF,cAAc,EAAErB,SAAS,CAAC;EAC7B,IAAII,UAAU,CAAC2B,YAAY,EAAE;IAC3BkF,2BAA2B,CAAC3G,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;EACzD,CAAC,MAAM;IACL0H,0BAA0B,CAACxH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;EACxD;EACA6H,qBAAqB,CAAC3H,GAAG,EAAEF,UAAU,EAAE,IAAI,CAAC;EAC5CwI,kBAAkB,CAACtI,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE,IAAI,CAAC;AAChD;AACA,SAASgB,SAASA,CAAC9C,IAAI,EAAE0B,SAAS,EAAEqB,cAAc,EAAEf,GAAG,EAAE;EACvD;EACA,IAAI6I,SAAS,GAAG7I,GAAG,CAAC8H,kBAAkB;EACtCe,SAAS,IAAIA,SAAS,CAACC,iBAAiB,CAAC,CAAC;EAC1C,IAAIC,KAAK,GAAG,EAAE;EACd/B,QAAQ,CAAChH,GAAG,EAAE,UAAUuG,IAAI,EAAE;IAC5BwC,KAAK,CAACC,IAAI,CAACzC,IAAI,CAAC;EAClB,CAAC,CAAC;EACFvG,GAAG,CAAC0H,mBAAmB,IAAIqB,KAAK,CAACC,IAAI,CAAChJ,GAAG,CAAC0H,mBAAmB,CAAC;EAC9D;EACA1H,GAAG,CAACuI,mBAAmB,KAAKxH,cAAc,GAAG,IAAI,CAAC;EAClDjF,MAAM,CAACmN,IAAI,CAACF,KAAK,EAAE,UAAUxC,IAAI,EAAE;IACjCxK,OAAO,CAACmN,aAAa,CAAC3C,IAAI,EAAE;MAC1B3B,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE;IACV,CAAC,EAAE9D,cAAc,EAAErB,SAAS,EAAE,YAAY;MACxCM,GAAG,CAACmJ,MAAM,IAAInJ,GAAG,CAACmJ,MAAM,CAAC3I,MAAM,CAACR,GAAG,CAAC;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;EACFhC,IAAI,CAACkC,gBAAgB,CAACR,SAAS,EAAE,IAAI,CAAC;AACxC;AACA,SAASgB,WAAWA,CAAC1C,IAAI,EAAE8B,UAAU,EAAE;EACrC,OAAO,CAAC9B,IAAI,CAACmE,aAAa,CAACrC,UAAU,CAACJ,SAAS,EAAE,QAAQ,CAAC,IAAI,MAAM,EAAE,CAAC,CAACI,UAAU,CAAC2B,YAAY,EAAE,CAAC,CAAC3B,UAAU,CAAC4B,UAAU,CAAC,CAAC0H,IAAI,CAAC,GAAG,CAAC;AACrI;AACA,SAASpC,QAAQA,CAAChH,GAAG,EAAEqJ,EAAE,EAAEC,OAAO,EAAE;EAClC;EACAxN,MAAM,CAACmN,IAAI,CAACjJ,GAAG,CAAC8G,iBAAiB,CAACyC,QAAQ,CAAC,CAAC,EAAE,UAAUC,EAAE,EAAE;IAC1DA,EAAE,KAAKxJ,GAAG,CAAC8H,kBAAkB,IAAIuB,EAAE,CAACI,IAAI,CAACH,OAAO,EAAEE,EAAE,CAAC;EACvD,CAAC,CAAC;AACJ;AACA,SAASrC,UAAUA,CAACqC,EAAE,EAAEE,cAAc,EAAEC,cAAc,EAAE7J,UAAU,EAAE8G,QAAQ,EAAEyC,EAAE,EAAE;EAChFK,cAAc,IAAIF,EAAE,CAAC7E,IAAI,CAAC+E,cAAc,CAAC;EACzC;EACA,IAAI5J,UAAU,CAAC4B,UAAU,IAAI,CAACkF,QAAQ,EAAE;IACtC+C,cAAc,IAAIH,EAAE,CAAC7E,IAAI,CAACgF,cAAc,CAAC;EAC3C,CAAC,MAAM;IACLA,cAAc,IAAI5N,OAAO,CAAC6K,QAAQ,GAAG,aAAa,GAAG,WAAW,CAAC,CAAC4C,EAAE,EAAEG,cAAc,EAAE7J,UAAU,CAACiB,cAAc,EAAEjB,UAAU,CAACJ,SAAS,EAAE2J,EAAE,CAAC;EAC5I;AACF;AACA,SAASlJ,YAAYA,CAACH,GAAG,EAAEpB,GAAG,EAAEkB,UAAU,EAAE;EAC1C,IAAIJ,SAAS,GAAGI,UAAU,CAACJ,SAAS;EACpC,IAAIE,SAAS,GAAGE,UAAU,CAACF,SAAS;EACpC;EACA;EACA,IAAIgK,aAAa,GAAGhK,SAAS,CAACiK,QAAQ,CAAC,UAAU,CAAC;EAClD,IAAIC,aAAa,GAAGF,aAAa,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACE,YAAY,CAAC,CAAC;EACtE,IAAIC,SAAS,GAAGpK,SAAS,CAACiK,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;EACxE,IAAIE,WAAW,GAAGrK,SAAS,CAACiK,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;EAC5E,IAAIG,WAAW,GAAGtK,SAAS,CAAC4C,UAAU,CAAC,QAAQ,CAAC;EAChD,IAAI2H,KAAK,GAAGP,aAAa,CAAC1I,GAAG,CAAC,OAAO,CAAC;EACtC,IAAIkJ,SAAS,GAAGR,aAAa,CAAC1I,GAAG,CAAC,WAAW,CAAC;EAC9C,IAAIoB,UAAU,GAAGsH,aAAa,CAAC1I,GAAG,CAAC,OAAO,CAAC;EAC3C8F,QAAQ,CAAChH,GAAG,EAAE,UAAUuG,IAAI,EAAE;IAC5B,IAAIA,IAAI,YAAY9J,OAAO,EAAE;MAC3B,IAAI4N,SAAS,GAAG9D,IAAI,CAACnE,KAAK;MAC1BmE,IAAI,CAAC+D,QAAQ,CAACxO,MAAM,CAACsK,MAAM,CAAC;QAC1B;QACAmE,KAAK,EAAEF,SAAS,CAACE,KAAK;QACtBnL,CAAC,EAAEiL,SAAS,CAACjL,CAAC;QACdC,CAAC,EAAEgL,SAAS,CAAChL,CAAC;QACdP,KAAK,EAAEuL,SAAS,CAACvL,KAAK;QACtBE,MAAM,EAAEqL,SAAS,CAACrL;MACpB,CAAC,EAAEc,UAAU,CAACsC,KAAK,CAAC,CAAC;IACvB,CAAC,MAAM;MACLmE,IAAI,CAAC+D,QAAQ,CAACxK,UAAU,CAACsC,KAAK,CAAC;IACjC;IACA,IAAIoI,aAAa,GAAGjE,IAAI,CAACkE,WAAW,CAAC,UAAU,CAAC;IAChDD,aAAa,CAACpI,KAAK,GAAG0H,aAAa;IACnC,IAAIxH,UAAU,EAAE;MACd;MACAkI,aAAa,CAAC5F,MAAM,GAAG2B,IAAI,CAAC3B,MAAM,GAAG,GAAG;MACxC4F,aAAa,CAAC3F,MAAM,GAAG0B,IAAI,CAAC1B,MAAM,GAAG,GAAG;IAC1C;IACA0B,IAAI,CAACkE,WAAW,CAAC,MAAM,CAAC,CAACrI,KAAK,GAAG4H,SAAS;IAC1CzD,IAAI,CAACkE,WAAW,CAAC,QAAQ,CAAC,CAACrI,KAAK,GAAG6H,WAAW;IAC9CC,WAAW,KAAK3D,IAAI,CAACmE,MAAM,GAAGR,WAAW,CAAC;IAC1C3D,IAAI,CAAChE,EAAE,GAAGzC,UAAU,CAACyC,EAAE;EACzB,CAAC,CAAC;EACF,IAAIoI,kBAAkB,GAAG/L,GAAG,CAACU,QAAQ,CAACrC,OAAO,CAAC,EAAE6C,UAAU,CAAC6C,cAAc,GAAG,CAAC,CAAC,CAAC;EAC/E,IAAIkF,OAAO,GAAG7H,GAAG,CAAC8H,kBAAkB;EACpCD,OAAO,CAAC+C,UAAU,GAAG,IAAI;EACzBrO,aAAa,CAACsL,OAAO,EAAErL,oBAAoB,CAACoD,SAAS,CAAC,EAAE;IACtDiL,YAAY,EAAEjM,GAAG,CAAChB,WAAW;IAC7BkN,cAAc,EAAEpL,SAAS;IACzBqL,WAAW,EAAEzO,eAAe,CAACsC,GAAG,CAAChB,WAAW,CAACK,OAAO,CAAC,CAAC,EAAEyB,SAAS,CAAC;IAClEsL,YAAY,EAAElL,UAAU,CAACsC,KAAK,CAAC+F,IAAI;IACnC8C,cAAc,EAAEnL,UAAU,CAACsC,KAAK,CAAC8I,OAAO;IACxCC,sBAAsB,EAAER;EAC1B,CAAC,CAAC;EACF3O,mBAAmB,CAACgE,GAAG,EAAEmK,KAAK,EAAEC,SAAS,EAAER,aAAa,CAAC1I,GAAG,CAAC,UAAU,CAAC,CAAC;AAC3E;AACA,SAAS4E,UAAUA,CAACsF,KAAK,EAAE;EACzB,IAAIC,YAAY,GAAGvJ,IAAI,CAACwJ,KAAK,CAACF,KAAK,CAAC;EACpC;EACA,OAAOtJ,IAAI,CAAC0C,GAAG,CAAC4G,KAAK,GAAGC,YAAY,CAAC,GAAG,IAAI,GAAGA,YAAY,GAAGvJ,IAAI,CAACyJ,IAAI,CAACH,KAAK,CAAC;AAChF;AACA,eAAehO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}